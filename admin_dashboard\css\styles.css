/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    background-color: #f8fafc;
    color: #1a202c;
    line-height: 1.6;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Notifications */
.notifications {
    position: relative;
}

.notification-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.notification-count {
    position: absolute;
    top: -5px;
    left: -5px;
    background: #e53e3e;
    color: white;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 20px;
    text-align: center;
}

.notifications-dropdown {
    position: absolute;
    top: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 350px;
    max-height: 400px;
    overflow: hidden;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.notifications-dropdown.show {
    opacity: 1;
    visibility: visible;
}

.notifications-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header h3 {
    color: #2d3748;
    margin: 0;
}

.notifications-header button {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 0.875rem;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

/* User Profile */
.user-profile {
    position: relative;
}

.profile-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
}

.profile-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.profile-img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.profile-dropdown {
    position: absolute;
    top: calc(100% + 10px);
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    overflow: hidden;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.profile-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.profile-dropdown a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #4a5568;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.profile-dropdown a:hover {
    background-color: #f7fafc;
}

.profile-dropdown a i {
    width: 16px;
    color: #718096;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    margin-top: 80px;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: white;
    border-left: 1px solid #e2e8f0;
    position: fixed;
    top: 80px;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    z-index: 900;
}

.sidebar-nav {
    padding: 2rem 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 2rem;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-item.active .nav-link {
    background-color: #edf2f7;
    color: #667eea;
}

.nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #667eea;
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-right: 280px;
    padding: 2rem;
    position: relative;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e2e8f0;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.section-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.section-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.cars { background: linear-gradient(135deg, #667eea, #764ba2); }
.stat-icon.bookings { background: linear-gradient(135deg, #f093fb, #f5576c); }
.stat-icon.revenue { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.stat-icon.users { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.stat-info h3 {
    font-size: 2.25rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #718096;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.stat-change.positive {
    background: #f0fff4;
    color: #38a169;
}

.stat-change.negative {
    background: #fed7d7;
    color: #e53e3e;
}

/* Charts */
.charts-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.chart-container {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.chart-header select {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
}

/* Recent Activities */
.recent-activities {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.recent-activities h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: #f7fafc;
    transition: all 0.2s ease;
}

.activity-item:hover {
    background: #edf2f7;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.activity-icon.booking { background: #667eea; }
.activity-icon.payment { background: #38a169; }
.activity-icon.review { background: #ed8936; }
.activity-icon.user { background: #9f7aea; }

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-content p {
    font-size: 0.875rem;
    color: #718096;
}

.activity-time {
    font-size: 0.75rem;
    color: #a0aec0;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #edf2f7;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Filters Row */
.filters-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    font-size: 0.875rem;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
}

.filters-row select {
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    color: #4a5568;
    min-width: 150px;
}

.filters-row input[type="date"] {
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    color: #4a5568;
}

/* Tables */
.table-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 2rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f7fafc;
    padding: 1rem 1.5rem;
    text-align: right;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.875rem;
}

.data-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    color: #4a5568;
    vertical-align: middle;
}

.data-table tr:hover {
    background: #f7fafc;
}

.table-img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-available { background: #f0fff4; color: #38a169; }
.status-rented { background: #fef5e7; color: #d69e2e; }
.status-maintenance { background: #fed7d7; color: #e53e3e; }
.status-pending { background: #e6fffa; color: #319795; }
.status-confirmed { background: #ebf4ff; color: #3182ce; }
.status-completed { background: #f0fff4; color: #38a169; }
.status-cancelled { background: #fed7d7; color: #e53e3e; }
.status-active { background: #f0fff4; color: #38a169; }
.status-inactive { background: #f7fafc; color: #718096; }
.status-verified { background: #f0fff4; color: #38a169; }
.status-rejected { background: #fed7d7; color: #e53e3e; }

/* Rating Stars */
.rating-stars {
    display: flex;
    gap: 0.125rem;
    font-size: 0.875rem;
}

.rating-stars .star {
    color: #ffd700;
}

.rating-stars .star.empty {
    color: #e2e8f0;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: scale(1.1);
}

.action-btn.edit {
    background: #ebf8ff;
    color: #3182ce;
}

.action-btn.delete {
    background: #fed7d7;
    color: #e53e3e;
}

.action-btn.view {
    background: #f0fff4;
    color: #38a169;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f7fafc;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Reviews Grid */
.reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.review-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.review-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.review-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.review-user img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.review-user-info h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.review-user-info p {
    font-size: 0.75rem;
    color: #718096;
}

.review-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.review-content {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.review-car {
    font-size: 0.875rem;
    color: #667eea;
    font-weight: 500;
}

/* Locations Grid */
.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.location-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.location-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.location-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.location-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
}

.location-type {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    background: #ebf8ff;
    color: #3182ce;
}

.location-address {
    color: #718096;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.location-coordinates {
    font-size: 0.875rem;
    color: #a0aec0;
    margin-bottom: 1rem;
}

.location-actions {
    display: flex;
    gap: 0.5rem;
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.report-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
}

.report-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.report-card canvas {
    margin-bottom: 1.5rem;
}

/* Settings */
.settings-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.tabs-nav {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    color: #718096;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tab-content {
    padding: 2rem;
    display: none;
}

.tab-content.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90%;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #a0aec0;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f7fafc;
    color: #718096;
}

.modal-body {
    padding: 2rem;
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Date Range Picker */
.date-range-picker {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.date-range-picker input {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
}

.date-range-picker span {
    color: #718096;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        margin-right: 250px;
    }
    
    .sidebar {
        width: 250px;
    }
    
    .charts-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .header-container {
        padding: 0 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-row {
        flex-direction: column;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .reviews-grid {
        grid-template-columns: 1fr;
    }
    
    .locations-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .tabs-nav {
        flex-wrap: wrap;
    }
    
    .data-table {
        font-size: 0.75rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 480px) {
    .header-actions {
        gap: 0.75rem;
    }
    
    .logo-text {
        display: none;
    }
    
    .profile-btn span {
        display: none;
    }
    
    .main-content {
        padding: 0.5rem;
    }
    
    .stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .modal-body,
    .tab-content {
        padding: 1rem;
    }
    
    .section-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Print Styles */
@media print {
    .sidebar,
    .header,
    .modal,
    .loading-overlay {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
    }
    
    .dashboard-container {
        margin-top: 0;
    }
    
    .section-actions {
        display: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body.dark-mode {
        background-color: #1a202c;
        color: #e2e8f0;
    }
    
    .dark-mode .sidebar,
    .dark-mode .table-container,
    .dark-mode .stat-card,
    .dark-mode .chart-container,
    .dark-mode .recent-activities,
    .dark-mode .modal-content {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .dark-mode .data-table th {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .dark-mode .data-table tr:hover {
        background: #4a5568;
    }
}

/* تنسيقات المكاتب */
.office-code {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.featured-badge {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    font-size: 0.7rem;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
    align-self: flex-start;
}

.office-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.office-info strong {
    font-weight: 600;
    color: #2d3748;
}

.office-info small {
    color: #718096;
    font-size: 0.85rem;
}

.emirate-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    color: white;
}

.emirate-dubai { background: #3182ce; }
.emirate-abu-dhabi { background: #38a169; }
.emirate-sharjah { background: #d69e2e; }
.emirate-ajman { background: #9f7aea; }
.emirate-ras-al-khaimah { background: #f56565; }
.emirate-fujairah { background: #38b2ac; }
.emirate-umm-al-quwain { background: #ed8936; }

.office-type-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    color: white;
}

.type-main { background: #2b6cb0; }
.type-branch { background: #38a169; }
.type-pickup-point { background: #d69e2e; }
.type-service-center { background: #9f7aea; }

.cars-count {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: center;
}

.capacity-bar {
    width: 60px;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.capacity-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78 0%, #38a169 50%, #d69e2e 80%, #f56565 100%);
    transition: width 0.3s ease;
}

/* تفاصيل المكتب */
.office-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.details-section {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 12px;
    border-right: 4px solid #3182ce;
}

.details-section h3 {
    color: #2d3748;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
}

.detail-item span {
    color: #2d3748;
    font-size: 0.95rem;
}

.services-badges, .facilities-badges {
    margin-top: 1rem;
}

.services-badges h4, .facilities-badges h4 {
    color: #2d3748;
    font-size: 1rem;
    margin-bottom: 0.75rem;
}

.service-badge, .facility-badge {
    display: inline-block;
    background: #ebf8ff;
    color: #3182ce;
    padding: 0.25rem 0.75rem;
    margin: 0.25rem 0.5rem 0.25rem 0;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid #bee3f8;
}

.working-hours {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.day-hours {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.day-name {
    font-weight: 600;
    color: #4a5568;
}

.hours.open {
    color: #38a169;
    font-weight: 500;
}

.hours.closed {
    color: #e53e3e;
    font-weight: 500;
}

/* نماذج المكاتب */
.large-modal .modal-content {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #4a5568;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3182ce;
}

/* استجابة المكاتب */
@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .working-hours {
        grid-template-columns: 1fr;
    }
    
    .cars-count {
        align-items: flex-start;
    }
    
    .capacity-bar {
        width: 80px;
    }
    
    .emirate-badge, .office-type-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.6rem;
    }
}
