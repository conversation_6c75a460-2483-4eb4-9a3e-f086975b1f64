// ملف JavaScript لإدارة الحجوزات

// متغيرات خاصة بالحجوزات
let bookingsData = [];
let filteredBookings = [];
let currentBookingsPage = 1;
let bookingsPerPage = 10;
let totalBookingsCount = 0;
let isLoadingBookings = false;

// تحميل بيانات الحجوزات
async function loadBookingsData() {
    if (isLoadingBookings) return;
    
    try {
        isLoadingBookings = true;
        showLoading();
        
        // جلب معايير التصفية الحالية
        const searchTerm = document.getElementById('bookingsSearch')?.value || '';
        const statusFilter = document.getElementById('bookingsStatus')?.value || '';
        const dateFromFilter = document.getElementById('bookingsDateFrom')?.value || '';
        const dateToFilter = document.getElementById('bookingsDateTo')?.value || '';
        
        // إعداد معاملات البحث
        const filters = {
            page: currentBookingsPage,
            limit: bookingsPerPage,
            search: searchTerm,
            status: statusFilter,
            date_from: dateFromFilter,
            date_to: dateToFilter
        };
        
        // جلب البيانات من قاعدة البيانات
        const response = await fetchBookings(filters);
        
        if (response && response.data) {
            bookingsData = response.data;
            filteredBookings = [...bookingsData];
            totalBookingsCount = response.total || 0;
            
            displayBookingsTable();
            setupBookingsPagination();
        } else {
            throw new Error('فشل في جلب بيانات الحجوزات');
        }
        
    } catch (error) {
        console.error('خطأ في تحميل الحجوزات:', error);
        
        // عرض رسالة خطأ في الجدول
        const tableBody = document.getElementById('bookingsTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center" style="padding: 3rem;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                        <p style="color: #e53e3e; font-size: 1.1rem; margin-bottom: 1rem;">حدث خطأ في تحميل الحجوزات</p>
                        <button class="btn btn-primary" onclick="loadBookingsData()">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </td>
                </tr>
            `;
        }
        
        showNotification('حدث خطأ في تحميل الحجوزات', 'error');
    } finally {
        isLoadingBookings = false;
        hideLoading();
    }
}

// إنشاء بيانات تجريبية للحجوزات
function generateMockBookingsData() {
    const customers = [
        'أحمد محمد', 'سارة أحمد', 'محمد علي', 'فاطمة خالد', 'يوسف محمد',
        'نور عبدالله', 'علي حسن', 'مريم سالم', 'خالد أحمد', 'ليلى محمد'
    ];
    
    const carNames = [
        'Toyota Camry 2024', 'Nissan Altima 2023', 'BMW 3 Series 2024',
        'Mercedes C-Class 2023', 'Audi A4 2024', 'Toyota RAV4 2024',
        'Honda Accord 2023', 'Hyundai Elantra 2024'
    ];
    
    const statuses = ['pending', 'confirmed', 'active', 'completed', 'cancelled'];
    const paymentStatuses = ['pending', 'paid', 'partially_paid', 'failed', 'refunded'];
    
    const bookings = [];
    
    for (let i = 1; i <= 156; i++) {
        const customer = customers[Math.floor(Math.random() * customers.length)];
        const carName = carNames[Math.floor(Math.random() * carNames.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
        
        const startDate = new Date();
        startDate.setDate(startDate.getDate() + Math.floor(Math.random() * 60) - 30); // من -30 إلى +30 يوم
        
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 7) + 1); // من 1 إلى 7 أيام
        
        const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        const dailyRate = Math.floor(Math.random() * 200) + 100;
        const totalAmount = dailyRate * days;
        
        bookings.push({
            id: `booking-${i}`,
            bookingNumber: `DCR${new Date().getFullYear()}${String(i).padStart(4, '0')}`,
            customer: customer,
            customerPhone: `+971-50-${Math.floor(Math.random() * 9000000) + 1000000}`,
            customerEmail: `${customer.replace(' ', '.').toLowerCase()}@example.com`,
            carName: carName,
            carId: `car-${Math.floor(Math.random() * 48) + 1}`,
            startDate: startDate,
            endDate: endDate,
            pickupLocation: getRandomLocation(),
            returnLocation: getRandomLocation(),
            dailyRate: dailyRate,
            days: days,
            subtotal: totalAmount,
            taxAmount: totalAmount * 0.05,
            totalAmount: totalAmount * 1.05,
            securityDeposit: 1000,
            status: status,
            paymentStatus: paymentStatus,
            paymentMethod: Math.random() > 0.5 ? 'credit_card' : 'bank_transfer',
            notes: generateRandomNotes(),
            createdAt: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)),
            updatedAt: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000))
        });
    }
    
    return bookings.sort((a, b) => b.createdAt - a.createdAt);
}

// الحصول على موقع عشوائي
function getRandomLocation() {
    const locations = [
        'Dubai Marina', 'Downtown Dubai', 'Jumeirah Beach Residence',
        'Dubai International Airport', 'Mall of the Emirates',
        'Dubai Mall', 'Palm Jumeirah', 'Business Bay'
    ];
    return locations[Math.floor(Math.random() * locations.length)];
}

// إنشاء ملاحظات عشوائية
function generateRandomNotes() {
    const notes = [
        'العميل يطلب توصيل السيارة للمطار',
        'حجز لمناسبة خاصة - عرس',
        'عميل VIP - تعامل مميز مطلوب',
        'طلب سيارة بديلة في حالة عدم التوفر',
        'العميل يسافر مع الأطفال',
        '',
        'رحلة عمل - فاتورة مطلوبة',
        'عميل متكرر - خصم 10%'
    ];
    return notes[Math.floor(Math.random() * notes.length)];
}

// عرض جدول الحجوزات
function displayBookingsTable() {
    const tableBody = document.getElementById('bookingsTableBody');
    const startIndex = (currentBookingsPage - 1) * bookingsPerPage;
    const endIndex = startIndex + bookingsPerPage;
    const bookingsToDisplay = filteredBookings.slice(startIndex, endIndex);
    
    tableBody.innerHTML = bookingsToDisplay.map(booking => `
        <tr>
            <td>
                <div class="booking-number">
                    <strong>${booking.bookingNumber}</strong>
                    <small class="text-muted">
                        ${booking.createdAt.toLocaleDateString('ar-AE')}
                    </small>
                </div>
            </td>
            <td>
                <div class="customer-info">
                    <strong>${booking.customer}</strong>
                    <small class="text-muted">${booking.customerPhone}</small>
                </div>
            </td>
            <td>
                <div class="car-info">
                    <strong>${booking.carName}</strong>
                    <small class="text-muted">${booking.pickupLocation}</small>
                </div>
            </td>
            <td>
                <div class="date-info">
                    ${booking.startDate.toLocaleDateString('ar-AE')}
                    <small class="text-muted">
                        ${booking.startDate.toLocaleTimeString('ar-AE', { hour: '2-digit', minute: '2-digit' })}
                    </small>
                </div>
            </td>
            <td>
                <div class="date-info">
                    ${booking.endDate.toLocaleDateString('ar-AE')}
                    <small class="text-muted">
                        ${booking.endDate.toLocaleTimeString('ar-AE', { hour: '2-digit', minute: '2-digit' })}
                    </small>
                </div>
            </td>
            <td>
                <div class="amount-info">
                    <strong>${formatCurrency(booking.totalAmount)}</strong>
                    <small class="text-muted">${booking.days} ${booking.days === 1 ? 'يوم' : 'أيام'}</small>
                </div>
            </td>
            <td>
                <span class="status-badge payment-${booking.paymentStatus}">
                    ${getPaymentStatusName(booking.paymentStatus)}
                </span>
            </td>
            <td>
                <span class="status-badge status-${booking.status}">
                    ${getBookingStatusName(booking.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewBooking('${booking.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editBooking('${booking.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="cancelBooking('${booking.id}')" title="إلغاء">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // تحديث معلومات الصفحة
    updateBookingsPageInfo();
}

// إعداد ترقيم الصفحات للحجوزات
function setupBookingsPagination() {
    const totalPages = Math.ceil(totalBookingsCount / bookingsPerPage);
    const paginationContainer = document.getElementById('bookingsPagination');
    
    // إخفاء الترقيم إذا كان هناك صفحة واحدة أو أقل
    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // زر السابق
    paginationHTML += `
        <button class="pagination-btn" ${currentBookingsPage === 1 ? 'disabled' : ''} 
                onclick="changeBookingsPage(${currentBookingsPage - 1})">
            <i class="fas fa-chevron-right"></i> السابق
        </button>
    `;
    
    // أرقام الصفحات
    const startPage = Math.max(1, currentBookingsPage - 2);
    const endPage = Math.min(totalPages, currentBookingsPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="pagination-btn" onclick="changeBookingsPage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="pagination-dots">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="pagination-btn ${i === currentBookingsPage ? 'active' : ''}" 
                    onclick="changeBookingsPage(${i})">${i}</button>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<span class="pagination-dots">...</span>`;
        }
        paginationHTML += `<button class="pagination-btn" onclick="changeBookingsPage(${totalPages})">${totalPages}</button>`;
    }
    
    // زر التالي
    paginationHTML += `
        <button class="pagination-btn" ${currentBookingsPage === totalPages ? 'disabled' : ''} 
                onclick="changeBookingsPage(${currentBookingsPage + 1})">
            التالي <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// تغيير صفحة الحجوزات
async function changeBookingsPage(page) {
    const totalPages = Math.ceil(totalBookingsCount / bookingsPerPage);
    if (page >= 1 && page <= totalPages && !isLoadingBookings) {
        currentBookingsPage = page;
        await loadBookingsData();
    }
}

// تحديث معلومات الصفحة
function updateBookingsPageInfo() {
    const startIndex = (currentBookingsPage - 1) * bookingsPerPage + 1;
    const endIndex = Math.min(currentBookingsPage * bookingsPerPage, totalBookingsCount);
    
    // عرض معلومات الصفحة
    const pageInfoElement = document.getElementById('bookingsPageInfo');
    if (pageInfoElement) {
        if (totalBookingsCount === 0) {
            pageInfoElement.textContent = 'لا توجد حجوزات';
        } else {
            pageInfoElement.textContent = `عرض ${startIndex}-${Math.min(endIndex, filteredBookings.length)} من ${totalBookingsCount} حجز`;
        }
    }
}

// فلترة الحجوزات
async function filterBookings() {
    if (isLoadingBookings) return;
    
    try {
        // إعادة تعيين الصفحة الحالية إلى الأولى عند التصفية
        currentBookingsPage = 1;
        
        // تحميل البيانات مع المعايير الجديدة
        await loadBookingsData();
        
    } catch (error) {
        console.error('خطأ في تصفية الحجوزات:', error);
        showNotification('حدث خطأ في تصفية الحجوزات', 'error');
    }
}

// عرض تفاصيل الحجز
function viewBooking(bookingId) {
    const booking = bookingsData.find(b => b.id === bookingId);
    if (!booking) return;
    
    // إنشاء مودال عرض الحجز
    const modal = createBookingViewModal(booking);
    document.body.appendChild(modal);
    modal.classList.add('show');
}

// إنشاء مودال عرض الحجز
function createBookingViewModal(booking) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'bookingViewModal';
    
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h2>تفاصيل الحجز ${booking.bookingNumber}</h2>
                <button class="close-btn" onclick="closeBookingViewModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="booking-view-content">
                    <div class="booking-status-bar" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div>
                            <span class="status-badge status-${booking.status}" style="font-size: 1rem; padding: 0.5rem 1rem;">
                                ${getBookingStatusName(booking.status)}
                            </span>
                        </div>
                        <div>
                            <span class="status-badge payment-${booking.paymentStatus}" style="font-size: 1rem; padding: 0.5rem 1rem;">
                                ${getPaymentStatusName(booking.paymentStatus)}
                            </span>
                        </div>
                    </div>
                    
                    <div class="details-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">معلومات العميل</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>الاسم:</strong> ${booking.customer}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>رقم الهاتف:</strong> ${booking.customerPhone}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>البريد الإلكتروني:</strong> ${booking.customerEmail}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">معلومات السيارة</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>السيارة:</strong> ${booking.carName}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>موقع الاستلام:</strong> ${booking.pickupLocation}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>موقع الإرجاع:</strong> ${booking.returnLocation}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">معلومات الحجز</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>رقم الحجز:</strong> ${booking.bookingNumber}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>تاريخ البداية:</strong> ${booking.startDate.toLocaleDateString('ar-AE')} ${booking.startDate.toLocaleTimeString('ar-AE', { hour: '2-digit', minute: '2-digit' })}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>تاريخ النهاية:</strong> ${booking.endDate.toLocaleDateString('ar-AE')} ${booking.endDate.toLocaleTimeString('ar-AE', { hour: '2-digit', minute: '2-digit' })}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>مدة الإيجار:</strong> ${booking.days} ${booking.days === 1 ? 'يوم' : 'أيام'}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">التفاصيل المالية</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>السعر اليومي:</strong> ${formatCurrency(booking.dailyRate)}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>المجموع الفرعي:</strong> ${formatCurrency(booking.subtotal)}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>الضريبة (5%):</strong> ${formatCurrency(booking.taxAmount)}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>المبلغ الإجمالي:</strong> ${formatCurrency(booking.totalAmount)}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>مبلغ الضمان:</strong> ${formatCurrency(booking.securityDeposit)}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>طريقة الدفع:</strong> ${getPaymentMethodName(booking.paymentMethod)}
                            </div>
                        </div>
                    </div>
                    
                    ${booking.notes ? `
                        <div class="notes-section" style="margin-top: 2rem;">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">ملاحظات</h4>
                            <p style="background: #f7fafc; padding: 1rem; border-radius: 8px; color: #4a5568;">${booking.notes}</p>
                        </div>
                    ` : ''}
                    
                    <div class="dates-section" style="margin-top: 2rem;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div class="detail-item">
                                <strong>تاريخ الإنشاء:</strong> ${booking.createdAt.toLocaleDateString('ar-AE')} ${booking.createdAt.toLocaleTimeString('ar-AE')}
                            </div>
                            <div class="detail-item">
                                <strong>آخر تحديث:</strong> ${booking.updatedAt.toLocaleDateString('ar-AE')} ${booking.updatedAt.toLocaleTimeString('ar-AE')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeBookingViewModal()">إغلاق</button>
                <button class="btn btn-primary" onclick="editBooking('${booking.id}'); closeBookingViewModal();">تعديل الحجز</button>
                <button class="btn btn-warning" onclick="printBooking('${booking.id}')">طباعة</button>
            </div>
        </div>
    `;
    
    return modal;
}

// إغلاق مودال عرض الحجز
function closeBookingViewModal() {
    const modal = document.getElementById('bookingViewModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// تعديل الحجز
function editBooking(bookingId) {
    const booking = bookingsData.find(b => b.id === bookingId);
    if (!booking) return;
    
    console.log(`تعديل الحجز: ${booking.bookingNumber}`);
    showNotification('ميزة تعديل الحجز قيد التطوير', 'info');
}

// إلغاء الحجز
async function cancelBooking(bookingId) {
    const booking = bookingsData.find(b => b.id === bookingId);
    if (!booking) return;
    
    if (booking.status === 'cancelled') {
        showNotification('الحجز ملغي بالفعل', 'warning');
        return;
    }
    
    const bookingNumber = booking.booking_number || booking.bookingNumber || bookingId;
    
    if (confirm(`هل أنت متأكد من إلغاء الحجز "${bookingNumber}"؟\n\nسيتم إشعار العميل بالإلغاء.`)) {
        try {
            showLoading();
            
            // تحديث حالة الحجز في قاعدة البيانات
            const response = await updateBookingInDB(bookingId, { status: 'cancelled' });
            
            if (response) {
                hideLoading();
                
                // إعادة تحميل البيانات
                await loadBookingsData();
                showNotification('تم إلغاء الحجز بنجاح', 'success');
            } else {
                throw new Error('فشل في إلغاء الحجز');
            }
            
        } catch (error) {
            console.error('خطأ في إلغاء الحجز:', error);
            hideLoading();
            showNotification('حدث خطأ في إلغاء الحجز', 'error');
        }
    }
}

// تأكيد الحجز
async function confirmBooking(bookingId) {
    const booking = bookingsData.find(b => b.id === bookingId);
    if (!booking) return;
    
    try {
        showLoading();
        
        // تحديث حالة الحجز في قاعدة البيانات
        const response = await updateBookingInDB(bookingId, { status: 'confirmed' });
        
        if (response) {
            hideLoading();
            
            // إعادة تحميل البيانات
            await loadBookingsData();
            showNotification('تم تأكيد الحجز بنجاح', 'success');
        } else {
            throw new Error('فشل في تأكيد الحجز');
        }
        
    } catch (error) {
        console.error('خطأ في تأكيد الحجز:', error);
        hideLoading();
        showNotification('حدث خطأ في تأكيد الحجز', 'error');
    }
}

// طباعة الحجز
function printBooking(bookingId) {
    const booking = bookingsData.find(b => b.id === bookingId);
    if (!booking) return;
    
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة الحجز ${booking.bookingNumber}</title>
            <style>
                body { 
                    font-family: 'Arial', sans-serif; 
                    direction: rtl; 
                    margin: 20px;
                    color: #333;
                }
                .header { 
                    text-align: center; 
                    border-bottom: 2px solid #333; 
                    padding-bottom: 20px; 
                    margin-bottom: 30px;
                }
                .booking-details { 
                    display: grid; 
                    grid-template-columns: 1fr 1fr; 
                    gap: 30px; 
                    margin-bottom: 30px;
                }
                .section { 
                    border: 1px solid #ddd; 
                    padding: 15px; 
                    border-radius: 5px;
                }
                .section h3 { 
                    margin-top: 0; 
                    color: #666; 
                    border-bottom: 1px solid #eee; 
                    padding-bottom: 10px;
                }
                .detail-row { 
                    display: flex; 
                    justify-content: space-between; 
                    margin-bottom: 8px;
                }
                .financial-summary { 
                    border: 2px solid #333; 
                    padding: 20px; 
                    margin-top: 30px;
                }
                .total { 
                    font-size: 1.2em; 
                    font-weight: bold; 
                    border-top: 2px solid #333; 
                    padding-top: 10px; 
                    margin-top: 10px;
                }
                @media print {
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تأجير السيارات في دبي</h1>
                <h2>فاتورة الحجز ${booking.bookingNumber}</h2>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-AE')}</p>
            </div>
            
            <div class="booking-details">
                <div class="section">
                    <h3>معلومات العميل</h3>
                    <div class="detail-row">
                        <span>الاسم:</span>
                        <span>${booking.customer}</span>
                    </div>
                    <div class="detail-row">
                        <span>رقم الهاتف:</span>
                        <span>${booking.customerPhone}</span>
                    </div>
                    <div class="detail-row">
                        <span>البريد الإلكتروني:</span>
                        <span>${booking.customerEmail}</span>
                    </div>
                </div>
                
                <div class="section">
                    <h3>معلومات السيارة</h3>
                    <div class="detail-row">
                        <span>السيارة:</span>
                        <span>${booking.carName}</span>
                    </div>
                    <div class="detail-row">
                        <span>موقع الاستلام:</span>
                        <span>${booking.pickupLocation}</span>
                    </div>
                    <div class="detail-row">
                        <span>موقع الإرجاع:</span>
                        <span>${booking.returnLocation}</span>
                    </div>
                </div>
                
                <div class="section">
                    <h3>تفاصيل الحجز</h3>
                    <div class="detail-row">
                        <span>تاريخ البداية:</span>
                        <span>${booking.startDate.toLocaleDateString('ar-AE')} ${booking.startDate.toLocaleTimeString('ar-AE', { hour: '2-digit', minute: '2-digit' })}</span>
                    </div>
                    <div class="detail-row">
                        <span>تاريخ النهاية:</span>
                        <span>${booking.endDate.toLocaleDateString('ar-AE')} ${booking.endDate.toLocaleTimeString('ar-AE', { hour: '2-digit', minute: '2-digit' })}</span>
                    </div>
                    <div class="detail-row">
                        <span>مدة الإيجار:</span>
                        <span>${booking.days} ${booking.days === 1 ? 'يوم' : 'أيام'}</span>
                    </div>
                </div>
                
                <div class="section">
                    <h3>حالة الحجز</h3>
                    <div class="detail-row">
                        <span>حالة الحجز:</span>
                        <span>${getBookingStatusName(booking.status)}</span>
                    </div>
                    <div class="detail-row">
                        <span>حالة الدفع:</span>
                        <span>${getPaymentStatusName(booking.paymentStatus)}</span>
                    </div>
                    <div class="detail-row">
                        <span>طريقة الدفع:</span>
                        <span>${getPaymentMethodName(booking.paymentMethod)}</span>
                    </div>
                </div>
            </div>
            
            <div class="financial-summary">
                <h3>الملخص المالي</h3>
                <div class="detail-row">
                    <span>السعر اليومي:</span>
                    <span>${formatCurrency(booking.dailyRate)}</span>
                </div>
                <div class="detail-row">
                    <span>عدد الأيام:</span>
                    <span>${booking.days}</span>
                </div>
                <div class="detail-row">
                    <span>المجموع الفرعي:</span>
                    <span>${formatCurrency(booking.subtotal)}</span>
                </div>
                <div class="detail-row">
                    <span>الضريبة (5%):</span>
                    <span>${formatCurrency(booking.taxAmount)}</span>
                </div>
                <div class="detail-row total">
                    <span>المبلغ الإجمالي:</span>
                    <span>${formatCurrency(booking.totalAmount)}</span>
                </div>
                <div class="detail-row">
                    <span>مبلغ الضمان:</span>
                    <span>${formatCurrency(booking.securityDeposit)}</span>
                </div>
            </div>
            
            ${booking.notes ? `
                <div class="section" style="margin-top: 30px;">
                    <h3>ملاحظات</h3>
                    <p>${booking.notes}</p>
                </div>
            ` : ''}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}

// الحصول على اسم حالة الحجز
function getBookingStatusName(status) {
    const statusNames = {
        'pending': 'في الانتظار',
        'confirmed': 'مؤكدة',
        'active': 'نشطة',
        'completed': 'مكتملة',
        'cancelled': 'ملغاة'
    };
    return statusNames[status] || status;
}

// الحصول على اسم حالة الدفع
function getPaymentStatusName(status) {
    const statusNames = {
        'pending': 'في الانتظار',
        'paid': 'مدفوعة',
        'partially_paid': 'مدفوعة جزئياً',
        'failed': 'فشل الدفع',
        'refunded': 'مستردة'
    };
    return statusNames[status] || status;
}

// الحصول على اسم طريقة الدفع
function getPaymentMethodName(method) {
    const methodNames = {
        'credit_card': 'بطاقة ائتمان',
        'debit_card': 'بطاقة خصم',
        'bank_transfer': 'تحويل بنكي',
        'cash': 'نقداً',
        'online_payment': 'دفع إلكتروني'
    };
    return methodNames[method] || method;
}

// تصدير بيانات الحجوزات
async function exportBookings() {
    try {
        showLoading();
        
        // جلب جميع الحجوزات من قاعدة البيانات
        const allBookings = await fetchBookings({
            page: 1,
            limit: 10000, // جلب عدد كبير للحصول على جميع البيانات
            search: '',
            status: '',
            startDate: '',
            endDate: ''
        });
        
        if (!allBookings || !allBookings.data) {
            throw new Error('فشل في جلب بيانات الحجوزات');
        }
        
        const exportData = allBookings.data.map(booking => ({
            'رقم الحجز': booking.booking_number || booking.id,
            'العميل': booking.customer_name || booking.customer,
            'رقم الهاتف': booking.customer_phone || booking.customerPhone || '',
            'البريد الإلكتروني': booking.customer_email || booking.customerEmail || '',
            'السيارة': booking.car_name || booking.carName || '',
            'تاريخ البداية': booking.start_date ? new Date(booking.start_date).toLocaleDateString('ar-AE') : '',
            'تاريخ النهاية': booking.end_date ? new Date(booking.end_date).toLocaleDateString('ar-AE') : '',
            'عدد الأيام': booking.days || '',
            'المبلغ الإجمالي': booking.total_amount || booking.totalAmount || '',
            'حالة الحجز': getBookingStatusName(booking.status),
            'حالة الدفع': getPaymentStatusName(booking.payment_status || booking.paymentStatus),
            'طريقة الدفع': getPaymentMethodName(booking.payment_method || booking.paymentMethod),
            'موقع الاستلام': booking.pickup_location || booking.pickupLocation || '',
            'موقع الإرجاع': booking.return_location || booking.returnLocation || '',
            'ملاحظات': booking.notes || '',
            'تاريخ الإنشاء': booking.created_at ? new Date(booking.created_at).toLocaleDateString('ar-AE') : ''
        }));
        
        hideLoading();
        
        const csv = convertToCSV(exportData);
        const fileName = `bookings-${formatDate(new Date())}.csv`;
        downloadCSV(csv, fileName);
        
        showNotification(`تم تصدير ${exportData.length} حجز بنجاح`, 'success');
        
    } catch (error) {
        console.error('خطأ في تصدير الحجوزات:', error);
        hideLoading();
        showNotification('حدث خطأ في تصدير البيانات', 'error');
    }
}
