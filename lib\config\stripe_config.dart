// إعداد مفاتيح Stripe - يجب تحديث هذا الملف بمفاتيحك الخاصة

class StripeConfig {
  // مفاتيح Stripe للاختبار (مؤقتة للتطوير - يج<PERSON> استبدالها)
  static const String publishableKey =
      'pk_test_51OTestKeyExample1234567890abcdefghij';
  static const String secretKey =
      'sk_test_51OTestKeyExample1234567890abcdefghij';

  // مفاتيح Stripe للإنتاج (لا تستخدمها في التطوير)
  // static const String publishableKey = 'pk_live_YOUR_LIVE_PUBLISHABLE_KEY_HERE';
  // static const String secretKey = 'sk_live_YOUR_LIVE_SECRET_KEY_HERE';

  // إعدادات إضافية
  static const String currency = 'aed'; // الدرهم الإماراتي
  static const String merchantDisplayName = 'Dubai Car Rental';
  static const String countryCode = 'AE';

  // للحصول على مفاتيح Stripe:
  // 1. سجل في https://dashboard.stripe.com/register
  // 2. اذهب إلى Developers > API keys
  // 3. انسخ Publishable key و Secret key
  // 4. استبدلهما في هذا الملف

  // إعداد Webhook (اختياري للتطوير):
  // 1. اذهب إلى Developers > Webhooks
  // 2. انشئ webhook جديد
  // 3. أضف الأحداث: payment_intent.succeeded, payment_intent.payment_failed
  // 4. استخدم الرابط: YOUR_BACKEND_URL/webhook/stripe
}

/*
بطاقات اختبار Stripe:

للنجح:
- الرقم: 4242424242424242
- التاريخ: أي تاريخ مستقبلي
- CVC: أي 3 أرقام

للفشل:
- الرقم: 4000000000000002
- التاريخ: أي تاريخ مستقبلي  
- CVC: أي 3 أرقام

يتطلب تأكيد:
- الرقم: 4000002500003155
- التاريخ: أي تاريخ مستقبلي
- CVC: أي 3 أرقام
*/
