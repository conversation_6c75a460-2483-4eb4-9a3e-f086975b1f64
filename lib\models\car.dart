class Car {
  final String id;
  final String name;
  final String brand;
  final String model;
  final int year;
  final String category;
  final double pricePerDay;
  final double rating;
  final int reviewCount;
  final List<String> images;
  final List<String> features;
  final Map<String, dynamic> specifications;
  final bool isAvailable;
  final String description;
  final String location;
  final String fuelType;
  final String transmission;
  final int seats;
  final int doors;
  final String color;
  final String plateNumber;
  final DateTime? availableFrom;
  final DateTime? availableUntil;

  Car({
    required this.id,
    required this.name,
    required this.brand,
    required this.model,
    required this.year,
    required this.category,
    required this.pricePerDay,
    required this.rating,
    required this.reviewCount,
    required this.images,
    required this.features,
    required this.specifications,
    required this.isAvailable,
    required this.description,
    required this.location,
    required this.fuelType,
    required this.transmission,
    required this.seats,
    required this.doors,
    required this.color,
    required this.plateNumber,
    this.availableFrom,
    this.availableUntil,
  });

  factory Car.fromJson(Map<String, dynamic> json) {
    return Car(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      brand: json['brand'] ?? '',
      model: json['model'] ?? '',
      year: json['year'] ?? 0,
      category: json['category'] ?? '',
      pricePerDay: (json['price_per_day'] ?? 0).toDouble(),
      rating: (json['rating'] ?? 0).toDouble(),
      reviewCount: json['review_count'] ?? 0,
      images: List<String>.from(json['images'] ?? []),
      features: List<String>.from(json['features'] ?? []),
      specifications: Map<String, dynamic>.from(json['specifications'] ?? {}),
      isAvailable: json['is_available'] ?? true,
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      fuelType: json['fuel_type'] ?? '',
      transmission: json['transmission'] ?? '',
      seats: json['seats'] ?? 0,
      doors: json['doors'] ?? 0,
      color: json['color'] ?? '',
      plateNumber: json['plate_number'] ?? '',
      availableFrom: json['available_from'] != null
          ? DateTime.parse(json['available_from'])
          : null,
      availableUntil: json['available_until'] != null
          ? DateTime.parse(json['available_until'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'model': model,
      'year': year,
      'category': category,
      'price_per_day': pricePerDay,
      'rating': rating,
      'review_count': reviewCount,
      'images': images,
      'features': features,
      'specifications': specifications,
      'is_available': isAvailable,
      'description': description,
      'location': location,
      'fuel_type': fuelType,
      'transmission': transmission,
      'seats': seats,
      'doors': doors,
      'color': color,
      'plate_number': plateNumber,
      'available_from': availableFrom?.toIso8601String(),
      'available_until': availableUntil?.toIso8601String(),
    };
  }

  Car copyWith({
    String? id,
    String? name,
    String? brand,
    String? model,
    int? year,
    String? category,
    double? pricePerDay,
    double? rating,
    int? reviewCount,
    List<String>? images,
    List<String>? features,
    Map<String, dynamic>? specifications,
    bool? isAvailable,
    String? description,
    String? location,
    String? fuelType,
    String? transmission,
    int? seats,
    int? doors,
    String? color,
    String? plateNumber,
    DateTime? availableFrom,
    DateTime? availableUntil,
  }) {
    return Car(
      id: id ?? this.id,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      year: year ?? this.year,
      category: category ?? this.category,
      pricePerDay: pricePerDay ?? this.pricePerDay,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      images: images ?? this.images,
      features: features ?? this.features,
      specifications: specifications ?? this.specifications,
      isAvailable: isAvailable ?? this.isAvailable,
      description: description ?? this.description,
      location: location ?? this.location,
      fuelType: fuelType ?? this.fuelType,
      transmission: transmission ?? this.transmission,
      seats: seats ?? this.seats,
      doors: doors ?? this.doors,
      color: color ?? this.color,
      plateNumber: plateNumber ?? this.plateNumber,
      availableFrom: availableFrom ?? this.availableFrom,
      availableUntil: availableUntil ?? this.availableUntil,
    );
  }

  @override
  String toString() {
    return 'Car(id: $id, name: $name, brand: $brand, model: $model, year: $year, pricePerDay: $pricePerDay)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Car && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Additional getters for compatibility
  double get dailyPrice => pricePerDay;
  double get weeklyPrice => pricePerDay * 6.5; // Usually cheaper for weekly
  double get monthlyPrice =>
      pricePerDay * 25; // Usually much cheaper for monthly
  double get securityDeposit => pricePerDay * 5; // Typical security deposit
  int get totalRatings => reviewCount;
  String get type => category;

  // Feature lists extracted from features list
  List<String> get generalFeatures {
    return features
        .where(
            (f) => !safetyFeatures.contains(f) && !comfortFeatures.contains(f))
        .toList();
  }

  List<String> get safetyFeatures {
    const safetyKeywords = [
      'airbag',
      'abs',
      'brake',
      'safety',
      'stability',
      'traction',
      'camera',
      'sensor'
    ];
    return features
        .where((f) =>
            safetyKeywords.any((keyword) => f.toLowerCase().contains(keyword)))
        .toList();
  }

  List<String> get comfortFeatures {
    const comfortKeywords = [
      'climate',
      'leather',
      'seat',
      'navigation',
      'bluetooth',
      'usb',
      'sound',
      'cruise'
    ];
    return features
        .where((f) =>
            comfortKeywords.any((keyword) => f.toLowerCase().contains(keyword)))
        .toList();
  }

  // Location coordinates (placeholder values - should come from database)
  double get locationLat => 25.2048; // Dubai default
  double get locationLng => 55.2708; // Dubai default

  // Location address getter
  String get locationAddress => location; // Use existing location field

  // Pickup locations (placeholder - should come from database)
  List<String> get pickupLocations => [
        'Dubai Mall',
        'Dubai International Airport',
        'Burj Al Arab',
        location, // Include the main location
      ];
}
