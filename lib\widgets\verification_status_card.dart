import 'package:flutter/material.dart';
import '../providers/document_provider.dart';
import '../utils/app_theme.dart';
import '../l10n/app_localizations.dart';

class VerificationStatusCard extends StatelessWidget {
  final VerificationStatus status;
  final String? notes;

  const VerificationStatusCard({
    super.key,
    required this.status,
    this.notes,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 24),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: _getStatusGradient(),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getStatusIcon(),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getStatusTitle(l10n),
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStatusSubtitle(l10n),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (notes != null && notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        notes!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  LinearGradient _getStatusGradient() {
    switch (status) {
      case VerificationStatus.notSubmitted:
        return LinearGradient(
          colors: [AppTheme.mediumGrey, AppTheme.mediumGrey.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case VerificationStatus.pending:
        return LinearGradient(
          colors: [Colors.orange, Colors.orange.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case VerificationStatus.verified:
        return LinearGradient(
          colors: [
            AppTheme.successGreen,
            AppTheme.successGreen.withOpacity(0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case VerificationStatus.rejected:
        return LinearGradient(
          colors: [AppTheme.errorRed, AppTheme.errorRed.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  IconData _getStatusIcon() {
    switch (status) {
      case VerificationStatus.notSubmitted:
        return Icons.upload_outlined;
      case VerificationStatus.pending:
        return Icons.hourglass_empty;
      case VerificationStatus.verified:
        return Icons.verified;
      case VerificationStatus.rejected:
        return Icons.error_outline;
    }
  }

  String _getStatusTitle(AppLocalizations l10n) {
    switch (status) {
      case VerificationStatus.notSubmitted:
        return l10n.documentsRequired;
      case VerificationStatus.pending:
        return l10n.verificationPending;
      case VerificationStatus.verified:
        return l10n.documentsVerified;
      case VerificationStatus.rejected:
        return l10n.documentsRejected;
    }
  }

  String _getStatusSubtitle(AppLocalizations l10n) {
    switch (status) {
      case VerificationStatus.notSubmitted:
        return l10n.pleaseUploadDocuments;
      case VerificationStatus.pending:
        return l10n.documentsUnderReview;
      case VerificationStatus.verified:
        return l10n.allDocumentsVerified;
      case VerificationStatus.rejected:
        return l10n.someDocumentsRejected;
    }
  }
}
