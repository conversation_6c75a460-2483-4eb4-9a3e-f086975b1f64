import 'package:flutter/material.dart';
import 'package:signature/signature.dart';
import 'package:provider/provider.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;

import '../../providers/booking_provider.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../../widgets/loading_button.dart';

class SignatureScreen extends StatefulWidget {
  final String bookingId;
  final String signatureType; // 'pickup' or 'return'
  final VoidCallback? onSignatureSaved;

  const SignatureScreen({
    Key? key,
    required this.bookingId,
    required this.signatureType,
    this.onSignatureSaved,
  }) : super(key: key);

  @override
  _SignatureScreenState createState() => _SignatureScreenState();
}

class _SignatureScreenState extends State<SignatureScreen> {
  late SignatureController _signatureController;
  bool _isLoading = false;
  bool _hasSignature = false;

  @override
  void initState() {
    super.initState();
    _signatureController = SignatureController(
      penStrokeWidth: 3,
      penColor: AppTheme.darkBlue,
      exportBackgroundColor: Colors.white,
      exportPenColor: AppTheme.darkBlue,
    );

    _signatureController.addListener(() {
      setState(() {
        _hasSignature = _signatureController.isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _signatureController.dispose();
    super.dispose();
  }

  Future<void> _saveSignature() async {
    if (_signatureController.isEmpty) {
      _showErrorSnackBar(AppLocalizations.of(context)!.pleaseSignFirst);
      return;
    }

    setState(() => _isLoading = true);

    try {
      // تحويل التوقيع إلى صورة
      final ui.Image? image = await _signatureController.toImage();
      if (image == null) {
        _showErrorSnackBar('Failed to capture signature');
        return;
      }

      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      // حفظ التوقيع
      final bookingProvider =
          Provider.of<BookingProvider>(context, listen: false);
      final success = await bookingProvider.saveSignature(
        pngBytes,
        widget.signatureType == 'pickup',
      );

      if (success && mounted) {
        _showSuccessDialog();
      } else if (mounted) {
        _showErrorSnackBar(AppLocalizations.of(context)!.signatureSaveFailed);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _clearSignature() {
    _signatureController.clear();
    setState(() {
      _hasSignature = false;
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.errorRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showSuccessDialog() {
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppTheme.successGreen.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check_circle,
            color: AppTheme.successGreen,
            size: 30,
          ),
        ),
        title: Text(
          l10n.signatureSaved,
          textAlign: TextAlign.center,
        ),
        content: Text(
          l10n.signatureSavedSuccessfully,
          textAlign: TextAlign.center,
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للشاشة السابقة
              widget.onSignatureSaved?.call();
            },
            child: Text(l10n.continue_),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          widget.signatureType == 'pickup'
              ? l10n.pickupSignature
              : l10n.returnSignature,
        ),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // تعليمات التوقيع
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.lightBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.lightBlue.withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.draw,
                  size: 40,
                  color: AppTheme.royalBlue,
                ),
                const SizedBox(height: 12),
                Text(
                  l10n.electronicSignature,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.royalBlue,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  l10n.signatureInstructions,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.mediumGrey,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // منطقة التوقيع
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _hasSignature
                      ? AppTheme.successGreen
                      : AppTheme.borderGrey,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(14),
                child: Stack(
                  children: [
                    // منطقة التوقيع
                    Signature(
                      controller: _signatureController,
                      backgroundColor: Colors.white,
                    ),

                    // خط التوقيع
                    if (!_hasSignature)
                      Positioned(
                        bottom: 80,
                        left: 40,
                        right: 40,
                        child: Column(
                          children: [
                            Container(
                              height: 2,
                              color: AppTheme.borderGrey,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              l10n.signHere,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: AppTheme.mediumGrey,
                                  ),
                            ),
                          ],
                        ),
                      ),

                    // مؤشر الحالة
                    if (_hasSignature)
                      Positioned(
                        top: 16,
                        right: 16,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: AppTheme.successGreen,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                l10n.signed,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // الأزرار
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // معلومات إضافية
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.lightGrey,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppTheme.mediumGrey,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          l10n.signatureDisclaimer,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.mediumGrey,
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // أزرار التحكم
                Row(
                  children: [
                    // زر المسح
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _hasSignature ? _clearSignature : null,
                        icon: const Icon(Icons.clear),
                        label: Text(l10n.clear),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // زر الحفظ
                    Expanded(
                      flex: 2,
                      child: LoadingButton(
                        onPressed: _hasSignature ? _saveSignature : null,
                        isLoading: _isLoading,
                        text: l10n.saveSignature,
                        icon: const Icon(Icons.save),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
