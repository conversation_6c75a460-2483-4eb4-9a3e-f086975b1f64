import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_stripe/flutter_stripe.dart' hide Card;

import '../../models/car.dart';
import '../../providers/booking_provider.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_button.dart';
import '../../widgets/payment_method_card.dart';
import '../../widgets/price_summary_card.dart';
import 'booking_confirmation_screen.dart';

class PaymentScreen extends StatefulWidget {
  final Car car;
  final Map<String, dynamic> bookingData;

  const PaymentScreen({
    Key? key,
    required this.car,
    required this.bookingData,
  }) : super(key: key);

  @override
  _PaymentScreenState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cardNumberController = TextEditingController();
  final _expiryDateController = TextEditingController();
  final _cvvController = TextEditingController();
  final _cardHolderNameController = TextEditingController();

  String _selectedPaymentMethod = 'card';
  bool _saveCard = false;
  bool _isProcessing = false;

  // بيانات البطاقة المحفوظة
  List<Map<String, String>> _savedCards = [];

  @override
  void initState() {
    super.initState();
    _loadSavedCards();
    _loadUserInfo();
  }

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    _cardHolderNameController.dispose();
    super.dispose();
  }

  void _loadSavedCards() async {
    // تحميل البطاقات المحفوظة من قاعدة البيانات
    // يمكن تنفيذ هذا لاحقاً
    setState(() {
      _savedCards = [
        {
          'id': '1',
          'last4': '4242',
          'brand': 'visa',
          'expiry': '12/25',
        },
      ];
    });
  }

  void _loadUserInfo() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user != null && user.fullName != null) {
      _cardHolderNameController.text = user.fullName!;
    }
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isProcessing = true);

    try {
      final bookingProvider =
          Provider.of<BookingProvider>(context, listen: false);

      String? paymentIntentId;

      if (_selectedPaymentMethod == 'card') {
        // معالجة الدفع بالبطاقة الائتمانية
        paymentIntentId = await _processCardPayment();
      } else if (_selectedPaymentMethod == 'apple_pay') {
        // معالجة الدفع بـ Apple Pay
        paymentIntentId = await _processApplePay();
      } else if (_selectedPaymentMethod == 'google_pay') {
        // معالجة الدفع بـ Google Pay
        paymentIntentId = await _processGooglePay();
      }

      if (paymentIntentId != null) {
        // إنشاء الحجز
        final bookingId = await bookingProvider.createBooking();

        if (bookingId != null && mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => BookingConfirmationScreen(
                bookingId: bookingId,
                car: widget.car,
              ),
            ),
          );
        } else if (mounted) {
          _showErrorDialog(AppLocalizations.of(context)!.bookingFailed);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  Future<String?> _processCardPayment() async {
    try {
      // إنشاء Payment Method
      await Stripe.instance.createPaymentMethod(
        params: PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(
            billingDetails: BillingDetails(
              name: _cardHolderNameController.text,
            ),
          ),
        ),
      );

      // تأكيد الدفع
      final confirmedPaymentIntent = await Stripe.instance.confirmPayment(
        paymentIntentClientSecret:
            'client_secret_here', // يجب الحصول عليه من الخادم
        data: PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(
            billingDetails: BillingDetails(
              name: _cardHolderNameController.text,
            ),
          ),
        ),
      );

      return confirmedPaymentIntent.id;
    } catch (e) {
      throw Exception('Payment failed: ${e.toString()}');
    }
  }

  Future<String?> _processApplePay() async {
    try {
      // تعطيل Apple Pay مؤقتاً
      throw Exception('Apple Pay not implemented yet');
    } catch (e) {
      throw Exception('Apple Pay failed: ${e.toString()}');
    }
  }

  Future<String?> _processGooglePay() async {
    try {
      // تعطيل Google Pay مؤقتاً
      throw Exception('Google Pay not implemented yet');
    } catch (e) {
      throw Exception('Google Pay failed: ${e.toString()}');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.paymentFailed),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context)!.ok),
          ),
        ],
      ),
    );
  }

  String _formatCardNumber(String value) {
    value = value.replaceAll(' ', '');
    String formatted = '';
    for (int i = 0; i < value.length; i++) {
      if (i > 0 && i % 4 == 0) {
        formatted += ' ';
      }
      formatted += value[i];
    }
    return formatted;
  }

  String _formatExpiryDate(String value) {
    value = value.replaceAll('/', '');
    if (value.length >= 2) {
      return '${value.substring(0, 2)}/${value.substring(2)}';
    }
    return value;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppTheme.lightGrey,
      appBar: AppBar(
        title: Text(l10n.payment),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // معلومات السيارة والحجز
            _buildBookingSummaryCard(l10n),

            // طرق الدفع
            _buildPaymentMethodsCard(l10n),

            // تفاصيل الدفع
            if (_selectedPaymentMethod == 'card') _buildCardDetailsCard(l10n),

            // ملخص السعر
            _buildPriceSummaryCard(l10n),

            const SizedBox(height: 100), // مساحة للشريط السفلي
          ],
        ),
      ),

      // شريط الدفع السفلي
      bottomNavigationBar: _buildPaymentBar(l10n),
    );
  }

  Widget _buildBookingSummaryCard(AppLocalizations l10n) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.bookingSummary,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    widget.car.images.first,
                    width: 60,
                    height: 45,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 45,
                        color: AppTheme.lightGrey,
                        child: Icon(
                          Icons.directions_car,
                          color: AppTheme.mediumGrey,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.car.name,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.bookingData['rental_days']} ${l10n.days}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.mediumGrey,
                            ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${widget.bookingData['total'].toStringAsFixed(0)} ${l10n.aed}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.royalBlue,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodsCard(AppLocalizations l10n) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.paymentMethod,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // البطاقات المحفوظة
            if (_savedCards.isNotEmpty) ...[
              Text(
                l10n.savedCards,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(height: 8),
              ..._savedCards.map((card) {
                return PaymentMethodCard(
                  title: '**** **** **** ${card['last4']}',
                  subtitle:
                      '${card['brand']?.toUpperCase()} • ${card['expiry']}',
                  icon: Icon(_getCardIcon(card['brand']!)),
                  isSelected: _selectedPaymentMethod == 'saved_${card['id']}',
                  onTap: () {
                    setState(() {
                      _selectedPaymentMethod = 'saved_${card['id']}';
                    });
                  },
                );
              }).toList(),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
            ],

            // بطاقة ائتمان جديدة
            PaymentMethodCard(
              title: l10n.creditDebitCard,
              subtitle: l10n.visa + ', ' + l10n.mastercard + ', ' + l10n.amex,
              icon: const Icon(Icons.credit_card),
              isSelected: _selectedPaymentMethod == 'card',
              onTap: () {
                setState(() {
                  _selectedPaymentMethod = 'card';
                });
              },
            ),

            const SizedBox(height: 8),

            // Apple Pay
            if (Theme.of(context).platform == TargetPlatform.iOS)
              PaymentMethodCard(
                title: l10n.applePay,
                subtitle: l10n.payWithApplePay,
                icon: const Icon(Icons.apple),
                isSelected: _selectedPaymentMethod == 'apple_pay',
                onTap: () {
                  setState(() {
                    _selectedPaymentMethod = 'apple_pay';
                  });
                },
              ),

            const SizedBox(height: 8),

            // Google Pay
            PaymentMethodCard(
              title: l10n.googlePay,
              subtitle: l10n.payWithGooglePay,
              icon: const Icon(Icons.account_balance_wallet),
              isSelected: _selectedPaymentMethod == 'google_pay',
              onTap: () {
                setState(() {
                  _selectedPaymentMethod = 'google_pay';
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardDetailsCard(AppLocalizations l10n) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.cardDetails,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),

              // رقم البطاقة
              CustomTextField(
                controller: _cardNumberController,
                labelText: l10n.cardNumber,
                hintText: '1234 5678 9012 3456',
                prefixIcon: const Icon(Icons.credit_card),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(16),
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    return TextEditingValue(
                      text: _formatCardNumber(newValue.text),
                      selection: TextSelection.collapsed(
                        offset: _formatCardNumber(newValue.text).length,
                      ),
                    );
                  }),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.fieldRequired;
                  }
                  if (value.replaceAll(' ', '').length < 16) {
                    return l10n.invalidCardNumber;
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              Row(
                children: [
                  // تاريخ الانتهاء
                  Expanded(
                    child: CustomTextField(
                      controller: _expiryDateController,
                      labelText: l10n.expiryDate,
                      hintText: 'MM/YY',
                      prefixIcon: const Icon(Icons.calendar_today),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                        TextInputFormatter.withFunction((oldValue, newValue) {
                          return TextEditingValue(
                            text: _formatExpiryDate(newValue.text),
                            selection: TextSelection.collapsed(
                              offset: _formatExpiryDate(newValue.text).length,
                            ),
                          );
                        }),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.fieldRequired;
                        }
                        if (value.length < 5) {
                          return l10n.invalidExpiryDate;
                        }
                        return null;
                      },
                    ),
                  ),

                  const SizedBox(width: 16),

                  // CVV
                  Expanded(
                    child: CustomTextField(
                      controller: _cvvController,
                      labelText: l10n.cvv,
                      hintText: '123',
                      prefixIcon: const Icon(Icons.lock),
                      keyboardType: TextInputType.number,
                      obscureText: true,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.fieldRequired;
                        }
                        if (value.length < 3) {
                          return l10n.invalidCvv;
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // اسم حامل البطاقة
              CustomTextField(
                controller: _cardHolderNameController,
                labelText: l10n.cardHolderName,
                prefixIcon: const Icon(Icons.person),
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.fieldRequired;
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // حفظ البطاقة
              CheckboxListTile(
                title: Text(l10n.saveCardForFuture),
                subtitle: Text(l10n.securelyStored),
                value: _saveCard,
                onChanged: (value) {
                  setState(() {
                    _saveCard = value!;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriceSummaryCard(AppLocalizations l10n) {
    return PriceSummaryCard(
      basePrice: widget.bookingData['subtotal'] ?? 0.0,
      taxes: widget.bookingData['tax'] ?? 0.0,
      serviceFee: 25.0,
      rentalDays: widget.bookingData['rental_days'] ?? 1,
      carName: widget.car.name,
      deliveryFee: widget.bookingData['delivery_fee'],
      additionalServices: widget.bookingData['additional_services'] ?? {},
    );
  }

  Widget _buildPaymentBar(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات الأمان
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.successGreen.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.security,
                    color: AppTheme.successGreen,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      l10n.securePaymentMessage,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.successGreen,
                          ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // زر الدفع
            LoadingButton(
              onPressed: _processPayment,
              isLoading: _isProcessing,
              text:
                  '${l10n.payNow} ${(widget.bookingData['total'] + widget.car.securityDeposit).toStringAsFixed(0)} ${l10n.aed}',
              icon: const Icon(Icons.payment),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCardIcon(String brand) {
    switch (brand.toLowerCase()) {
      case 'visa':
        return Icons.credit_card;
      case 'mastercard':
        return Icons.credit_card;
      case 'amex':
        return Icons.credit_card;
      default:
        return Icons.credit_card;
    }
  }
}
