-- قاعدة البيانات الكاملة لتطبيق تأجير السيارات في دبي
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- جدول المستخدمين المحدث
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  full_name TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  phone TEXT,
  email TEXT,
  date_of_birth DATE,
  nationality TEXT,
  
  -- الوثائق
  emirates_id_front_url TEXT,
  emirates_id_back_url TEXT,
  driving_license_url TEXT,
  passport_url TEXT,
  profile_image_url TEXT,
  license_image_url TEXT, -- إضافة حقل صورة الرخصة
  signature_url TEXT, -- التوقيع الإلكتروني الشخصي
  
  -- العنوان
  address TEXT,
  address_line1 TEXT,
  address_line2 TEXT,
  city TEXT DEFAULT 'Dubai',
  country TEXT DEFAULT 'UAE',
  emirate TEXT DEFAULT 'Dubai',
  postal_code TEXT,
  
  -- معلومات الطوارئ
  emergency_contact_name TEXT,
  emergency_contact_phone TEXT,
  
  -- حالة التحقق
  is_verified BOOLEAN DEFAULT false,
  is_documents_verified BOOLEAN DEFAULT false,
  is_email_verified BOOLEAN DEFAULT false,
  is_phone_verified BOOLEAN DEFAULT false,
  is_license_verified BOOLEAN DEFAULT false,
  is_profile_complete BOOLEAN DEFAULT false,
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected')),
  verification_notes TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'banned')),
  
  -- الإعدادات
  preferred_language TEXT DEFAULT 'ar' CHECK (preferred_language IN ('ar', 'en')),
  notification_enabled BOOLEAN DEFAULT true,
  email_notifications BOOLEAN DEFAULT true,
  sms_notifications BOOLEAN DEFAULT true,
  dark_mode BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  PRIMARY KEY (id)
);

-- جدول السيارات المحدث
CREATE TABLE cars (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  make TEXT NOT NULL, -- العلامة التجارية (تغيير من brand إلى make)
  brand TEXT NOT NULL, -- للتوافق مع النظام الحالي
  model TEXT NOT NULL,
  year INTEGER NOT NULL CHECK (year >= 2015 AND year <= 2025),
  category TEXT NOT NULL CHECK (category IN ('economy', 'standard', 'premium', 'luxury', 'exotic')),
  type TEXT NOT NULL CHECK (type IN ('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'pickup', 'luxury')),
  transmission TEXT NOT NULL CHECK (transmission IN ('automatic', 'manual')),
  fuel_type TEXT NOT NULL CHECK (fuel_type IN ('gasoline', 'petrol', 'diesel', 'hybrid', 'electric')),
  doors INTEGER NOT NULL CHECK (doors IN (2, 4, 5)),
  seats INTEGER NOT NULL CHECK (seats >= 2 AND seats <= 8),
  color TEXT,
  
  -- معلومات السيارة
  plate_number TEXT UNIQUE NOT NULL, -- تغيير من license_plate
  license_plate TEXT UNIQUE NOT NULL, -- للتوافق مع النظام الحالي
  vin_number TEXT UNIQUE,
  mileage INTEGER DEFAULT 0,
  fuel_capacity DECIMAL(5,2),
  engine_size TEXT,
  
  -- الأسعار
  hourly_price DECIMAL(10,2) DEFAULT 0,
  daily_rate DECIMAL(10,2) NOT NULL CHECK (daily_rate > 0), -- تغيير من daily_price
  daily_price DECIMAL(10,2) NOT NULL CHECK (daily_price > 0), -- للتوافق
  weekly_rate DECIMAL(10,2) NOT NULL CHECK (weekly_rate > 0), -- تغيير من weekly_price
  weekly_price DECIMAL(10,2) NOT NULL CHECK (weekly_price > 0), -- للتوافق
  monthly_rate DECIMAL(10,2) NOT NULL CHECK (monthly_rate > 0), -- تغيير من monthly_price
  monthly_price DECIMAL(10,2) NOT NULL CHECK (monthly_price > 0), -- للتوافق
  security_deposit DECIMAL(10,2) DEFAULT 1000,
  
  -- التقييمات
  rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
  total_ratings INTEGER DEFAULT 0,
  total_reviews INTEGER DEFAULT 0, -- إضافة للتطابق مع النموذج
  
  -- الوسائط
  images TEXT[] NOT NULL,
  image_urls TEXT[] NOT NULL, -- للتطابق مع النموذج
  thumbnail_url TEXT, -- إضافة صورة مصغرة
  video_url TEXT,
  
  -- المميزات
  features TEXT[] DEFAULT '{}',
  safety_features TEXT[] DEFAULT '{}',
  comfort_features TEXT[] DEFAULT '{}',
  
  -- الموقع
  location_lat DECIMAL(10,8) NOT NULL,
  location_lng DECIMAL(11,8) NOT NULL,
  location_address TEXT NOT NULL,
  current_location JSONB, -- موقع حالي بتفاصيل أكثر
  pickup_locations TEXT[] DEFAULT '{}',
  pickup_location TEXT, -- موقع استلام افتراضي
  return_location TEXT, -- موقع إرجاع افتراضي
  
  -- الحالة
  is_available BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  availability_status TEXT DEFAULT 'available' CHECK (availability_status IN ('available', 'rented', 'maintenance', 'unavailable')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance', 'retired')),
  
  -- التأمين والصيانة
  insurance JSONB, -- معلومات التأمين بتفصيل أكثر
  insurance_expiry DATE NOT NULL,
  registration_expiry DATE NOT NULL,
  last_service_date DATE,
  last_maintenance DATE,
  next_service_date DATE,
  next_maintenance DATE,
  
  -- الوصف
  description TEXT,
  description_ar TEXT,
  description_en TEXT,
  terms_conditions_ar TEXT,
  terms_conditions_en TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول مكاتب تأجير السيارات
CREATE TABLE rental_offices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  office_code TEXT UNIQUE NOT NULL, -- رمز المكتب (مثل DXB001, SHJ002)
  name TEXT NOT NULL,
  name_ar TEXT NOT NULL,
  name_en TEXT NOT NULL,
  
  -- معلومات الاتصال
  phone TEXT NOT NULL,
  email TEXT NOT NULL,
  whatsapp TEXT,
  fax TEXT,
  website TEXT,
  
  -- العنوان التفصيلي
  address TEXT NOT NULL,
  address_ar TEXT NOT NULL,
  address_en TEXT NOT NULL,
  street_address TEXT,
  landmark TEXT,
  building_name TEXT,
  floor_number TEXT,
  office_number TEXT,
  emirate TEXT NOT NULL CHECK (emirate IN ('Dubai', 'Abu Dhabi', 'Sharjah', 'Ajman', 'Ras Al Khaimah', 'Fujairah', 'Umm Al Quwain')),
  city TEXT NOT NULL,
  area TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'UAE',
  
  -- الموقع الجغرافي
  latitude DECIMAL(10,8) NOT NULL,
  longitude DECIMAL(11,8) NOT NULL,
  location_point POINT, -- للاستعلامات الجغرافية
  google_maps_url TEXT,
  
  -- أوقات العمل
  working_hours JSONB NOT NULL DEFAULT '{
    "saturday": {"open": "08:00", "close": "22:00", "is_open": true},
    "sunday": {"open": "08:00", "close": "22:00", "is_open": true},
    "monday": {"open": "08:00", "close": "22:00", "is_open": true},
    "tuesday": {"open": "08:00", "close": "22:00", "is_open": true},
    "wednesday": {"open": "08:00", "close": "22:00", "is_open": true},
    "thursday": {"open": "08:00", "close": "22:00", "is_open": true},
    "friday": {"open": "14:00", "close": "22:00", "is_open": true}
  }'::jsonb,
  
  -- معلومات المكتب
  office_type TEXT NOT NULL CHECK (office_type IN ('main', 'branch', 'pickup_point', 'service_center')),
  capacity INTEGER DEFAULT 0, -- عدد السيارات التي يمكن أن يستوعبها المكتب
  current_cars_count INTEGER DEFAULT 0, -- عدد السيارات الحالي
  is_pickup_location BOOLEAN DEFAULT true,
  is_return_location BOOLEAN DEFAULT true,
  is_24_hours BOOLEAN DEFAULT false,
  has_valet_service BOOLEAN DEFAULT false,
  has_delivery_service BOOLEAN DEFAULT false,
  
  -- الخدمات المتاحة
  services TEXT[] DEFAULT '{}', -- مثل: cleaning, maintenance, inspection, fuel
  facilities TEXT[] DEFAULT '{}', -- مثل: parking, wifi, waiting_area, restroom
  
  -- معلومات الموظفين
  manager_name TEXT,
  manager_phone TEXT,
  staff_count INTEGER DEFAULT 0,
  
  -- التقييمات
  rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
  total_ratings INTEGER DEFAULT 0,
  
  -- الحالة والإعدادات
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance', 'closed')),
  
  -- معلومات إضافية
  description TEXT,
  description_ar TEXT,
  description_en TEXT,
  special_instructions TEXT,
  special_instructions_ar TEXT,
  special_instructions_en TEXT,
  
  -- الصور والوسائط
  images TEXT[] DEFAULT '{}',
  office_photos TEXT[] DEFAULT '{}',
  logo_url TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- إضافة عمود office_id إلى جدول السيارات
ALTER TABLE cars ADD COLUMN office_id UUID REFERENCES rental_offices(id) ON DELETE SET NULL;
ALTER TABLE cars ADD COLUMN assigned_office_id UUID REFERENCES rental_offices(id) ON DELETE SET NULL; -- مكتب الإسناد الحالي

-- جدول الحجوزات المحدث
CREATE TABLE bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  booking_number TEXT UNIQUE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  car_id UUID REFERENCES cars(id) ON DELETE CASCADE NOT NULL,
  
  -- مكاتب الاستلام والإرجاع
  pickup_office_id UUID REFERENCES rental_offices(id) ON DELETE SET NULL, -- مكتب الاستلام
  return_office_id UUID REFERENCES rental_offices(id) ON DELETE SET NULL, -- مكتب الإرجاع
  
  -- تواريخ الحجز
  pickup_date TIMESTAMP WITH TIME ZONE NOT NULL,
  return_date TIMESTAMP WITH TIME ZONE NOT NULL,
  pickup_date_time TIMESTAMP WITH TIME ZONE NOT NULL, -- للتطابق مع النموذج
  return_date_time TIMESTAMP WITH TIME ZONE NOT NULL, -- للتطابق مع النموذج
  actual_pickup_date TIMESTAMP WITH TIME ZONE,
  actual_return_date TIMESTAMP WITH TIME ZONE,
  
  -- مواقع الاستلام والتسليم
  pickup_location JSONB NOT NULL, -- موقع الاستلام بتفاصيل كاملة
  return_location JSONB NOT NULL, -- موقع الإرجاع بتفاصيل كاملة
  pickup_location_lat DECIMAL(10,8) NOT NULL,
  pickup_location_lng DECIMAL(11,8) NOT NULL,
  pickup_address TEXT NOT NULL,
  pickup_type TEXT DEFAULT 'pickup' CHECK (pickup_type IN ('pickup', 'delivery')),
  
  return_location_lat DECIMAL(10,8),
  return_location_lng DECIMAL(11,8),
  return_address TEXT,
  return_type TEXT DEFAULT 'return' CHECK (return_type IN ('return', 'pickup')),
  
  -- التكاليف والتسعير
  rental_duration_hours INTEGER,
  rental_duration_days INTEGER NOT NULL CHECK (rental_duration_days > 0),
  hourly_rate DECIMAL(10,2) DEFAULT 0,
  daily_rate DECIMAL(10,2) NOT NULL,
  subtotal DECIMAL(10,2) NOT NULL,
  tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  security_deposit DECIMAL(10,2) NOT NULL DEFAULT 1000,
  delivery_fee DECIMAL(10,2) DEFAULT 0,
  additional_fees DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount > 0),
  pricing JSONB, -- تفاصيل التسعير كاملة
  
  -- الحالة
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'car_assigned', 'picked_up', 'active', 'completed', 'cancelled')),
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partially_paid', 'failed', 'refunded')),
  
  -- معلومات الدفع
  payment JSONB, -- معلومات الدفع كاملة
  payment_method TEXT,
  payment_intent_id TEXT,
  
  -- التوقيعات والوثائق
  pickup_signature_url TEXT,
  return_signature_url TEXT,
  signature_url TEXT, -- التوقيع الرئيسي
  contract_url TEXT, -- رابط العقد
  pickup_inspection_photos TEXT[],
  return_inspection_photos TEXT[],
  document_urls TEXT[] DEFAULT '{}', -- روابط الوثائق
  
  -- الشروط والأحكام
  terms_accepted BOOLEAN DEFAULT false NOT NULL,
  terms_accepted_at TIMESTAMP WITH TIME ZONE,
  
  -- الطلبات والملاحظات
  special_requests TEXT,
  pickup_notes TEXT,
  return_notes TEXT,
  notes TEXT, -- ملاحظات عامة
  
  -- الإلغاء
  cancellation JSONB, -- معلومات الإلغاء
  cancellation_reason TEXT,
  cancelled_at TIMESTAMP WITH TIME ZONE,
  cancelled_by UUID REFERENCES profiles(id),
  
  -- معلومات إضافية
  driver_age INTEGER,
  additional_drivers TEXT[],
  emergency_contact_name TEXT,
  emergency_contact_phone TEXT,
  
  -- سجل الحالات
  status_history JSONB DEFAULT '[]', -- سجل تغييرات الحالة
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  CONSTRAINT valid_dates CHECK (return_date > pickup_date),
  CONSTRAINT valid_datetime CHECK (return_date_time > pickup_date_time)
);

-- جدول المدفوعات المحدث
CREATE TABLE payments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  payment_type TEXT NOT NULL CHECK (payment_type IN ('rental', 'deposit', 'additional', 'refund')),
  
  -- معلومات الدفع
  stripe_payment_intent_id TEXT UNIQUE,
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  currency TEXT DEFAULT 'AED' NOT NULL,
  
  -- الحالة
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'cancelled', 'refunded')),
  
  -- تفاصيل الدفع
  payment_method TEXT,
  payment_method_details JSONB,
  receipt_url TEXT,
  invoice_url TEXT,
  
  -- الاسترداد
  refund_amount DECIMAL(10,2) DEFAULT 0,
  refund_reason TEXT,
  refunded_at TIMESTAMP WITH TIME ZONE,
  
  -- معلومات إضافية
  description TEXT,
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول سجل تغيير حالات الحجز
CREATE TABLE booking_status_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  from_status TEXT,
  to_status TEXT NOT NULL,
  changed_by UUID REFERENCES profiles(id),
  reason TEXT,
  notes TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول معلومات الموقع (للاستخدام في عدة جداول)
CREATE TABLE locations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  address TEXT NOT NULL,
  latitude DECIMAL(10,8) NOT NULL,
  longitude DECIMAL(11,8) NOT NULL,
  city TEXT DEFAULT 'Dubai',
  emirate TEXT DEFAULT 'Dubai',
  postal_code TEXT,
  type TEXT CHECK (type IN ('pickup', 'dropoff', 'both')),
  is_active BOOLEAN DEFAULT true,
  operating_hours JSONB, -- ساعات العمل
  contact_info JSONB, -- معلومات الاتصال
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول التقييمات المحدث
CREATE TABLE reviews (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE NOT NULL UNIQUE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  car_id UUID REFERENCES cars(id) ON DELETE CASCADE NOT NULL,
  
  -- التقييمات
  overall_rating INTEGER NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
  car_condition_rating INTEGER CHECK (car_condition_rating >= 1 AND car_condition_rating <= 5),
  service_rating INTEGER CHECK (service_rating >= 1 AND service_rating <= 5),
  value_rating INTEGER CHECK (value_rating >= 1 AND value_rating <= 5),
  
  -- التعليقات
  comment TEXT,
  pros TEXT[],
  cons TEXT[],
  
  -- الحالة
  is_verified BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول الإشعارات المحدث
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  
  -- محتوى الإشعار
  title_ar TEXT NOT NULL,
  title_en TEXT NOT NULL,
  body_ar TEXT NOT NULL,
  body_en TEXT NOT NULL,
  
  -- النوع والأولوية
  type TEXT NOT NULL CHECK (type IN ('booking', 'payment', 'reminder', 'promotion', 'system', 'document', 'verification')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  
  -- البيانات الإضافية
  data JSONB DEFAULT '{}',
  action_url TEXT,
  
  -- الحالة
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMP WITH TIME ZONE,
  
  -- الإرسال
  sent_via_push BOOLEAN DEFAULT false,
  sent_via_email BOOLEAN DEFAULT false,
  sent_via_sms BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول الكوبونات والعروض
CREATE TABLE coupons (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  
  -- الوصف
  title_ar TEXT NOT NULL,
  title_en TEXT NOT NULL,
  description_ar TEXT,
  description_en TEXT,
  
  -- نوع الخصم
  discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
  discount_value DECIMAL(10,2) NOT NULL CHECK (discount_value > 0),
  max_discount_amount DECIMAL(10,2),
  min_order_amount DECIMAL(10,2) DEFAULT 0,
  
  -- الصلاحية
  valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
  valid_until TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- الاستخدام
  usage_limit INTEGER,
  used_count INTEGER DEFAULT 0,
  user_usage_limit INTEGER DEFAULT 1,
  
  -- الشروط
  applicable_car_types TEXT[],
  applicable_brands TEXT[],
  min_rental_days INTEGER DEFAULT 1,
  
  -- الحالة
  is_active BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول استخدام الكوبونات
CREATE TABLE coupon_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  coupon_id UUID REFERENCES coupons(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  discount_amount DECIMAL(10,2) NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول الأسئلة الشائعة المحدث
CREATE TABLE faqs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  category_ar TEXT NOT NULL,
  category_en TEXT NOT NULL,
  question_ar TEXT NOT NULL,
  question_en TEXT NOT NULL,
  answer_ar TEXT NOT NULL,
  answer_en TEXT NOT NULL,
  order_index INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- جدول إعدادات التطبيق
CREATE TABLE app_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value_ar TEXT,
  value_en TEXT,
  data_type TEXT DEFAULT 'text' CHECK (data_type IN ('text', 'number', 'boolean', 'json')),
  category TEXT NOT NULL,
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- إدراج بيانات السيارات التجريبية المحدثة
INSERT INTO cars (
  name, make, brand, model, year, category, type, transmission, fuel_type, doors, seats, 
  color, plate_number, license_plate, daily_rate, daily_price, weekly_rate, weekly_price, 
  monthly_rate, monthly_price, security_deposit, rating, total_ratings, total_reviews, 
  images, image_urls, thumbnail_url, features, safety_features, comfort_features, 
  location_lat, location_lng, location_address, current_location, pickup_locations, 
  pickup_location, return_location, status, insurance_expiry, registration_expiry, 
  description, description_ar, description_en
) VALUES

('تويوتا كامري 2024 - فئة فاخرة', 'Toyota', 'Toyota', 'Camry', 2024, 'luxury', 'sedan', 'automatic', 'gasoline', 4, 5, 
 'Pearl White', 'A-12345', 'A-12345', 180.00, 180.00, 1080.00, 1080.00, 4200.00, 4200.00, 1500.00, 4.8, 156, 156,
 ARRAY['/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400'], 
 ARRAY['/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400'], 
 '/placeholder.svg?height=200&width=300',
 ARRAY['GPS Navigation', 'Bluetooth', 'AC', 'USB Charging', 'Backup Camera', 'Cruise Control', 'Leather Seats', 'Premium Sound'],
 ARRAY['ABS Brakes', 'Multiple Airbags', 'Blind Spot Monitor', 'Lane Departure Warning', 'Emergency Braking'],
 ARRAY['Heated Seats', 'Panoramic Sunroof', 'Power Seats', 'Memory Seats', 'Dual Climate Control'],
 25.2048, 55.2708, 'Dubai Marina, Dubai, UAE', 
 '{"address": "Dubai Marina, Dubai, UAE", "latitude": 25.2048, "longitude": 55.2708, "details": "Available for pickup"}',
 ARRAY['Dubai Marina', 'Downtown Dubai', 'Dubai International Airport', 'Dubai Mall'],
 'Dubai Marina', 'Dubai Marina', 'active', '2025-12-31', '2025-06-30',
 'سيارة تويوتا كامري 2024 الفاخرة مع جميع وسائل الراحة والأمان. مثالية للرحلات الطويلة والاستخدام اليومي.',
 'سيارة تويوتا كامري 2024 الفاخرة مع جميع وسائل الراحة والأمان. مثالية للرحلات الطويلة والاستخدام اليومي.',
 'Luxury Toyota Camry 2024 with all comfort and safety features. Perfect for long trips and daily use.'),

('نيسان التيما 2023 - اقتصادية', 'Nissan', 'Nissan', 'Altima', 2023, 'economy', 'sedan', 'automatic', 'gasoline', 4, 5, 
 'Silver', 'B-67890', 'B-67890', 150.00, 150.00, 900.00, 900.00, 3500.00, 3500.00, 1200.00, 4.5, 98, 98,
 ARRAY['/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400'], 
 ARRAY['/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400'], 
 '/placeholder.svg?height=200&width=300',
 ARRAY['GPS Navigation', 'AC', 'Bluetooth', 'USB Charging', 'Keyless Entry', 'Push Start'],
 ARRAY['ABS Brakes', 'Airbags', 'Stability Control', 'Backup Camera'],
 ARRAY['Comfortable Seats', 'Climate Control', 'Power Windows', 'Power Mirrors'],
 25.1972, 55.2744, 'Downtown Dubai, Dubai, UAE', 
 '{"address": "Downtown Dubai, Dubai, UAE", "latitude": 25.1972, "longitude": 55.2744, "details": "Available for pickup"}',
 ARRAY['Downtown Dubai', 'Dubai Mall', 'Burj Khalifa', 'Dubai Fountain'],
 'Downtown Dubai', 'Downtown Dubai', 'active', '2025-10-15', '2025-05-20',
 'سيارة نيسان التيما 2023 اقتصادية ومريحة، مثالية للاستخدام اليومي في المدينة.',
 'سيارة نيسان التيما 2023 اقتصادية ومريحة، مثالية للاستخدام اليومي في المدينة.',
 'Economical and comfortable Nissan Altima 2023, perfect for daily city use.'),

('تويوتا راف 4 2024 - دفع رباعي', 'Toyota', 'Toyota', 'RAV4', 2024, 'premium', 'suv', 'automatic', 'gasoline', 4, 5, 
 'Black', 'D-22222', 'D-22222', 220.00, 220.00, 1320.00, 1320.00, 5100.00, 5100.00, 2000.00, 4.7, 203, 203,
 ARRAY['/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400'], 
 ARRAY['/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400', '/placeholder.svg?height=300&width=400'], 
 '/placeholder.svg?height=200&width=300',
 ARRAY['AWD', 'GPS Navigation', 'Bluetooth', 'AC', 'USB Charging', 'Backup Camera', 'Roof Rails', 'Multiple Drive Modes'],
 ARRAY['Toyota Safety Sense', 'Emergency Braking', 'Forward Collision Warning', 'Blind Spot Monitor', 'Rear Cross Traffic Alert'],
 ARRAY['Heated Seats', 'Dual Climate Control', 'Power Seats', 'Sunroof', 'LED Interior Lighting'],
 25.2285, 55.2708, 'Jumeirah Beach Residence, Dubai, UAE', 
 '{"address": "Jumeirah Beach Residence, Dubai, UAE", "latitude": 25.2285, "longitude": 55.2708, "details": "Available for pickup"}',
 ARRAY['Jumeirah Beach Residence', 'Dubai Marina', 'Palm Jumeirah', 'Mall of the Emirates'],
 'Jumeirah Beach Residence', 'Jumeirah Beach Residence', 'active', '2025-09-30', '2025-04-15',
 'سيارة تويوتا راف 4 2024 بدفع رباعي، مثالية للمغامرات والرحلات الصحراوية.',
 'سيارة تويوتا راف 4 2024 بدفع رباعي، مثالية للمغامرات والرحلات الصحراوية.',
 'Toyota RAV4 2024 with AWD, perfect for adventures and desert trips.');

-- إدراج بعض المواقع التجريبية
INSERT INTO locations (name, address, latitude, longitude, city, emirate, type, operating_hours, contact_info) VALUES
('Dubai Marina Branch', 'Dubai Marina Walk, Dubai Marina, Dubai', 25.2048, 55.2708, 'Dubai', 'Dubai', 'both', 
 '{"sunday": "08:00-22:00", "monday": "08:00-22:00", "tuesday": "08:00-22:00", "wednesday": "08:00-22:00", "thursday": "08:00-22:00", "friday": "08:00-22:00", "saturday": "08:00-22:00"}',
 '{"phone": "+971-4-123-1001", "email": "<EMAIL>"}'),

('Downtown Dubai Branch', 'Burj Khalifa Boulevard, Downtown Dubai', 25.1972, 55.2744, 'Dubai', 'Dubai', 'both',
 '{"sunday": "08:00-22:00", "monday": "08:00-22:00", "tuesday": "08:00-22:00", "wednesday": "08:00-22:00", "thursday": "08:00-22:00", "friday": "08:00-22:00", "saturday": "08:00-22:00"}',
 '{"phone": "+971-4-123-1002", "email": "<EMAIL>"}'),

('Dubai International Airport', 'Dubai International Airport, Terminal 3', 25.2532, 55.3657, 'Dubai', 'Dubai', 'pickup',
 '{"sunday": "06:00-24:00", "monday": "06:00-24:00", "tuesday": "06:00-24:00", "wednesday": "06:00-24:00", "thursday": "06:00-24:00", "friday": "06:00-24:00", "saturday": "06:00-24:00"}',
 '{"phone": "+971-4-123-1003", "email": "<EMAIL>"}'),

('Jumeirah Beach Branch', 'The Walk, Jumeirah Beach Residence', 25.2285, 55.2708, 'Dubai', 'Dubai', 'both',
 '{"sunday": "09:00-21:00", "monday": "09:00-21:00", "tuesday": "09:00-21:00", "wednesday": "09:00-21:00", "thursday": "09:00-21:00", "friday": "09:00-21:00", "saturday": "09:00-21:00"}',
 '{"phone": "+971-4-123-1004", "email": "<EMAIL>"}');

-- إدراج إعدادات التطبيق
INSERT INTO app_settings (key, value_ar, value_en, category, is_public) VALUES
('app_version', '1.0.0', '1.0.0', 'general', true),
('maintenance_mode', 'false', 'false', 'general', true),
('min_rental_age', '21', '21', 'booking', true),
('max_rental_days', '30', '30', 'booking', true),
('cancellation_hours', '24', '24', 'booking', true),
('support_phone', '+971-4-123-4567', '+971-4-123-4567', 'contact', true),
('support_email', '<EMAIL>', '<EMAIL>', 'contact', true),
('terms_url', 'https://dubaicarrental.ae/terms-ar', 'https://dubaicarrental.ae/terms-en', 'legal', true),
('privacy_url', 'https://dubaicarrental.ae/privacy-ar', 'https://dubaicarrental.ae/privacy-en', 'legal', true);

-- إدراج الأسئلة الشائعة
INSERT INTO faqs (category_ar, category_en, question_ar, question_en, answer_ar, answer_en, order_index) VALUES
('المتطلبات', 'Requirements', 'ما هي متطلبات تأجير السيارة؟', 'What are the car rental requirements?', 
 'تحتاج إلى: رخصة قيادة سارية المفعول، هوية إماراتية أو جواز سفر، عمر 21 سنة فأكثر، وبطاقة ائتمان للدفع والضمان.', 
 'You need: Valid driving license, Emirates ID or passport, minimum age 21 years, and credit card for payment and security deposit.', 1),

('الحجز', 'Booking', 'هل يمكنني إلغاء الحجز؟', 'Can I cancel my booking?', 
 'نعم، يمكنك إلغاء الحجز قبل 24 ساعة من موعد الاستلام مع استرداد كامل. الإلغاء بعد ذلك قد يتضمن رسوم إلغاء.', 
 'Yes, you can cancel your booking 24 hours before pickup time with full refund. Cancellation after that may include cancellation fees.', 1),

('الوقود', 'Fuel', 'ما هي سياسة الوقود؟', 'What is the fuel policy?', 
 'السيارة تُسلم بخزان ممتلئ ويجب إرجاعها بنفس المستوى. في حالة عدم ملء الخزان، سيتم خصم تكلفة الوقود من الضمان.', 
 'The car is delivered with a full tank and should be returned with the same fuel level. If not refueled, fuel cost will be deducted from the deposit.', 1);

-- تفعيل Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupon_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_status_history ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own bookings" ON bookings FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own bookings" ON bookings FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own bookings" ON bookings FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own payments" ON payments 
  FOR SELECT USING (EXISTS (SELECT 1 FROM bookings WHERE bookings.id = payments.booking_id AND bookings.user_id = auth.uid()));

CREATE POLICY "Users can view own reviews" ON reviews FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own reviews" ON reviews FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own booking history" ON booking_status_history 
  FOR SELECT USING (EXISTS (SELECT 1 FROM bookings WHERE bookings.id = booking_status_history.booking_id AND bookings.user_id = auth.uid()));

-- السياسات العامة
CREATE POLICY "Cars are viewable by everyone" ON cars FOR SELECT USING (true);
CREATE POLICY "Locations are viewable by everyone" ON locations FOR SELECT USING (is_active = true);
CREATE POLICY "FAQs are viewable by everyone" ON faqs FOR SELECT USING (is_active = true);
CREATE POLICY "Public settings are viewable by everyone" ON app_settings FOR SELECT USING (is_public = true);

-- الفهارس المحسنة
CREATE INDEX idx_cars_available ON cars (is_available, availability_status) WHERE is_available = true;
CREATE INDEX idx_cars_category ON cars (category, type);
CREATE INDEX idx_cars_location ON cars USING GIST (POINT(location_lng, location_lat));
CREATE INDEX idx_cars_price ON cars (daily_rate, category);

CREATE INDEX idx_bookings_user_status ON bookings (user_id, status);
CREATE INDEX idx_bookings_car_dates ON bookings (car_id, pickup_date, return_date);
CREATE INDEX idx_bookings_dates ON bookings (pickup_date, return_date);
CREATE INDEX idx_bookings_status ON bookings (status, payment_status);

CREATE INDEX idx_payments_booking ON payments (booking_id, status);
CREATE INDEX idx_payments_stripe ON payments (stripe_payment_intent_id) WHERE stripe_payment_intent_id IS NOT NULL;

CREATE INDEX idx_reviews_car ON reviews (car_id, overall_rating);
CREATE INDEX idx_notifications_user_unread ON notifications (user_id, is_read, created_at) WHERE is_read = false;

CREATE INDEX idx_booking_history_booking ON booking_status_history (booking_id, created_at);

-- Storage buckets
INSERT INTO storage.buckets (id, name, public) 
SELECT 'car-images', 'car-images', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'car-images');

INSERT INTO storage.buckets (id, name, public) 
SELECT 'signatures', 'signatures', false
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'signatures');

INSERT INTO storage.buckets (id, name, public) 
SELECT 'documents', 'documents', false
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'documents');

INSERT INTO storage.buckets (id, name, public) 
SELECT 'profile-images', 'profile-images', false
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'profile-images');

INSERT INTO storage.buckets (id, name, public) 
SELECT 'inspection-photos', 'inspection-photos', false
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'inspection-photos');

-- دوال مساعدة محسنة
CREATE OR REPLACE FUNCTION generate_booking_number() RETURNS TEXT AS $$
BEGIN
  RETURN 'DCR' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
END;
$$ LANGUAGE plpgsql;

-- دالة لحساب المسافة بين نقطتين
CREATE OR REPLACE FUNCTION calculate_distance(lat1 DECIMAL, lng1 DECIMAL, lat2 DECIMAL, lng2 DECIMAL) 
RETURNS DECIMAL AS $$
DECLARE
  earth_radius DECIMAL := 6371; -- نصف قطر الأرض بالكيلومترات
  dlat DECIMAL;
  dlng DECIMAL;
  a DECIMAL;
  c DECIMAL;
BEGIN
  dlat := RADIANS(lat2 - lat1);
  dlng := RADIANS(lng2 - lng1);
  a := SIN(dlat/2) * SIN(dlat/2) + COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * SIN(dlng/2) * SIN(dlng/2);
  c := 2 * ATAN2(SQRT(a), SQRT(1-a));
  RETURN earth_radius * c;
END;
$$ LANGUAGE plpgsql;

-- دالة للبحث عن السيارات المتاحة
CREATE OR REPLACE FUNCTION get_available_cars(
  pickup_date TIMESTAMP WITH TIME ZONE,
  return_date TIMESTAMP WITH TIME ZONE,
  user_lat DECIMAL DEFAULT NULL,
  user_lng DECIMAL DEFAULT NULL,
  max_distance DECIMAL DEFAULT 50
) 
RETURNS TABLE (
  car_id UUID,
  name TEXT,
  make TEXT,
  model TEXT,
  category TEXT,
  daily_rate DECIMAL,
  rating DECIMAL,
  distance DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.name,
    c.make,
    c.model,
    c.category,
    c.daily_rate,
    c.rating,
    CASE 
      WHEN user_lat IS NOT NULL AND user_lng IS NOT NULL THEN
        calculate_distance(user_lat, user_lng, c.location_lat, c.location_lng)
      ELSE NULL
    END as distance
  FROM cars c
  WHERE 
    c.is_available = true 
    AND c.availability_status = 'available'
    AND c.status = 'active'
    AND NOT EXISTS (
      SELECT 1 FROM bookings b
      WHERE b.car_id = c.id
      AND b.status IN ('confirmed', 'car_assigned', 'picked_up', 'active')
      AND (
        (pickup_date BETWEEN b.pickup_date AND b.return_date)
        OR (return_date BETWEEN b.pickup_date AND b.return_date)
        OR (b.pickup_date BETWEEN pickup_date AND return_date)
      )
    )
    AND (
      user_lat IS NULL OR user_lng IS NULL OR max_distance IS NULL
      OR calculate_distance(user_lat, user_lng, c.location_lat, c.location_lng) <= max_distance
    )
  ORDER BY 
    CASE WHEN user_lat IS NOT NULL THEN distance ELSE c.rating END DESC;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث تقييم السيارة
CREATE OR REPLACE FUNCTION update_car_rating() RETURNS TRIGGER AS $$
BEGIN
  UPDATE cars 
  SET 
    rating = (
      SELECT COALESCE(AVG(overall_rating), 0)
      FROM reviews 
      WHERE car_id = NEW.car_id
    ),
    total_reviews = (
      SELECT COUNT(*)
      FROM reviews 
      WHERE car_id = NEW.car_id
    )
  WHERE id = NEW.car_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنشاء سجل تغيير الحالة
CREATE OR REPLACE FUNCTION log_booking_status_change() RETURNS TRIGGER AS $$
BEGIN
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO booking_status_history (booking_id, from_status, to_status, changed_by, created_at)
    VALUES (NEW.id, OLD.status, NEW.status, auth.uid(), NOW());
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- محفزات (Triggers)
CREATE OR REPLACE FUNCTION set_booking_number() RETURNS TRIGGER AS $$
BEGIN
  IF NEW.booking_number IS NULL THEN
    NEW.booking_number := generate_booking_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_booking_number_trigger
  BEFORE INSERT ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION set_booking_number();

CREATE TRIGGER update_car_rating_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_car_rating();

CREATE TRIGGER log_booking_status_trigger
  AFTER UPDATE ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION log_booking_status_change();

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق تحديث updated_at على الجداول
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cars_updated_at BEFORE UPDATE ON cars
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON locations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_app_settings_updated_at BEFORE UPDATE ON app_settings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إضافة trigger لجدول مكاتب التأجير
CREATE TRIGGER update_rental_offices_updated_at BEFORE UPDATE ON rental_offices
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إنشاء فهارس للمكاتب
CREATE INDEX idx_rental_offices_office_code ON rental_offices(office_code);
CREATE INDEX idx_rental_offices_emirate ON rental_offices(emirate);
CREATE INDEX idx_rental_offices_city ON rental_offices(city);
CREATE INDEX idx_rental_offices_office_type ON rental_offices(office_type);
CREATE INDEX idx_rental_offices_status ON rental_offices(status);
CREATE INDEX idx_rental_offices_location ON rental_offices(latitude, longitude);

-- فهارس للعلاقات الجديدة في جدول السيارات
CREATE INDEX idx_cars_office_id ON cars(office_id);
CREATE INDEX idx_cars_assigned_office_id ON cars(assigned_office_id);

-- فهارس للعلاقات الجديدة في جدول الحجوزات
CREATE INDEX idx_bookings_pickup_office_id ON bookings(pickup_office_id);
CREATE INDEX idx_bookings_return_office_id ON bookings(return_office_id);

-- دالة لحساب المسافة بين المكاتب والمواقع
CREATE OR REPLACE FUNCTION calculate_distance_to_offices(
  user_lat DECIMAL(10,8),
  user_lng DECIMAL(11,8),
  max_distance_km DECIMAL DEFAULT 50
)
RETURNS TABLE (
  office_id UUID,
  office_name TEXT,
  distance_km DECIMAL,
  office_type TEXT,
  is_active BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ro.id,
    ro.name,
    (6371 * acos(cos(radians(user_lat)) * cos(radians(ro.latitude)) 
      * cos(radians(ro.longitude) - radians(user_lng)) 
      + sin(radians(user_lat)) * sin(radians(ro.latitude))))::DECIMAL as distance,
    ro.office_type,
    ro.is_active
  FROM rental_offices ro
  WHERE ro.is_active = true
    AND (6371 * acos(cos(radians(user_lat)) * cos(radians(ro.latitude)) 
      * cos(radians(ro.longitude) - radians(user_lng)) 
      + sin(radians(user_lat)) * sin(radians(ro.latitude)))) <= max_distance_km
  ORDER BY distance;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث عدد السيارات في المكتب
CREATE OR REPLACE FUNCTION update_office_cars_count()
RETURNS TRIGGER AS $$
BEGIN
  -- تحديث العدد في المكتب القديم (إذا كان موجود)
  IF TG_OP = 'UPDATE' AND OLD.office_id IS DISTINCT FROM NEW.office_id THEN
    IF OLD.office_id IS NOT NULL THEN
      UPDATE rental_offices 
      SET current_cars_count = (
        SELECT COUNT(*) FROM cars WHERE office_id = OLD.office_id
      )
      WHERE id = OLD.office_id;
    END IF;
  END IF;
  
  -- تحديث العدد في المكتب الجديد
  IF TG_OP IN ('INSERT', 'UPDATE') AND NEW.office_id IS NOT NULL THEN
    UPDATE rental_offices 
    SET current_cars_count = (
      SELECT COUNT(*) FROM cars WHERE office_id = NEW.office_id
    )
    WHERE id = NEW.office_id;
  END IF;
  
  -- تحديث العدد في المكتب المحذوف منه
  IF TG_OP = 'DELETE' AND OLD.office_id IS NOT NULL THEN
    UPDATE rental_offices 
    SET current_cars_count = (
      SELECT COUNT(*) FROM cars WHERE office_id = OLD.office_id
    )
    WHERE id = OLD.office_id;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- ربط trigger بجدول السيارات لتحديث العدد تلقائياً
CREATE TRIGGER cars_office_count_update
  AFTER INSERT OR UPDATE OR DELETE ON cars
  FOR EACH ROW EXECUTE FUNCTION update_office_cars_count();

-- دالة للبحث عن المكاتب المتاحة حسب المنطقة
CREATE OR REPLACE FUNCTION get_available_offices_by_area(
  area_name TEXT DEFAULT NULL,
  emirate_name TEXT DEFAULT NULL,
  office_types TEXT[] DEFAULT NULL
)
RETURNS TABLE (
  office_id UUID,
  office_code TEXT,
  name TEXT,
  name_ar TEXT,
  emirate TEXT,
  city TEXT,
  area TEXT,
  available_cars INTEGER,
  office_type TEXT,
  is_24_hours BOOLEAN,
  rating DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ro.id,
    ro.office_code,
    ro.name,
    ro.name_ar,
    ro.emirate,
    ro.city,
    ro.area,
    ro.current_cars_count,
    ro.office_type,
    ro.is_24_hours,
    ro.rating
  FROM rental_offices ro
  WHERE ro.is_active = true
    AND (area_name IS NULL OR ro.area ILIKE '%' || area_name || '%')
    AND (emirate_name IS NULL OR ro.emirate = emirate_name)
    AND (office_types IS NULL OR ro.office_type = ANY(office_types))
  ORDER BY ro.rating DESC, ro.current_cars_count DESC;
END;
$$ LANGUAGE plpgsql;

-- إدراج بيانات تجريبية للمكاتب
INSERT INTO rental_offices (
  office_code, name, name_ar, name_en, phone, email, address, address_ar, address_en,
  emirate, city, area, latitude, longitude, office_type, capacity, is_pickup_location, is_return_location
) VALUES 
-- مكتب دبي الرئيسي
('DXB001', 'Dubai Main Office', 'المكتب الرئيسي - دبي', 'Dubai Main Office', '+971-4-555-1001', '<EMAIL>', 
 'Downtown Dubai, Burj Khalifa Boulevard', 'وسط دبي، شارع برج خليفة', 'Downtown Dubai, Burj Khalifa Boulevard',
 'Dubai', 'Dubai', 'Downtown Dubai', 25.1972, 55.2744, 'main', 50, true, true),

-- مكتب مطار دبي
('DXB002', 'Dubai Airport Office', 'مكتب مطار دبي', 'Dubai Airport Office', '+971-4-555-1002', '<EMAIL>',
 'Dubai International Airport, Terminal 3', 'مطار دبي الدولي، المبنى 3', 'Dubai International Airport, Terminal 3',
 'Dubai', 'Dubai', 'Al Garhoud', 25.2532, 55.3657, 'branch', 30, true, true),

-- مكتب دبي مول
('DXB003', 'Dubai Mall Office', 'مكتب دبي مول', 'Dubai Mall Office', '+971-4-555-1003', '<EMAIL>',
 'The Dubai Mall, Lower Ground Floor', 'دبي مول، الطابق السفلي', 'The Dubai Mall, Lower Ground Floor',
 'Dubai', 'Dubai', 'Downtown Dubai', 25.1975, 55.2796, 'branch', 20, true, true),

-- مكتب الشارقة
('SHJ001', 'Sharjah Main Office', 'المكتب الرئيسي - الشارقة', 'Sharjah Main Office', '+971-6-555-2001', '<EMAIL>',
 'Al Majaz, Corniche Street', 'المجاز، شارع الكورنيش', 'Al Majaz, Corniche Street',
 'Sharjah', 'Sharjah', 'Al Majaz', 25.3216, 55.3809, 'branch', 25, true, true),

-- مكتب أبوظبي
('AUH001', 'Abu Dhabi Main Office', 'المكتب الرئيسي - أبوظبي', 'Abu Dhabi Main Office', '+971-2-555-3001', '<EMAIL>',
 'Corniche Road, Abu Dhabi', 'شارع الكورنيش، أبوظبي', 'Corniche Road, Abu Dhabi',
 'Abu Dhabi', 'Abu Dhabi', 'Corniche', 24.4539, 54.3773, 'branch', 35, true, true);

-- تحديث عداد السيارات لكل مكتب (سيتم تحديثه تلقائياً عند إضافة السيارات)
UPDATE rental_offices SET current_cars_count = 0;