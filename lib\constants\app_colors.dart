import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Royal Blue Theme
  static const Color primary = Color(0xFF0052CC);
  static const Color primaryLight = Color(0xFF4D7FFF);
  static const Color primaryDark = Color(0xFF003399);
  static const Color primaryContainer = Color(0xFFE6F2FF);

  // Secondary Colors
  static const Color secondary = Color(0xFF666CFF);
  static const Color secondaryLight = Color(0xFF9C9EFF);
  static const Color secondaryDark = Color(0xFF3E47CC);
  static const Color secondaryContainer = Color(0xFFEEEFFF);

  // Accent Colors
  static const Color accent = Color(0xFFFF6B35);
  static const Color accentLight = Color(0xFFFF9568);
  static const Color accentDark = Color(0xFFCC4102);

  // Success Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF388E3C);
  static const Color successContainer = Color(0xFFE8F5E8);

  // Warning Colors
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFF57C00);
  static const Color warningContainer = Color(0xFFFFF3E0);

  // Error Colors
  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFEF5350);
  static const Color errorDark = Color(0xFFD32F2F);
  static const Color errorContainer = Color(0xFFFFEBEE);

  // Neutral Colors - Light Theme
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color surfaceContainer = Color(0xFFFAFAFA);
  static const Color surfaceContainerHigh = Color(0xFFEFEFEF);
  static const Color surfaceContainerHighest = Color(0xFFE0E0E0);

  static const Color onSurface = Color(0xFF1C1C1C);
  static const Color onSurfaceVariant = Color(0xFF424242);
  static const Color onSurfaceSecondary = Color(0xFF666666);
  static const Color onSurfaceTertiary = Color(0xFF999999);

  // Neutral Colors - Dark Theme
  static const Color surfaceDark = Color(0xFF121212);
  static const Color surfaceVariantDark = Color(0xFF1E1E1E);
  static const Color surfaceContainerDark = Color(0xFF2C2C2C);
  static const Color surfaceContainerHighDark = Color(0xFF363636);
  static const Color surfaceContainerHighestDark = Color(0xFF404040);

  static const Color onSurfaceDark = Color(0xFFFFFFFF);
  static const Color onSurfaceVariantDark = Color(0xFFE0E0E0);
  static const Color onSurfaceSecondaryDark = Color(0xFFB3B3B3);
  static const Color onSurfaceTertiaryDark = Color(0xFF808080);

  // Outline Colors
  static const Color outline = Color(0xFFE0E0E0);
  static const Color outlineVariant = Color(0xFFF0F0F0);
  static const Color outlineDark = Color(0xFF404040);
  static const Color outlineVariantDark = Color(0xFF303030);

  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0F000000);
  static const Color shadowDark = Color(0x33000000);

  // Brand Colors
  static const Color gold = Color(0xFFFFD700);
  static const Color silver = Color(0xFFC0C0C0);
  static const Color bronze = Color(0xFFCD7F32);

  // Status Colors
  static const Color online = Color(0xFF4CAF50);
  static const Color offline = Color(0xFF9E9E9E);
  static const Color away = Color(0xFFFF9800);
  static const Color busy = Color(0xFFF44336);

  // Car Category Colors
  static const Color economy = Color(0xFF4CAF50);
  static const Color standard = Color(0xFF2196F3);
  static const Color premium = Color(0xFF9C27B0);
  static const Color luxury = Color(0xFFFF9800);
  static const Color exotic = Color(0xFFE91E63);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient surfaceGradient = LinearGradient(
    colors: [surface, surfaceVariant],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient overlayGradient = LinearGradient(
    colors: [Colors.transparent, Color(0x80000000)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // Transparent Colors
  static const Color transparent = Colors.transparent;
  static const Color black12 = Colors.black12;
  static const Color black26 = Colors.black26;
  static const Color black38 = Colors.black38;
  static const Color black45 = Colors.black45;
  static const Color black54 = Colors.black54;
  static const Color white10 = Colors.white10;
  static const Color white12 = Colors.white12;
  static const Color white24 = Colors.white24;
  static const Color white30 = Colors.white30;
  static const Color white38 = Colors.white38;
  static const Color white54 = Colors.white54;
  static const Color white60 = Colors.white60;
  static const Color white70 = Colors.white70;
}

// Color Schemes for Material 3
class AppColorSchemes {
  static ColorScheme get lightColorScheme => ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.primary,
    onPrimary: Colors.white,
    primaryContainer: AppColors.primaryContainer,
    onPrimaryContainer: AppColors.primaryDark,
    secondary: AppColors.secondary,
    onSecondary: Colors.white,
    secondaryContainer: AppColors.secondaryContainer,
    onSecondaryContainer: AppColors.secondaryDark,
    tertiary: AppColors.accent,
    onTertiary: Colors.white,
    tertiaryContainer: AppColors.warningContainer,
    onTertiaryContainer: AppColors.accentDark,
    error: AppColors.error,
    onError: Colors.white,
    errorContainer: AppColors.errorContainer,
    onErrorContainer: AppColors.errorDark,
    outline: AppColors.outline,
    outlineVariant: AppColors.outlineVariant,
    surface: AppColors.surface,
    onSurface: AppColors.onSurface,
    surfaceVariant: AppColors.surfaceVariant,
    onSurfaceVariant: AppColors.onSurfaceVariant,
    inverseSurface: AppColors.onSurface,
    onInverseSurface: AppColors.surface,
    inversePrimary: AppColors.primaryLight,
    shadow: AppColors.shadow,
    surfaceTint: AppColors.primary,
  );

  static ColorScheme get darkColorScheme => ColorScheme(
    brightness: Brightness.dark,
    primary: AppColors.primaryLight,
    onPrimary: AppColors.primaryDark,
    primaryContainer: AppColors.primaryDark,
    onPrimaryContainer: AppColors.primaryContainer,
    secondary: AppColors.secondaryLight,
    onSecondary: AppColors.secondaryDark,
    secondaryContainer: AppColors.secondaryDark,
    onSecondaryContainer: AppColors.secondaryContainer,
    tertiary: AppColors.accentLight,
    onTertiary: AppColors.accentDark,
    tertiaryContainer: AppColors.accentDark,
    onTertiaryContainer: AppColors.warningContainer,
    error: AppColors.errorLight,
    onError: AppColors.errorDark,
    errorContainer: AppColors.errorDark,
    onErrorContainer: AppColors.errorContainer,
    outline: AppColors.outlineDark,
    outlineVariant: AppColors.outlineVariantDark,
    surface: AppColors.surfaceDark,
    onSurface: AppColors.onSurfaceDark,
    surfaceVariant: AppColors.surfaceVariantDark,
    onSurfaceVariant: AppColors.onSurfaceVariantDark,
    inverseSurface: AppColors.surface,
    onInverseSurface: AppColors.onSurface,
    inversePrimary: AppColors.primary,
    shadow: AppColors.shadowDark,
    surfaceTint: AppColors.primaryLight,
  );
}
