import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../models/car.dart';
import '../../models/car_model.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/price_summary_card.dart';
import '../../services/booking_service.dart';
import '../../services/stripe_service.dart';
import '../../providers/auth_provider.dart';
import '../../constants/app_colors.dart';
import 'payment_screen.dart';

class BookingScreen extends StatefulWidget {
  final Car car;

  const BookingScreen({
    Key? key,
    required this.car,
  }) : super(key: key);

  @override
  _BookingScreenState createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // تواريخ الحجز
  DateTime? _pickupDate;
  DateTime? _returnDate;
  TimeOfDay? _pickupTime;
  TimeOfDay? _returnTime;

  // مواقع الاستلام والتسليم
  String _pickupLocation = '';
  String _returnLocation = '';
  bool _deliveryToLocation = false;

  // معلومات إضافية
  final _emergencyContactNameController = TextEditingController();
  final _emergencyContactPhoneController = TextEditingController();
  final _specialRequestsController = TextEditingController();

  // السائقون الإضافيون
  List<Map<String, String>> _additionalDrivers = [];

  // الخدمات الإضافية
  bool _childSeat = false;
  bool _gpsNavigation = false;
  bool _additionalInsurance = false;
  bool _unlimitedMileage = false;

  // الشروط والأحكام
  bool _acceptTerms = false;
  bool _acceptCancellationPolicy = false;

  // حسابات السعر
  double _subtotal = 0;
  double _tax = 0;
  double _deliveryFee = 0;
  double _additionalServicesFee = 0;
  double _total = 0;
  int _rentalDays = 1;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _pickupLocation = widget.car.locationAddress;
    _returnLocation = widget.car.locationAddress;
    _calculatePricing();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emergencyContactNameController.dispose();
    _emergencyContactPhoneController.dispose();
    _specialRequestsController.dispose();
    super.dispose();
  }

  void _calculatePricing() {
    if (_pickupDate != null && _returnDate != null) {
      _rentalDays = _returnDate!.difference(_pickupDate!).inDays;
      if (_rentalDays == 0) _rentalDays = 1;
    }

    _subtotal = widget.car.dailyPrice * _rentalDays;

    // رسوم الخدمات الإضافية
    _additionalServicesFee = 0;
    if (_childSeat) _additionalServicesFee += 25;
    if (_gpsNavigation) _additionalServicesFee += 15;
    if (_additionalInsurance) _additionalServicesFee += 50;
    if (_unlimitedMileage) _additionalServicesFee += 30;

    // رسوم التوصيل
    _deliveryFee = _deliveryToLocation ? 50 : 0;

    // الضريبة
    _tax = (_subtotal + _additionalServicesFee) * 0.05; // 5% VAT

    // المجموع
    _total = _subtotal + _additionalServicesFee + _deliveryFee + _tax;

    setState(() {});
  }

  Future<void> _selectPickupDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppTheme.royalBlue,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _pickupDate = picked;
        if (_returnDate != null && _returnDate!.isBefore(picked)) {
          _returnDate = picked.add(const Duration(days: 1));
        }
      });
      _calculatePricing();
    }
  }

  Future<void> _selectReturnDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _pickupDate?.add(const Duration(days: 1)) ??
          DateTime.now().add(const Duration(days: 2)),
      firstDate: _pickupDate ?? DateTime.now().add(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppTheme.royalBlue,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _returnDate = picked;
      });
      _calculatePricing();
    }
  }

  Future<void> _selectPickupTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppTheme.royalBlue,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _pickupTime = picked;
      });
    }
  }

  Future<void> _selectReturnTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppTheme.royalBlue,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _returnTime = picked;
      });
    }
  }

  void _addAdditionalDriver() {
    showDialog(
      context: context,
      builder: (context) => _AddDriverDialog(
        onDriverAdded: (driver) {
          setState(() {
            _additionalDrivers.add(driver);
          });
        },
      ),
    );
  }

  void _removeAdditionalDriver(int index) {
    setState(() {
      _additionalDrivers.removeAt(index);
    });
  }

  void _proceedToPayment() {
    if (!_formKey.currentState!.validate()) {
      _tabController.animateTo(0);
      return;
    }

    if (_pickupDate == null || _returnDate == null) {
      _showErrorSnackBar(AppLocalizations.of(context)!.selectDatesFirst);
      _tabController.animateTo(0);
      return;
    }

    if (!_acceptTerms || !_acceptCancellationPolicy) {
      _showErrorSnackBar(AppLocalizations.of(context)!.mustAcceptTerms);
      _tabController.animateTo(3);
      return;
    }

    // إنشاء بيانات الحجز
    final bookingData = {
      'car_id': widget.car.id,
      'pickup_date': _pickupDate!.toIso8601String(),
      'return_date': _returnDate!.toIso8601String(),
      'pickup_time': _pickupTime?.format(context) ?? '10:00',
      'return_time': _returnTime?.format(context) ?? '10:00',
      'pickup_location': _pickupLocation,
      'return_location': _returnLocation,
      'delivery_to_location': _deliveryToLocation,
      'emergency_contact_name': _emergencyContactNameController.text,
      'emergency_contact_phone': _emergencyContactPhoneController.text,
      'special_requests': _specialRequestsController.text,
      'additional_drivers': _additionalDrivers,
      'child_seat': _childSeat,
      'gps_navigation': _gpsNavigation,
      'additional_insurance': _additionalInsurance,
      'unlimited_mileage': _unlimitedMileage,
      'subtotal': _subtotal,
      'tax': _tax,
      'delivery_fee': _deliveryFee,
      'additional_services_fee': _additionalServicesFee,
      'total': _total,
      'rental_days': _rentalDays,
    };

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PaymentScreen(
          car: widget.car,
          bookingData: bookingData,
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.errorRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppTheme.lightGrey,
      appBar: AppBar(
        title: Text(l10n.bookCar),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.royalBlue,
          unselectedLabelColor: AppTheme.mediumGrey,
          indicatorColor: AppTheme.royalBlue,
          tabs: [
            Tab(text: l10n.dateTime),
            Tab(text: l10n.details),
            Tab(text: l10n.extras),
            Tab(text: l10n.review),
          ],
        ),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // معلومات السيارة
            _buildCarInfoCard(l10n),

            // محتوى التبويبات
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildDateTimeTab(l10n),
                  _buildDetailsTab(l10n),
                  _buildExtrasTab(l10n),
                  _buildReviewTab(l10n),
                ],
              ),
            ),

            // ملخص السعر وزر المتابعة
            _buildBottomBar(l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildCarInfoCard(AppLocalizations l10n) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // صورة السيارة
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                widget.car.images.first,
                width: 80,
                height: 60,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 80,
                    height: 60,
                    color: AppTheme.lightGrey,
                    child: Icon(
                      Icons.directions_car,
                      color: AppTheme.mediumGrey,
                    ),
                  );
                },
              ),
            ),

            const SizedBox(width: 12),

            // معلومات السيارة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.car.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.car.brand} ${widget.car.model} ${widget.car.year}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.mediumGrey,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.people,
                        size: 16,
                        color: AppTheme.mediumGrey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.car.seats} ${l10n.seats}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.mediumGrey,
                            ),
                      ),
                      const SizedBox(width: 12),
                      Icon(
                        Icons.local_gas_station,
                        size: 16,
                        color: AppTheme.mediumGrey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.car.fuelType,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.mediumGrey,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // السعر
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${widget.car.dailyPrice.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.royalBlue,
                      ),
                ),
                Text(
                  l10n.aedPerDay,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.mediumGrey,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeTab(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تاريخ الاستلام
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.pickupDateTime,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 12),

                  // تاريخ الاستلام
                  InkWell(
                    onTap: _selectPickupDate,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppTheme.borderGrey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            color: AppTheme.royalBlue,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _pickupDate != null
                                  ? DateFormat('EEEE, dd MMMM yyyy')
                                      .format(_pickupDate!)
                                  : l10n.selectPickupDate,
                              style: TextStyle(
                                color: _pickupDate != null
                                    ? AppTheme.darkGrey
                                    : AppTheme.mediumGrey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // وقت الاستلام
                  InkWell(
                    onTap: _selectPickupTime,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppTheme.borderGrey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            color: AppTheme.royalBlue,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _pickupTime != null
                                  ? _pickupTime!.format(context)
                                  : l10n.selectPickupTime,
                              style: TextStyle(
                                color: _pickupTime != null
                                    ? AppTheme.darkGrey
                                    : AppTheme.mediumGrey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // تاريخ التسليم
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.returnDateTime,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 12),

                  // تاريخ التسليم
                  InkWell(
                    onTap: _selectReturnDate,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppTheme.borderGrey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            color: AppTheme.royalBlue,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _returnDate != null
                                  ? DateFormat('EEEE, dd MMMM yyyy')
                                      .format(_returnDate!)
                                  : l10n.selectReturnDate,
                              style: TextStyle(
                                color: _returnDate != null
                                    ? AppTheme.darkGrey
                                    : AppTheme.mediumGrey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // وقت التسليم
                  InkWell(
                    onTap: _selectReturnTime,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppTheme.borderGrey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            color: AppTheme.royalBlue,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _returnTime != null
                                  ? _returnTime!.format(context)
                                  : l10n.selectReturnTime,
                              style: TextStyle(
                                color: _returnTime != null
                                    ? AppTheme.darkGrey
                                    : AppTheme.mediumGrey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // ملخص المدة والسعر
          if (_pickupDate != null && _returnDate != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          l10n.rentalDuration,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        Text(
                          '$_rentalDays ${l10n.days}',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.royalBlue,
                                  ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(l10n.estimatedCost),
                        Text(
                          '${_subtotal.toStringAsFixed(0)} ${l10n.aed}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.royalBlue,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // مواقع الاستلام والتسليم
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.pickupReturnLocations,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),

                  // موقع الاستلام
                  DropdownButtonFormField<String>(
                    value: _pickupLocation,
                    decoration: InputDecoration(
                      labelText: l10n.pickupLocation,
                      prefixIcon: const Icon(Icons.location_on),
                    ),
                    items: [
                      widget.car.locationAddress,
                      ...widget.car.pickupLocations,
                    ].map((location) {
                      return DropdownMenuItem(
                        value: location,
                        child: Text(location),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _pickupLocation = value!;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // موقع التسليم
                  DropdownButtonFormField<String>(
                    value: _returnLocation,
                    decoration: InputDecoration(
                      labelText: l10n.returnLocation,
                      prefixIcon: const Icon(Icons.location_off),
                    ),
                    items: [
                      widget.car.locationAddress,
                      ...widget.car.pickupLocations,
                    ].map((location) {
                      return DropdownMenuItem(
                        value: location,
                        child: Text(location),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _returnLocation = value!;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // خيار التوصيل
                  CheckboxListTile(
                    title: Text(l10n.deliverToMyLocation),
                    subtitle: Text('${l10n.additionalFee}: 50 ${l10n.aed}'),
                    value: _deliveryToLocation,
                    onChanged: (value) {
                      setState(() {
                        _deliveryToLocation = value!;
                      });
                      _calculatePricing();
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // جهة الاتصال الطارئة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.emergencyContact,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _emergencyContactNameController,
                    labelText: l10n.emergencyContactName,
                    prefixIcon: Icon(Icons.person),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return l10n.fieldRequired;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _emergencyContactPhoneController,
                    labelText: l10n.emergencyContactPhone,
                    prefixIcon: Icon(Icons.phone),
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return l10n.fieldRequired;
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // السائقون الإضافيون
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.additionalDrivers,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      TextButton.icon(
                        onPressed: _addAdditionalDriver,
                        icon: const Icon(Icons.add),
                        label: Text(l10n.addDriver),
                      ),
                    ],
                  ),
                  if (_additionalDrivers.isEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        l10n.noAdditionalDrivers,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.mediumGrey,
                            ),
                      ),
                    )
                  else
                    ..._additionalDrivers.asMap().entries.map((entry) {
                      final index = entry.key;
                      final driver = entry.value;
                      return ListTile(
                        leading: CircleAvatar(
                          child: Text('${index + 1}'),
                        ),
                        title: Text(driver['name']!),
                        subtitle: Text(driver['license']!),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _removeAdditionalDriver(index),
                        ),
                      );
                    }).toList(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // طلبات خاصة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.specialRequests,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _specialRequestsController,
                    labelText: l10n.enterSpecialRequests,
                    maxLines: 3,
                    textInputAction: TextInputAction.newline,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExtrasTab(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.additionalServices,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.selectAdditionalServices,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.mediumGrey,
                ),
          ),

          const SizedBox(height: 20),

          // مقعد الأطفال
          Card(
            child: CheckboxListTile(
              title: Text(l10n.childSeat),
              subtitle: Text('25 ${l10n.aed} / ${l10n.day}'),
              secondary: const Icon(Icons.child_care),
              value: _childSeat,
              onChanged: (value) {
                setState(() {
                  _childSeat = value!;
                });
                _calculatePricing();
              },
            ),
          ),

          // نظام الملاحة
          Card(
            child: CheckboxListTile(
              title: Text(l10n.gpsNavigation),
              subtitle: Text('15 ${l10n.aed} / ${l10n.day}'),
              secondary: const Icon(Icons.navigation),
              value: _gpsNavigation,
              onChanged: (value) {
                setState(() {
                  _gpsNavigation = value!;
                });
                _calculatePricing();
              },
            ),
          ),

          // تأمين إضافي
          Card(
            child: CheckboxListTile(
              title: Text(l10n.additionalInsurance),
              subtitle: Text('50 ${l10n.aed} / ${l10n.day}'),
              secondary: const Icon(Icons.security),
              value: _additionalInsurance,
              onChanged: (value) {
                setState(() {
                  _additionalInsurance = value!;
                });
                _calculatePricing();
              },
            ),
          ),

          // مسافة غير محدودة
          Card(
            child: CheckboxListTile(
              title: Text(l10n.unlimitedMileage),
              subtitle: Text('30 ${l10n.aed} / ${l10n.day}'),
              secondary: const Icon(Icons.speed),
              value: _unlimitedMileage,
              onChanged: (value) {
                setState(() {
                  _unlimitedMileage = value!;
                });
                _calculatePricing();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewTab(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص الحجز
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.bookingSummary,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  if (_pickupDate != null && _returnDate != null) ...[
                    _buildSummaryRow(
                      l10n.pickupDate,
                      DateFormat('dd/MM/yyyy').format(_pickupDate!),
                    ),
                    _buildSummaryRow(
                      l10n.returnDate,
                      DateFormat('dd/MM/yyyy').format(_returnDate!),
                    ),
                    _buildSummaryRow(
                      l10n.rentalDuration,
                      '$_rentalDays ${l10n.days}',
                    ),
                  ],
                  _buildSummaryRow(l10n.pickupLocation, _pickupLocation),
                  _buildSummaryRow(l10n.returnLocation, _returnLocation),
                  if (_deliveryToLocation)
                    _buildSummaryRow(l10n.delivery, l10n.yes),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // ملخص السعر
          PriceSummaryCard(
            basePrice: widget.car.dailyPrice,
            taxes: _tax,
            serviceFee: 25.0, // Fixed service fee
            rentalDays: _rentalDays,
            carName: widget.car.name,
            deliveryFee: _deliveryFee,
            additionalServices: const <String, double>{}, // Empty for now
          ),

          const SizedBox(height: 16),

          // الشروط والأحكام
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.termsAndConditions,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    title: Text(l10n.iAgreeToTerms),
                    value: _acceptTerms,
                    onChanged: (value) {
                      setState(() {
                        _acceptTerms = value!;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                  CheckboxListTile(
                    title: Text(l10n.iAcceptCancellationPolicy),
                    value: _acceptCancellationPolicy,
                    onChanged: (value) {
                      setState(() {
                        _acceptCancellationPolicy = value!;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.mediumGrey,
                ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // ملخص السعر
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${_total.toStringAsFixed(0)} ${l10n.aed}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.royalBlue,
                        ),
                  ),
                  Text(
                    '+ ${widget.car.securityDeposit.toStringAsFixed(0)} ${l10n.deposit}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.mediumGrey,
                        ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // زر المتابعة
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _proceedToPayment,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  l10n.proceedToPayment,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// حوار إضافة سائق إضافي
class _AddDriverDialog extends StatefulWidget {
  final Function(Map<String, String>) onDriverAdded;

  const _AddDriverDialog({
    Key? key,
    required this.onDriverAdded,
  }) : super(key: key);

  @override
  _AddDriverDialogState createState() => _AddDriverDialogState();
}

class _AddDriverDialogState extends State<_AddDriverDialog> {
  final _nameController = TextEditingController();
  final _licenseController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _nameController.dispose();
    _licenseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return AlertDialog(
      title: Text(l10n.addDriver),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomTextField(
              controller: _nameController,
              labelText: l10n.driverName,
              prefixIcon: const Icon(Icons.person),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return l10n.fieldRequired;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _licenseController,
              labelText: l10n.driverLicense,
              prefixIcon: const Icon(Icons.credit_card),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return l10n.fieldRequired;
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onDriverAdded({
                'name': _nameController.text,
                'license': _licenseController.text,
              });
              Navigator.of(context).pop();
            }
          },
          child: Text(l10n.add),
        ),
      ],
    );
  }
}
