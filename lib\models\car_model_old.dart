import 'package:equatable/equatable.dart';

class CarModel extends Equatable {
  final String id;
  final String make; // العلامة التجارية
  final String model; // الموديل
  final int year;
  final String category; // economy, standard, premium, luxury, exotic
  final String fuelType; // gasoline, diesel, hybrid, electric
  final String transmission; // manual, automatic
  final int seats;
  final int doors;
  final String color;
  final String plateNumber;
  final List<String> imageUrls;
  final String thumbnailUrl;
  final double dailyRate;
  final double weeklyRate;
  final double monthlyRate;
  final double securityDeposit;
  final String description;
  final List<String> features; // GPS, Bluetooth, AC, etc.
  final double rating;
  final int totalReviews;
  final bool isAvailable;
  final CarStatus status;
  final LocationInfo? currentLocation;
  final String? pickupLocation;
  final String? returnLocation;
  final CarInsurance? insurance;
  final int mileage; // الأميال المقطوعة
  final DateTime? lastServiceDate;
  final DateTime? nextServiceDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CarModel({
    required this.id,
    required this.make,
    required this.model,
    required this.year,
    required this.category,
    required this.fuelType,
    required this.transmission,
    required this.seats,
    required this.doors,
    required this.color,
    required this.plateNumber,
    required this.imageUrls,
    required this.thumbnailUrl,
    required this.dailyRate,
    required this.weeklyRate,
    required this.monthlyRate,
    required this.securityDeposit,
    required this.description,
    required this.features,
    required this.rating,
    required this.totalReviews,
    required this.isAvailable,
    required this.status,
    this.currentLocation,
    this.pickupLocation,
    this.returnLocation,
    this.insurance,
    required this.mileage,
    this.lastServiceDate,
    this.nextServiceDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CarModel.fromJson(Map<String, dynamic> json) {
    return CarModel(
      id: json['id'] as String,
      make: json['make'] as String,
      model: json['model'] as String,
      year: json['year'] as int,
      category: json['category'] as String,
      fuelType: json['fuel_type'] as String,
      transmission: json['transmission'] as String,
      seats: json['seats'] as int,
      doors: json['doors'] as int,
      color: json['color'] as String,
      plateNumber: json['plate_number'] as String,
      imageUrls: List<String>.from(json['image_urls'] as List<dynamic>),
      thumbnailUrl: json['thumbnail_url'] as String,
      dailyRate: (json['daily_rate'] as num).toDouble(),
      weeklyRate: (json['weekly_rate'] as num).toDouble(),
      monthlyRate: (json['monthly_rate'] as num).toDouble(),
      securityDeposit: (json['security_deposit'] as num).toDouble(),
      description: json['description'] as String,
      features: List<String>.from(json['features'] as List<dynamic>),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalReviews: json['total_reviews'] as int? ?? 0,
      isAvailable: json['is_available'] as bool? ?? true,
      status: CarStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String? ?? 'available'),
        orElse: () => CarStatus.available,
      ),
      currentLocation: json['current_location'] != null
          ? LocationInfo.fromJson(
              json['current_location'] as Map<String, dynamic>)
          : null,
      pickupLocation: json['pickup_location'] as String?,
      returnLocation: json['return_location'] as String?,
      insurance: json['insurance'] != null
          ? CarInsurance.fromJson(json['insurance'] as Map<String, dynamic>)
          : null,
      mileage: json['mileage'] as int? ?? 0,
      lastServiceDate: json['last_service_date'] != null
          ? DateTime.parse(json['last_service_date'] as String)
          : null,
      nextServiceDate: json['next_service_date'] != null
          ? DateTime.parse(json['next_service_date'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'make': make,
      'model': model,
      'year': year,
      'category': category,
      'fuel_type': fuelType,
      'transmission': transmission,
      'seats': seats,
      'doors': doors,
      'color': color,
      'plate_number': plateNumber,
      'image_urls': imageUrls,
      'thumbnail_url': thumbnailUrl,
      'daily_rate': dailyRate,
      'weekly_rate': weeklyRate,
      'monthly_rate': monthlyRate,
      'security_deposit': securityDeposit,
      'description': description,
      'features': features,
      'rating': rating,
      'total_reviews': totalReviews,
      'is_available': isAvailable,
      'status': status.name,
      'current_location': currentLocation?.toJson(),
      'pickup_location': pickupLocation,
      'return_location': returnLocation,
      'insurance': insurance?.toJson(),
      'mileage': mileage,
      'last_service_date': lastServiceDate?.toIso8601String(),
      'next_service_date': nextServiceDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get displayName => '$make $model';
  String get fullName => '$year $make $model';

  CarModel copyWith({
    String? id,
    String? make,
    String? model,
    int? year,
    String? category,
    String? fuelType,
    String? transmission,
    int? seats,
    int? doors,
    String? color,
    String? plateNumber,
    List<String>? imageUrls,
    String? thumbnailUrl,
    double? dailyRate,
    double? weeklyRate,
    double? monthlyRate,
    double? securityDeposit,
    String? description,
    List<String>? features,
    double? rating,
    int? totalReviews,
    bool? isAvailable,
    CarStatus? status,
    LocationInfo? currentLocation,
    String? pickupLocation,
    String? returnLocation,
    CarInsurance? insurance,
    int? mileage,
    DateTime? lastServiceDate,
    DateTime? nextServiceDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CarModel(
      id: id ?? this.id,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      category: category ?? this.category,
      fuelType: fuelType ?? this.fuelType,
      transmission: transmission ?? this.transmission,
      seats: seats ?? this.seats,
      doors: doors ?? this.doors,
      color: color ?? this.color,
      plateNumber: plateNumber ?? this.plateNumber,
      imageUrls: imageUrls ?? this.imageUrls,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      dailyRate: dailyRate ?? this.dailyRate,
      weeklyRate: weeklyRate ?? this.weeklyRate,
      monthlyRate: monthlyRate ?? this.monthlyRate,
      securityDeposit: securityDeposit ?? this.securityDeposit,
      description: description ?? this.description,
      features: features ?? this.features,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      isAvailable: isAvailable ?? this.isAvailable,
      status: status ?? this.status,
      currentLocation: currentLocation ?? this.currentLocation,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      returnLocation: returnLocation ?? this.returnLocation,
      insurance: insurance ?? this.insurance,
      mileage: mileage ?? this.mileage,
      lastServiceDate: lastServiceDate ?? this.lastServiceDate,
      nextServiceDate: nextServiceDate ?? this.nextServiceDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        make,
        model,
        year,
        category,
        fuelType,
        transmission,
        seats,
        doors,
        color,
        plateNumber,
        imageUrls,
        thumbnailUrl,
        dailyRate,
        weeklyRate,
        monthlyRate,
        securityDeposit,
        description,
        features,
        rating,
        totalReviews,
        isAvailable,
        status,
        currentLocation,
        pickupLocation,
        returnLocation,
        insurance,
        mileage,
        lastServiceDate,
        nextServiceDate,
        createdAt,
        updatedAt,
      ];
}

enum CarStatus {
  available,
  rented,
  maintenance,
  outOfService,
}

enum CarCategory {
  economy,
  standard,
  premium,
  luxury,
  exotic,
}

class LocationInfo extends Equatable {
  final double latitude;
  final double longitude;
  final String? address;
  final String? city;
  final String? district;

  const LocationInfo({
    required this.latitude,
    required this.longitude,
    this.address,
    this.city,
    this.district,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String?,
      city: json['city'] as String?,
      district: json['district'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'district': district,
    };
  }

  @override
  List<Object?> get props => [latitude, longitude, address, city, district];
}

class CarInsurance extends Equatable {
  final String provider;
  final String policyNumber;
  final DateTime expiryDate;
  final double coverageAmount;
  final String type; // full, third_party

  const CarInsurance({
    required this.provider,
    required this.policyNumber,
    required this.expiryDate,
    required this.coverageAmount,
    required this.type,
  });

  factory CarInsurance.fromJson(Map<String, dynamic> json) {
    return CarInsurance(
      provider: json['provider'] as String,
      policyNumber: json['policy_number'] as String,
      expiryDate: DateTime.parse(json['expiry_date'] as String),
      coverageAmount: (json['coverage_amount'] as num).toDouble(),
      type: json['type'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'policy_number': policyNumber,
      'expiry_date': expiryDate.toIso8601String(),
      'coverage_amount': coverageAmount,
      'type': type,
    };
  }

  @override
  List<Object> get props =>
      [provider, policyNumber, expiryDate, coverageAmount, type];
}

// Extensions for display names
extension CarCategoryExtension on CarCategory {
  String get displayName {
    switch (this) {
      case CarCategory.economy:
        return 'Economy';
      case CarCategory.standard:
        return 'Standard';
      case CarCategory.premium:
        return 'Premium';
      case CarCategory.luxury:
        return 'Luxury';
      case CarCategory.exotic:
        return 'Exotic';
    }
  }

  String get displayNameArabic {
    switch (this) {
      case CarCategory.economy:
        return 'اقتصادية';
      case CarCategory.standard:
        return 'عادية';
      case CarCategory.premium:
        return 'متميزة';
      case CarCategory.luxury:
        return 'فاخرة';
      case CarCategory.exotic:
        return 'استثنائية';
    }
  }
}

extension CarStatusExtension on CarStatus {
  String get displayName {
    switch (this) {
      case CarStatus.available:
        return 'Available';
      case CarStatus.rented:
        return 'Rented';
      case CarStatus.maintenance:
        return 'Maintenance';
      case CarStatus.outOfService:
        return 'Out of Service';
    }
  }

  String get displayNameArabic {
    switch (this) {
      case CarStatus.available:
        return 'متاحة';
      case CarStatus.rented:
        return 'مؤجرة';
      case CarStatus.maintenance:
        return 'صيانة';
      case CarStatus.outOfService:
        return 'خارج الخدمة';
    }
  }
}
