import 'package:flutter/foundation.dart';
import '../models/booking.dart';
import '../models/car.dart';

class BookingProvider with ChangeNotifier {
  List<Booking> _bookings = [];
  bool _isLoading = false;
  String? _error;
  Booking? _currentBooking;

  // Current booking flow data
  Car? _selectedCar;
  DateTime? _pickupDate;
  DateTime? _dropoffDate;
  String _pickupLocation = '';
  String _dropoffLocation = '';
  Map<String, dynamic> _additionalServices = {};
  double _totalPrice = 0.0;
  String _paymentMethodId = '';

  // Getters
  List<Booking> get bookings => _bookings;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Booking? get currentBooking => _currentBooking;

  // Booking flow getters
  Car? get selectedCar => _selectedCar;
  DateTime? get pickupDate => _pickupDate;
  DateTime? get dropoffDate => _dropoffDate;
  String get pickupLocation => _pickupLocation;
  String get dropoffLocation => _dropoffLocation;
  Map<String, dynamic> get additionalServices => _additionalServices;
  double get totalPrice => _totalPrice;
  String get paymentMethodId => _paymentMethodId;

  List<Booking> get activeBookings =>
      _bookings.where((booking) => booking.isActive).toList();

  List<Booking> get pastBookings => _bookings
      .where((booking) => booking.isCompleted || booking.isCancelled)
      .toList();

  List<Booking> get upcomingBookings => _bookings
      .where((booking) =>
          booking.status == BookingStatus.confirmed &&
          booking.pickupDate.isAfter(DateTime.now()))
      .toList();

  // Methods
  Future<void> loadBookings() async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      _bookings = []; // Replace with actual API call
      _error = null;
    } catch (e) {
      _error = e.toString();
    }
    _setLoading(false);
  }

  Future<void> refreshBookings() async {
    await loadBookings();
  }

  // Booking flow methods
  void setSelectedCar(Car car) {
    _selectedCar = car;
    _calculateTotalPrice();
    notifyListeners();
  }

  void setPickupDate(DateTime date) {
    _pickupDate = date;
    _calculateTotalPrice();
    notifyListeners();
  }

  void setDropoffDate(DateTime date) {
    _dropoffDate = date;
    _calculateTotalPrice();
    notifyListeners();
  }

  void setPickupLocation(String location) {
    _pickupLocation = location;
    notifyListeners();
  }

  void setDropoffLocation(String location) {
    _dropoffLocation = location;
    notifyListeners();
  }

  void setAdditionalServices(Map<String, dynamic> services) {
    _additionalServices = services;
    _calculateTotalPrice();
    notifyListeners();
  }

  void addAdditionalService(String key, dynamic value) {
    _additionalServices[key] = value;
    _calculateTotalPrice();
    notifyListeners();
  }

  void removeAdditionalService(String key) {
    _additionalServices.remove(key);
    _calculateTotalPrice();
    notifyListeners();
  }

  void setPaymentMethodId(String id) {
    _paymentMethodId = id;
    notifyListeners();
  }

  void clearBookingFlow() {
    _selectedCar = null;
    _pickupDate = null;
    _dropoffDate = null;
    _pickupLocation = '';
    _dropoffLocation = '';
    _additionalServices.clear();
    _totalPrice = 0.0;
    _paymentMethodId = '';
    notifyListeners();
  }

  Future<String?> createBooking() async {
    if (_selectedCar == null ||
        _pickupDate == null ||
        _dropoffDate == null ||
        _pickupLocation.isEmpty ||
        _dropoffLocation.isEmpty) {
      return 'Missing required booking information';
    }

    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      final booking = Booking(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'current_user_id', // Replace with actual user ID
        carId: _selectedCar!.id,
        car: _selectedCar,
        pickupDate: _pickupDate!,
        dropoffDate: _dropoffDate!,
        pickupLocation: _pickupLocation,
        dropoffLocation: _dropoffLocation,
        totalPrice: _totalPrice,
        deposit: _totalPrice * 0.2, // 20% deposit
        taxes: _totalPrice * 0.05, // 5% tax
        serviceFee: 50.0,
        status: BookingStatus.pending,
        paymentMethodId: _paymentMethodId,
        additionalServices: _additionalServices,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _bookings.add(booking);
      _currentBooking = booking;
      _error = null;
      return null; // Success
    } catch (e) {
      _error = e.toString();
      return _error;
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> confirmBooking(String bookingId) async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final bookingIndex = _bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex != -1) {
        _bookings[bookingIndex] = _bookings[bookingIndex].copyWith(
          status: BookingStatus.confirmed,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }

      return null; // Success
    } catch (e) {
      _error = e.toString();
      return _error;
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> cancelBooking(String bookingId, {String? reason}) async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final bookingIndex = _bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex != -1) {
        _bookings[bookingIndex] = _bookings[bookingIndex].copyWith(
          status: BookingStatus.cancelled,
          notes: reason,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }

      return null; // Success
    } catch (e) {
      _error = e.toString();
      return _error;
    } finally {
      _setLoading(false);
    }
  }

  Future<Booking?> getBookingById(String id) async {
    try {
      return _bookings.firstWhere((booking) => booking.id == id);
    } catch (e) {
      return null;
    }
  }

  void _calculateTotalPrice() {
    if (_selectedCar == null || _pickupDate == null || _dropoffDate == null) {
      _totalPrice = 0.0;
      return;
    }

    final days = _dropoffDate!.difference(_pickupDate!).inDays;
    final rentalDays = days == 0 ? 1 : days;

    double price = _selectedCar!.pricePerDay * rentalDays;

    // Add additional services cost
    for (final service in _additionalServices.entries) {
      if (service.value is Map && service.value['selected'] == true) {
        price += (service.value['price'] as num?)?.toDouble() ?? 0.0;
      }
    }

    _totalPrice = price;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  int get rentalDays {
    if (_pickupDate == null || _dropoffDate == null) return 0;
    final days = _dropoffDate!.difference(_pickupDate!).inDays;
    return days == 0 ? 1 : days;
  }

  double get basePrice {
    if (_selectedCar == null) return 0.0;
    return _selectedCar!.pricePerDay * rentalDays;
  }

  double get additionalServicesTotal {
    double total = 0.0;
    for (final service in _additionalServices.entries) {
      if (service.value is Map && service.value['selected'] == true) {
        total += (service.value['price'] as num?)?.toDouble() ?? 0.0;
      }
    }
    return total;
  }

  double get taxes {
    return _totalPrice * 0.05; // 5% tax
  }

  double get deposit {
    return _totalPrice * 0.2; // 20% deposit
  }

  double get serviceFee => 50.0;

  // Save signature method
  Future<bool> saveSignature(List<int> signatureBytes, bool isPickup) async {
    try {
      // Simulate API call to save signature
      await Future.delayed(const Duration(seconds: 1));

      // Here you would typically upload the signature to your backend
      // For now, we'll just simulate success
      return true;
    } catch (e) {
      return false;
    }
  }
}
