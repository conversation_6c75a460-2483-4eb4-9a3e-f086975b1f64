import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';

import '../../providers/language_provider.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../../widgets/simple_app_logo.dart';
import '../auth/login_screen.dart';

class OnboardingScreen extends StatefulWidget {
  @override
  _OnboardingScreenState createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      image: '/placeholder.svg?height=300&width=300',
      titleAr: 'أفضل السيارات في دبي',
      titleEn: 'Best Cars in Dubai',
      descriptionAr:
          'اختر من مجموعة واسعة من السيارات الفاخرة والاقتصادية المتاحة في جميع أنحاء دبي',
      descriptionEn:
          'Choose from a wide range of luxury and economy cars available throughout Dubai',
    ),
    OnboardingPage(
      image: '/placeholder.svg?height=300&width=300',
      titleAr: 'حجز سهل وسريع',
      titleEn: 'Easy & Quick Booking',
      descriptionAr:
          'احجز سيارتك في دقائق معدودة مع نظام الحجز الذكي والدفع الآمن',
      descriptionEn:
          'Book your car in minutes with smart booking system and secure payment',
    ),
    OnboardingPage(
      image: '/placeholder.svg?height=300&width=300',
      titleAr: 'دعم على مدار الساعة',
      titleEn: '24/7 Customer Support',
      descriptionAr:
          'فريق الدعم متاح على مدار الساعة لمساعدتك في أي وقت تحتاجه',
      descriptionEn:
          'Our support team is available 24/7 to help you whenever you need',
    ),
    OnboardingPage(
      image: '/placeholder.svg?height=300&width=300',
      titleAr: 'توصيل بدون تلامس',
      titleEn: 'Contactless Delivery',
      descriptionAr:
          'استلم سيارتك في أي مكان في دبي مع خدمة التوصيل الآمنة والمريحة',
      descriptionEn:
          'Get your car delivered anywhere in Dubai with safe and convenient service',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // شريط علوي مع زر تخطي واختيار اللغة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // زر اختيار اللغة
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.borderGrey),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: TextButton.icon(
                      onPressed: () => _showLanguageDialog(),
                      icon: const Icon(Icons.language, size: 18),
                      label: Text(
                        languageProvider.isArabic ? 'العربية' : 'English',
                        style: const TextStyle(fontSize: 12),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  ),

                  // زر تخطي
                  TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      l10n.skip,
                      style: TextStyle(
                        color: AppTheme.mediumGrey,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // محتوى الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  final page = _pages[index];
                  return Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // الصورة
                        Container(
                          width: 280,
                          height: 280,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.royalBlue.withOpacity(0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: Image.network(
                              page.image,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.9),
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: SimpleAppLogo(
                                    size: 100,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),

                        const SizedBox(height: 40),

                        // العنوان
                        Text(
                          languageProvider.isArabic
                              ? page.titleAr
                              : page.titleEn,
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppTheme.darkGrey,
                              ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 16),

                        // الوصف
                        Text(
                          languageProvider.isArabic
                              ? page.descriptionAr
                              : page.descriptionEn,
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: AppTheme.mediumGrey,
                                    height: 1.5,
                                  ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // مؤشرات الصفحات والأزرار
            Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  // مؤشرات الصفحات
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (index) => AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: _currentPage == index ? 24 : 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _currentPage == index
                              ? AppTheme.royalBlue
                              : AppTheme.borderGrey,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // الأزرار
                  Row(
                    children: [
                      // زر السابق
                      if (_currentPage > 0)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _previousPage,
                            child: Text(l10n.previous),
                          ),
                        ),

                      if (_currentPage > 0) const SizedBox(width: 16),

                      // زر التالي أو البدء
                      Expanded(
                        flex: _currentPage == 0 ? 1 : 1,
                        child: ElevatedButton(
                          onPressed: _currentPage == _pages.length - 1
                              ? _finishOnboarding
                              : _nextPage,
                          child: Text(
                            _currentPage == _pages.length - 1
                                ? l10n.getStarted
                                : l10n.next,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog() {
    final languageProvider =
        Provider.of<LanguageProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.selectLanguage),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Text('🇦🇪', style: TextStyle(fontSize: 24)),
              title: const Text('العربية'),
              trailing: languageProvider.isArabic
                  ? const Icon(Icons.check, color: AppTheme.royalBlue)
                  : null,
              onTap: () {
                languageProvider.setLanguage('ar');
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              leading: const Text('🇺🇸', style: TextStyle(fontSize: 24)),
              title: const Text('English'),
              trailing: !languageProvider.isArabic
                  ? const Icon(Icons.check, color: AppTheme.royalBlue)
                  : null,
              onTap: () {
                languageProvider.setLanguage('en');
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _skipOnboarding() async {
    await _markOnboardingComplete();
    _navigateToLogin();
  }

  void _finishOnboarding() async {
    await _markOnboardingComplete();
    _navigateToLogin();
  }

  Future<void> _markOnboardingComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_seen_onboarding', true);
  }

  void _navigateToLogin() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => LoginScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

class OnboardingPage {
  final String image;
  final String titleAr;
  final String titleEn;
  final String descriptionAr;
  final String descriptionEn;

  OnboardingPage({
    required this.image,
    required this.titleAr,
    required this.titleEn,
    required this.descriptionAr,
    required this.descriptionEn,
  });
}
