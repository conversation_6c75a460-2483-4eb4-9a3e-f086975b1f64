import 'package:equatable/equatable.dart';
import 'car_model.dart';
import 'user_model.dart';

class BookingModel extends Equatable {
  final String id;
  final String userId;
  final String carId;
  final UserModel? user;
  final CarModel? car;
  final DateTime pickupDateTime;
  final DateTime returnDateTime;
  final LocationInfo pickupLocation;
  final LocationInfo returnLocation;
  final BookingStatus status;
  final PaymentInfo? payment;
  final PricingDetails pricing;
  final String? signatureUrl;
  final String? contractUrl;
  final List<String> documentUrls;
  final String? specialRequests;
  final String? notes;
  final CancellationInfo? cancellation;
  final List<BookingStatusHistory> statusHistory;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BookingModel({
    required this.id,
    required this.userId,
    required this.carId,
    this.user,
    this.car,
    required this.pickupDateTime,
    required this.returnDateTime,
    required this.pickupLocation,
    required this.returnLocation,
    required this.status,
    this.payment,
    required this.pricing,
    this.signatureUrl,
    this.contractUrl,
    required this.documentUrls,
    this.specialRequests,
    this.notes,
    this.cancellation,
    required this.statusHistory,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      carId: json['car_id'] as String,
      user: json['user'] != null
          ? UserModel.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      car: json['car'] != null
          ? CarModel.fromJson(json['car'] as Map<String, dynamic>)
          : null,
      pickupDateTime: DateTime.parse(json['pickup_date_time'] as String),
      returnDateTime: DateTime.parse(json['return_date_time'] as String),
      pickupLocation: LocationInfo.fromJson(
          json['pickup_location'] as Map<String, dynamic>),
      returnLocation: LocationInfo.fromJson(
          json['return_location'] as Map<String, dynamic>),
      status: BookingStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String),
        orElse: () => BookingStatus.pending,
      ),
      payment: json['payment'] != null
          ? PaymentInfo.fromJson(json['payment'] as Map<String, dynamic>)
          : null,
      pricing: PricingDetails.fromJson(json['pricing'] as Map<String, dynamic>),
      signatureUrl: json['signature_url'] as String?,
      contractUrl: json['contract_url'] as String?,
      documentUrls:
          List<String>.from(json['document_urls'] as List<dynamic>? ?? []),
      specialRequests: json['special_requests'] as String?,
      notes: json['notes'] as String?,
      cancellation: json['cancellation'] != null
          ? CancellationInfo.fromJson(
              json['cancellation'] as Map<String, dynamic>)
          : null,
      statusHistory: (json['status_history'] as List<dynamic>?)
              ?.map((e) =>
                  BookingStatusHistory.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'car_id': carId,
      'user': user?.toJson(),
      'car': car?.toJson(),
      'pickup_date_time': pickupDateTime.toIso8601String(),
      'return_date_time': returnDateTime.toIso8601String(),
      'pickup_location': pickupLocation.toJson(),
      'return_location': returnLocation.toJson(),
      'status': status.name,
      'payment': payment?.toJson(),
      'pricing': pricing.toJson(),
      'signature_url': signatureUrl,
      'contract_url': contractUrl,
      'document_urls': documentUrls,
      'special_requests': specialRequests,
      'notes': notes,
      'cancellation': cancellation?.toJson(),
      'status_history': statusHistory.map((e) => e.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Duration get duration => returnDateTime.difference(pickupDateTime);
  int get totalDays => duration.inDays;
  int get totalHours => duration.inHours;

  bool get canBeCancelled {
    return status == BookingStatus.pending ||
        status == BookingStatus.confirmed ||
        (status == BookingStatus.upcoming &&
            DateTime.now()
                .isBefore(pickupDateTime.subtract(const Duration(hours: 24))));
  }

  bool get isActive {
    return status == BookingStatus.active || status == BookingStatus.ongoing;
  }

  bool get isCompleted {
    return status == BookingStatus.completed;
  }

  bool get isCancelled {
    return status == BookingStatus.cancelled;
  }

  BookingModel copyWith({
    String? id,
    String? userId,
    String? carId,
    UserModel? user,
    CarModel? car,
    DateTime? pickupDateTime,
    DateTime? returnDateTime,
    LocationInfo? pickupLocation,
    LocationInfo? returnLocation,
    BookingStatus? status,
    PaymentInfo? payment,
    PricingDetails? pricing,
    String? signatureUrl,
    String? contractUrl,
    List<String>? documentUrls,
    String? specialRequests,
    String? notes,
    CancellationInfo? cancellation,
    List<BookingStatusHistory>? statusHistory,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookingModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      carId: carId ?? this.carId,
      user: user ?? this.user,
      car: car ?? this.car,
      pickupDateTime: pickupDateTime ?? this.pickupDateTime,
      returnDateTime: returnDateTime ?? this.returnDateTime,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      returnLocation: returnLocation ?? this.returnLocation,
      status: status ?? this.status,
      payment: payment ?? this.payment,
      pricing: pricing ?? this.pricing,
      signatureUrl: signatureUrl ?? this.signatureUrl,
      contractUrl: contractUrl ?? this.contractUrl,
      documentUrls: documentUrls ?? this.documentUrls,
      specialRequests: specialRequests ?? this.specialRequests,
      notes: notes ?? this.notes,
      cancellation: cancellation ?? this.cancellation,
      statusHistory: statusHistory ?? this.statusHistory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        carId,
        user,
        car,
        pickupDateTime,
        returnDateTime,
        pickupLocation,
        returnLocation,
        status,
        payment,
        pricing,
        signatureUrl,
        contractUrl,
        documentUrls,
        specialRequests,
        notes,
        cancellation,
        statusHistory,
        createdAt,
        updatedAt,
      ];
}

enum BookingStatus {
  pending, // في الانتظار
  confirmed, // مؤكد
  upcoming, // قادم
  active, // نشط
  ongoing, // جاري
  completed, // مكتمل
  cancelled, // ملغي
  expired, // منتهي الصلاحية
}

class PricingDetails extends Equatable {
  final double baseRate;
  final int days;
  final double subtotal;
  final double deliveryFee;
  final double securityDeposit;
  final double tax;
  final double discount;
  final double total;
  final String currency;
  final String? discountCode;

  const PricingDetails({
    required this.baseRate,
    required this.days,
    required this.subtotal,
    required this.deliveryFee,
    required this.securityDeposit,
    required this.tax,
    required this.discount,
    required this.total,
    required this.currency,
    this.discountCode,
  });

  factory PricingDetails.fromJson(Map<String, dynamic> json) {
    return PricingDetails(
      baseRate: (json['base_rate'] as num).toDouble(),
      days: json['days'] as int,
      subtotal: (json['subtotal'] as num).toDouble(),
      deliveryFee: (json['delivery_fee'] as num).toDouble(),
      securityDeposit: (json['security_deposit'] as num).toDouble(),
      tax: (json['tax'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
      currency: json['currency'] as String,
      discountCode: json['discount_code'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'base_rate': baseRate,
      'days': days,
      'subtotal': subtotal,
      'delivery_fee': deliveryFee,
      'security_deposit': securityDeposit,
      'tax': tax,
      'discount': discount,
      'total': total,
      'currency': currency,
      'discount_code': discountCode,
    };
  }

  @override
  List<Object?> get props => [
        baseRate,
        days,
        subtotal,
        deliveryFee,
        securityDeposit,
        tax,
        discount,
        total,
        currency,
        discountCode,
      ];
}

class PaymentInfo extends Equatable {
  final String id;
  final String method;
  final String status;
  final double amount;
  final String currency;
  final String? transactionId;
  final String? stripePaymentIntentId;
  final DateTime? paidAt;
  final Map<String, dynamic>? metadata;

  const PaymentInfo({
    required this.id,
    required this.method,
    required this.status,
    required this.amount,
    required this.currency,
    this.transactionId,
    this.stripePaymentIntentId,
    this.paidAt,
    this.metadata,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      id: json['id'] as String,
      method: json['method'] as String,
      status: json['status'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      transactionId: json['transaction_id'] as String?,
      stripePaymentIntentId: json['stripe_payment_intent_id'] as String?,
      paidAt: json['paid_at'] != null
          ? DateTime.parse(json['paid_at'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'method': method,
      'status': status,
      'amount': amount,
      'currency': currency,
      'transaction_id': transactionId,
      'stripe_payment_intent_id': stripePaymentIntentId,
      'paid_at': paidAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [
        id,
        method,
        status,
        amount,
        currency,
        transactionId,
        stripePaymentIntentId,
        paidAt,
        metadata,
      ];
}

class CancellationInfo extends Equatable {
  final String reason;
  final DateTime cancelledAt;
  final String? cancelledBy;
  final double refundAmount;
  final String refundStatus;

  const CancellationInfo({
    required this.reason,
    required this.cancelledAt,
    this.cancelledBy,
    required this.refundAmount,
    required this.refundStatus,
  });

  factory CancellationInfo.fromJson(Map<String, dynamic> json) {
    return CancellationInfo(
      reason: json['reason'] as String,
      cancelledAt: DateTime.parse(json['cancelled_at'] as String),
      cancelledBy: json['cancelled_by'] as String?,
      refundAmount: (json['refund_amount'] as num).toDouble(),
      refundStatus: json['refund_status'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reason': reason,
      'cancelled_at': cancelledAt.toIso8601String(),
      'cancelled_by': cancelledBy,
      'refund_amount': refundAmount,
      'refund_status': refundStatus,
    };
  }

  @override
  List<Object?> get props =>
      [reason, cancelledAt, cancelledBy, refundAmount, refundStatus];
}

class BookingStatusHistory extends Equatable {
  final BookingStatus status;
  final DateTime timestamp;
  final String? note;

  const BookingStatusHistory({
    required this.status,
    required this.timestamp,
    this.note,
  });

  factory BookingStatusHistory.fromJson(Map<String, dynamic> json) {
    return BookingStatusHistory(
      status: BookingStatus.values.firstWhere((e) => e.name == json['status']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      note: json['note'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status.name,
      'timestamp': timestamp.toIso8601String(),
      'note': note,
    };
  }

  @override
  List<Object?> get props => [status, timestamp, note];
}

// Extensions
extension BookingStatusExtension on BookingStatus {
  String get displayName {
    switch (this) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.upcoming:
        return 'Upcoming';
      case BookingStatus.active:
        return 'Active';
      case BookingStatus.ongoing:
        return 'Ongoing';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.expired:
        return 'Expired';
    }
  }

  String get displayNameArabic {
    switch (this) {
      case BookingStatus.pending:
        return 'قيد المعالجة';
      case BookingStatus.confirmed:
        return 'مؤكد';
      case BookingStatus.upcoming:
        return 'قادم';
      case BookingStatus.active:
        return 'نشط';
      case BookingStatus.ongoing:
        return 'جاري';
      case BookingStatus.completed:
        return 'مكتمل';
      case BookingStatus.cancelled:
        return 'ملغي';
      case BookingStatus.expired:
        return 'منتهي الصلاحية';
    }
  }
}
