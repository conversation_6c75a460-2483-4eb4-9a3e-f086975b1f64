-- تحديث ملف SQL الرئيسي لإضافة دعم Stripe
-- يتم تشغيل هذا بعد rental_extensions_schema.sql

-- إضافة أعمدة Stripe إلى جدول المدفوعات الرئيسي إذا لم تكن موجودة
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='payments' AND column_name='stripe_payment_intent_id') THEN
    ALTER TABLE payments ADD COLUMN stripe_payment_intent_id TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='payments' AND column_name='stripe_customer_id') THEN
    ALTER TABLE payments ADD COLUMN stripe_customer_id TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='payments' AND column_name='stripe_payment_method_id') THEN
    ALTER TABLE payments ADD COLUMN stripe_payment_method_id TEXT;
  END IF;
END $$;

-- إضافة معلومات Stripe للمستخدمين
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='stripe_customer_id') THEN
    ALTER TABLE profiles ADD COLUMN stripe_customer_id TEXT UNIQUE;
  END IF;
END $$;

-- جدول لحفظ طرق الدفع المحفوظة للمستخدمين
CREATE TABLE IF NOT EXISTS user_payment_methods (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  
  -- معلومات Stripe
  stripe_payment_method_id TEXT NOT NULL,
  stripe_customer_id TEXT NOT NULL,
  
  -- معلومات البطاقة (آمنة)
  card_brand TEXT, -- visa, mastercard, etc.
  card_last4 TEXT, -- آخر 4 أرقام
  card_exp_month INTEGER,
  card_exp_year INTEGER,
  card_funding TEXT, -- credit, debit, prepaid
  card_country TEXT,
  
  -- الإعدادات
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  nickname TEXT, -- اسم مستعار للبطاقة
  
  -- تواريخ النظام
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_used_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_user_payment_methods_user_id ON user_payment_methods (user_id);
CREATE INDEX idx_user_payment_methods_stripe_id ON user_payment_methods (stripe_payment_method_id);
CREATE UNIQUE INDEX idx_user_payment_methods_default ON user_payment_methods (user_id) WHERE is_default = true;

-- جدول سجل معاملات Stripe
CREATE TABLE IF NOT EXISTS stripe_webhooks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  stripe_event_id TEXT NOT NULL UNIQUE,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL,
  
  -- معلومات المعالجة
  processed BOOLEAN DEFAULT false,
  processed_at TIMESTAMP WITH TIME ZONE,
  processing_error TEXT,
  processing_attempts INTEGER DEFAULT 0,
  
  -- معرفات مرتبطة
  related_extension_id UUID REFERENCES rental_extensions(id),
  related_booking_id UUID REFERENCES bookings(id),
  related_user_id UUID REFERENCES profiles(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_stripe_webhooks_event_id ON stripe_webhooks (stripe_event_id);
CREATE INDEX idx_stripe_webhooks_event_type ON stripe_webhooks (event_type);
CREATE INDEX idx_stripe_webhooks_processed ON stripe_webhooks (processed);
CREATE INDEX idx_stripe_webhooks_created_at ON stripe_webhooks (created_at);

-- دالة لمعالجة webhooks من Stripe
CREATE OR REPLACE FUNCTION process_stripe_webhook(
  p_event_id TEXT,
  p_event_type TEXT,
  p_event_data JSONB
) RETURNS BOOLEAN AS $$
DECLARE
  v_webhook_id UUID;
  v_payment_intent_id TEXT;
  v_extension_id UUID;
BEGIN
  -- إدراج سجل الـ webhook
  INSERT INTO stripe_webhooks (stripe_event_id, event_type, event_data)
  VALUES (p_event_id, p_event_type, p_event_data)
  ON CONFLICT (stripe_event_id) DO NOTHING
  RETURNING id INTO v_webhook_id;
  
  -- إذا كان الحدث موجود مسبقاً، إرجاع true
  IF v_webhook_id IS NULL THEN
    RETURN true;
  END IF;
  
  -- معالجة الحدث حسب النوع
  CASE p_event_type
    WHEN 'payment_intent.succeeded' THEN
      v_payment_intent_id := p_event_data->>'id';
      
      -- تحديث حالة التمديد
      UPDATE rental_extensions 
      SET 
        payment_status = 'paid',
        status = CASE WHEN status = 'pending' THEN 'paid' ELSE status END,
        updated_at = NOW()
      WHERE payment_intent_id = v_payment_intent_id
      RETURNING id INTO v_extension_id;
      
      -- تحديث معلومات الـ webhook
      UPDATE stripe_webhooks 
      SET 
        related_extension_id = v_extension_id,
        processed = true,
        processed_at = NOW()
      WHERE id = v_webhook_id;
      
    WHEN 'payment_intent.payment_failed' THEN
      v_payment_intent_id := p_event_data->>'id';
      
      UPDATE rental_extensions 
      SET 
        payment_status = 'failed',
        status = 'cancelled',
        updated_at = NOW()
      WHERE payment_intent_id = v_payment_intent_id
      RETURNING id INTO v_extension_id;
      
      UPDATE stripe_webhooks 
      SET 
        related_extension_id = v_extension_id,
        processed = true,
        processed_at = NOW()
      WHERE id = v_webhook_id;
      
    ELSE
      -- أحداث أخرى - تحديد كمعالجة
      UPDATE stripe_webhooks 
      SET 
        processed = true,
        processed_at = NOW()
      WHERE id = v_webhook_id;
  END CASE;
  
  RETURN true;
  
EXCEPTION WHEN OTHERS THEN
  -- تسجيل الخطأ
  UPDATE stripe_webhooks 
  SET 
    processing_error = SQLERRM,
    processing_attempts = processing_attempts + 1
  WHERE id = v_webhook_id;
  
  RETURN false;
END;
$$ LANGUAGE plpgsql;

-- إعدادات أمان لجداول Stripe
ALTER TABLE user_payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_webhooks ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان
CREATE POLICY "Users can view own payment methods" ON user_payment_methods 
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment methods" ON user_payment_methods 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own payment methods" ON user_payment_methods 
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own payment methods" ON user_payment_methods 
  FOR DELETE USING (auth.uid() = user_id);

-- سياسة للـ webhooks (إدارية فقط)
CREATE POLICY "Admin only access to webhooks" ON stripe_webhooks 
  FOR ALL USING (false); -- سيتم إدارتها من الخادم فقط

-- دالة للتحقق من البطاقة الافتراضية الوحيدة
CREATE OR REPLACE FUNCTION ensure_single_default_payment_method()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_default = true THEN
    -- إلغاء تحديد البطاقات الأخرى كافتراضية لنفس المستخدم
    UPDATE user_payment_methods 
    SET is_default = false 
    WHERE user_id = NEW.user_id AND id != NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفز
CREATE TRIGGER ensure_single_default_payment_method_trigger
  BEFORE INSERT OR UPDATE ON user_payment_methods
  FOR EACH ROW EXECUTE FUNCTION ensure_single_default_payment_method();

-- إضافة إعدادات Stripe للنظام
INSERT INTO system_settings (key, value_ar, value_en, category, is_active) VALUES
('stripe_publishable_key', 'pk_test_...', 'pk_test_...', 'payments', true),
('stripe_webhook_secret', 'whsec_...', 'whsec_...', 'payments', true),
('stripe_currency', 'aed', 'aed', 'payments', true),
('payment_success_url', 'https://app.com/payment-success', 'https://app.com/payment-success', 'payments', true),
('payment_cancel_url', 'https://app.com/payment-cancel', 'https://app.com/payment-cancel', 'payments', true)
ON CONFLICT (key) DO NOTHING;

COMMENT ON TABLE user_payment_methods IS 'طرق الدفع المحفوظة للمستخدمين';
COMMENT ON TABLE stripe_webhooks IS 'سجل أحداث Stripe الواردة';
COMMENT ON COLUMN user_payment_methods.stripe_payment_method_id IS 'معرف طريقة الدفع في Stripe';
COMMENT ON COLUMN stripe_webhooks.stripe_event_id IS 'معرف الحدث الفريد من Stripe';
