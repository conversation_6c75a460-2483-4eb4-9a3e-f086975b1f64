import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  // Basic
  String get appTitle;
  String get welcome;
  String get welcomeBack;
  String get hello;
  String get user;
  String get findYourPerfectCar;

  // Authentication
  String get login;
  String get register;
  String get createAccount;
  String get joinUs;
  String get email;
  String get password;
  String get confirmPassword;
  String get fullName;
  String get phone;
  String get phoneNumber;
  String get dateOfBirth;
  String get nationality;
  String get forgotPassword;
  String get loginWithGoogle;
  String get signUpWithGoogle;
  String get orContinueWith;
  String get orSignUpWith;
  String get dontHaveAccount;
  String get alreadyHaveAccount;
  String get signUp;
  String get signIn;

  // Navigation
  String get home;
  String get cars;
  String get bookings;
  String get profile;
  String get notifications;

  // Common
  String get cancel;
  String get ok;
  String get save;
  String get delete;
  String get edit;
  String get update;
  String get loading;
  String get error;
  String get success;
  String get warning;
  String get info;
  String get search;
  String get filter;
  String get sort;
  String get clear;
  String get apply;
  String get reset;
  String get back;
  String get next;
  String get previous;
  String get skip;
  String get continue_;
  String get finish;
  String get done;
  String get close;
  String get open;
  String get view;
  String get details;
  String get more;
  String get less;
  String get showAll;
  String get showLess;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }
  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue on GitHub with a reproducible '
      'sample app and the gen-l10n configuration that was used.');
}
