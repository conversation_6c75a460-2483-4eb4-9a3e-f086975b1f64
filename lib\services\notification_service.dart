import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/notification_model.dart';

class NotificationService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get user notifications
  Future<List<NotificationModel>> getUserNotifications({
    required String userId,
    int? limit,
    int? offset,
    bool? unreadOnly,
  }) async {
    try {
      dynamic query = _supabase
          .from('notifications')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      if (unreadOnly == true) {
        // Use generic filter to avoid typing issues across versions
        query = query.filter('is_read', 'eq', false);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await query;

      return (response as List)
          .map((json) => NotificationModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load notifications: $e');
    }
  }

  // Get unread notifications count
  Future<int> getUnreadCount(String userId) async {
    try {
      final data = await _supabase
          .from('notifications')
          .select('id')
          .eq('user_id', userId)
          .eq('is_read', false);

      return (data as List).length;
    } catch (e) {
      throw Exception('Failed to get unread count: $e');
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _supabase.from('notifications').update({
        'is_read': true,
        'read_at': DateTime.now().toIso8601String(),
      }).eq('id', notificationId);
    } catch (e) {
      throw Exception('Failed to mark as read: $e');
    }
  }

  // Mark all user notifications as read
  Future<void> markAllAsRead(String userId) async {
    try {
      await _supabase
          .from('notifications')
          .update({
            'is_read': true,
            'read_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('is_read', false);
    } catch (e) {
      throw Exception('Failed to mark all as read: $e');
    }
  }

  // Pin notification
  Future<void> pinNotification(String notificationId) async {
    try {
      await _supabase
          .from('notifications')
          .update({'is_pinned': true}).eq('id', notificationId);
    } catch (e) {
      throw Exception('Failed to pin notification: $e');
    }
  }

  // Unpin notification
  Future<void> unpinNotification(String notificationId) async {
    try {
      await _supabase
          .from('notifications')
          .update({'is_pinned': false}).eq('id', notificationId);
    } catch (e) {
      throw Exception('Failed to unpin notification: $e');
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _supabase.from('notifications').delete().eq('id', notificationId);
    } catch (e) {
      throw Exception('Failed to delete notification: $e');
    }
  }

  // Create notification (for admin/system use)
  Future<NotificationModel> createNotification({
    required String userId,
    required String title,
    required String message,
    required NotificationType type,
    NotificationPriority priority = NotificationPriority.normal,
    String? imageUrl,
    String? actionUrl,
    Map<String, dynamic>? metadata,
    DateTime? expiresAt,
  }) async {
    try {
      final notificationData = {
        'user_id': userId,
        'title': title,
        'message': message,
        'type': type.name,
        'priority': priority.name,
        'image_url': imageUrl,
        'action_url': actionUrl,
        'metadata': metadata,
        'expires_at': expiresAt?.toIso8601String(),
      };

      final response = await _supabase
          .from('notifications')
          .insert(notificationData)
          .select()
          .single();

      return NotificationModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create notification: $e');
    }
  }

  // Get notifications by type
  Future<List<NotificationModel>> getNotificationsByType({
    required String userId,
    required NotificationType type,
    int? limit,
  }) async {
    try {
      var query = _supabase
          .from('notifications')
          .select()
          .eq('user_id', userId)
          .eq('type', type.name)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return (response as List)
          .map((json) => NotificationModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load notifications by type: $e');
    }
  }

  // Clear expired notifications
  Future<void> clearExpiredNotifications(String userId) async {
    try {
      await _supabase
          .from('notifications')
          .delete()
          .eq('user_id', userId)
          .lt('expires_at', DateTime.now().toIso8601String());
    } catch (e) {
      throw Exception('Failed to clear expired notifications: $e');
    }
  }

  // Bulk delete notifications
  Future<void> deleteNotifications(List<String> notificationIds) async {
    try {
      // Fallback loop to ensure compatibility across SDK versions
      for (final id in notificationIds) {
        await _supabase.from('notifications').delete().eq('id', id);
      }
    } catch (e) {
      throw Exception('Failed to delete notifications: $e');
    }
  }

  // Get notification settings for user
  Future<Map<String, bool>> getNotificationSettings(String userId) async {
    try {
      final response = await _supabase
          .from('profiles')
          .select(
              'notification_enabled, email_notifications, sms_notifications')
          .eq('id', userId)
          .single();

      return {
        'push_notifications': response['notification_enabled'] ?? true,
        'email_notifications': response['email_notifications'] ?? true,
        'sms_notifications': response['sms_notifications'] ?? false,
      };
    } catch (e) {
      throw Exception('Failed to get notification settings: $e');
    }
  }

  // Update notification settings
  Future<void> updateNotificationSettings({
    required String userId,
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
  }) async {
    try {
      final updateData = <String, dynamic>{};

      if (pushNotifications != null) {
        updateData['notification_enabled'] = pushNotifications;
      }
      if (emailNotifications != null) {
        updateData['email_notifications'] = emailNotifications;
      }
      if (smsNotifications != null) {
        updateData['sms_notifications'] = smsNotifications;
      }

      if (updateData.isNotEmpty) {
        await _supabase.from('profiles').update(updateData).eq('id', userId);
      }
    } catch (e) {
      throw Exception('Failed to update notification settings: $e');
    }
  }
}
