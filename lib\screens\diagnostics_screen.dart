import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/car_service.dart';
import '../data/dummy_data_new.dart';
import '../constants/app_colors.dart';

class DiagnosticsScreen extends StatefulWidget {
  @override
  _DiagnosticsScreenState createState() => _DiagnosticsScreenState();
}

class _DiagnosticsScreenState extends State<DiagnosticsScreen> {
  final CarService _carService = CarService();
  Map<String, dynamic> _testResults = {};
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _runDiagnostics();
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
      _testResults = {};
    });

    Map<String, dynamic> results = {};

    // Test 1: Dummy Data
    try {
      final dummyCars = DummyData.getDummyCars();
      results['dummy_cars'] = {
        'status': 'success',
        'count': dummyCars.length,
        'message': 'البيانات الوهمية متاحة',
        'details': dummyCars.map((car) => car.name).toList(),
      };
    } catch (e) {
      results['dummy_cars'] = {
        'status': 'error',
        'message': 'خطأ في البيانات الوهمية: $e',
      };
    }

    // Test 2: CarService - Featured Cars
    try {
      final featuredCars = await _carService.getFeaturedCars();
      results['featured_cars_service'] = {
        'status': 'success',
        'count': featuredCars.length,
        'message': 'خدمة السيارات المميزة تعمل',
        'details': featuredCars.map((car) => car.name).toList(),
      };
    } catch (e) {
      results['featured_cars_service'] = {
        'status': 'error',
        'message': 'خطأ في خدمة السيارات المميزة: $e',
      };
    }

    // Test 3: CarService - Popular Categories
    try {
      final categories = await _carService.getPopularCategories();
      results['popular_categories'] = {
        'status': 'success',
        'count': categories.length,
        'message': 'خدمة الفئات الشائعة تعمل',
        'details': categories,
      };
    } catch (e) {
      results['popular_categories'] = {
        'status': 'error',
        'message': 'خطأ في خدمة الفئات الشائعة: $e',
      };
    }

    // Test 4: CarService - All Cars
    try {
      final allCars = await _carService.getCars(limit: 10);
      results['all_cars_service'] = {
        'status': 'success',
        'count': allCars.length,
        'message': 'خدمة جميع السيارات تعمل',
        'details': allCars.map((car) => car.name).toList(),
      };
    } catch (e) {
      results['all_cars_service'] = {
        'status': 'error',
        'message': 'خطأ في خدمة جميع السيارات: $e',
      };
    }

    setState(() {
      _testResults = results;
      _isRunning = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تشخيص التطبيق',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _runDiagnostics,
          ),
        ],
      ),
      body: _isRunning
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColors.primary),
                  SizedBox(height: 16),
                  Text(
                    'جاري إجراء التشخيص...',
                    style: GoogleFonts.cairo(fontSize: 16),
                  ),
                ],
              ),
            )
          : ListView(
              padding: EdgeInsets.all(16),
              children: [
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'نتائج التشخيص',
                          style: GoogleFonts.cairo(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 16),
                        ..._testResults.entries.map((entry) {
                          return _buildTestResult(entry.key, entry.value);
                        }).toList(),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _runDiagnostics,
                  icon: Icon(Icons.refresh),
                  label: Text('إعادة تشغيل التشخيص'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.all(16),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildTestResult(String testName, Map<String, dynamic> result) {
    final isSuccess = result['status'] == 'success';
    final icon = isSuccess ? Icons.check_circle : Icons.error;
    final color = isSuccess ? Colors.green : Colors.red;

    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getTestDisplayName(testName),
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              result['message'] ?? 'لا توجد رسالة',
              style: GoogleFonts.cairo(fontSize: 14),
            ),
            if (result['count'] != null)
              Text(
                'العدد: ${result['count']}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            if (result['details'] != null && isSuccess)
              ExpansionTile(
                title: Text('التفاصيل', style: GoogleFonts.cairo(fontSize: 12)),
                children: [
                  if (result['details'] is List)
                    ...((result['details'] as List)
                        .take(5)
                        .map((item) => Padding(
                              padding: EdgeInsets.only(left: 16, bottom: 4),
                              child: Text('• $item',
                                  style: GoogleFonts.cairo(fontSize: 11)),
                            ))),
                  if (result['details'] is Map)
                    ...((result['details'] as Map)
                        .entries
                        .take(5)
                        .map((entry) => Padding(
                              padding: EdgeInsets.only(left: 16, bottom: 4),
                              child: Text('• ${entry.key}: ${entry.value}',
                                  style: GoogleFonts.cairo(fontSize: 11)),
                            ))),
                ],
              ),
          ],
        ),
      ),
    );
  }

  String _getTestDisplayName(String testName) {
    switch (testName) {
      case 'dummy_cars':
        return 'البيانات الوهمية';
      case 'featured_cars_service':
        return 'خدمة السيارات المميزة';
      case 'popular_categories':
        return 'خدمة الفئات الشائعة';
      case 'all_cars_service':
        return 'خدمة جميع السيارات';
      default:
        return testName;
    }
  }
}
