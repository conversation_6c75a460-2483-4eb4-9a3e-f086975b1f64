import 'dart:typed_data';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';

class StorageService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Upload user avatar
  Future<String> uploadUserAvatar({
    required String userId,
    required Uint8List imageBytes,
    required String fileName,
  }) async {
    try {
      final String path = 'avatars/$userId/$fileName';

      await _supabase.storage
          .from(AppConstants.userAvatarsBucket)
          .uploadBinary(path, imageBytes);

      final String publicUrl = _supabase.storage
          .from(AppConstants.userAvatarsBucket)
          .getPublicUrl(path);

      return publicUrl;
    } catch (e) {
      throw Exception('Failed to upload avatar: $e');
    }
  }

  // Upload document (license, etc.)
  Future<String> uploadDocument({
    required String userId,
    required Uint8List imageBytes,
    required String fileName,
    required String documentType,
  }) async {
    try {
      final String path = 'documents/$userId/$documentType/$fileName';

      await _supabase.storage
          .from(AppConstants.documentsImagesBucket)
          .uploadBinary(path, imageBytes);

      final String publicUrl = _supabase.storage
          .from(AppConstants.documentsImagesBucket)
          .getPublicUrl(path);

      return publicUrl;
    } catch (e) {
      throw Exception('Failed to upload document: $e');
    }
  }

  // Upload signature
  Future<String> uploadSignature({
    required String userId,
    required String bookingId,
    required Uint8List signatureBytes,
  }) async {
    try {
      final String fileName =
          'signature_${DateTime.now().millisecondsSinceEpoch}.png';
      final String path = 'signatures/$userId/$bookingId/$fileName';

      await _supabase.storage
          .from(AppConstants.signaturesBucket)
          .uploadBinary(path, signatureBytes);

      final String publicUrl = _supabase.storage
          .from(AppConstants.signaturesBucket)
          .getPublicUrl(path);

      return publicUrl;
    } catch (e) {
      throw Exception('Failed to upload signature: $e');
    }
  }

  // Upload car image
  Future<String> uploadCarImage({
    required String carId,
    required Uint8List imageBytes,
    required String fileName,
  }) async {
    try {
      final String path = 'cars/$carId/$fileName';

      await _supabase.storage
          .from(AppConstants.carImagesBucket)
          .uploadBinary(path, imageBytes);

      final String publicUrl = _supabase.storage
          .from(AppConstants.carImagesBucket)
          .getPublicUrl(path);

      return publicUrl;
    } catch (e) {
      throw Exception('Failed to upload car image: $e');
    }
  }

  // Upload multiple car images
  Future<List<String>> uploadCarImages({
    required String carId,
    required List<Uint8List> imagesBytes,
    required List<String> fileNames,
  }) async {
    try {
      final List<String> urls = [];

      for (int i = 0; i < imagesBytes.length; i++) {
        final url = await uploadCarImage(
          carId: carId,
          imageBytes: imagesBytes[i],
          fileName: fileNames[i],
        );
        urls.add(url);
      }

      return urls;
    } catch (e) {
      throw Exception('Failed to upload car images: $e');
    }
  }

  // Delete file
  Future<void> deleteFile({
    required String bucket,
    required String path,
  }) async {
    try {
      await _supabase.storage.from(bucket).remove([path]);
    } catch (e) {
      throw Exception('Failed to delete file: $e');
    }
  }

  // Delete user avatar
  Future<void> deleteUserAvatar(String userId, String fileName) async {
    try {
      final String path = 'avatars/$userId/$fileName';
      await deleteFile(bucket: AppConstants.userAvatarsBucket, path: path);
    } catch (e) {
      throw Exception('Failed to delete avatar: $e');
    }
  }

  // Delete document
  Future<void> deleteDocument(
      String userId, String documentType, String fileName) async {
    try {
      final String path = 'documents/$userId/$documentType/$fileName';
      await deleteFile(bucket: AppConstants.documentsImagesBucket, path: path);
    } catch (e) {
      throw Exception('Failed to delete document: $e');
    }
  }

  // Delete signature
  Future<void> deleteSignature(
      String userId, String bookingId, String fileName) async {
    try {
      final String path = 'signatures/$userId/$bookingId/$fileName';
      await deleteFile(bucket: AppConstants.signaturesBucket, path: path);
    } catch (e) {
      throw Exception('Failed to delete signature: $e');
    }
  }

  // Delete car image
  Future<void> deleteCarImage(String carId, String fileName) async {
    try {
      final String path = 'cars/$carId/$fileName';
      await deleteFile(bucket: AppConstants.carImagesBucket, path: path);
    } catch (e) {
      throw Exception('Failed to delete car image: $e');
    }
  }

  // Get file info
  Future<Map<String, dynamic>?> getFileInfo({
    required String bucket,
    required String path,
  }) async {
    try {
      final List<FileObject> files =
          await _supabase.storage.from(bucket).list(path: path);

      if (files.isNotEmpty) {
        return {
          'name': files.first.name,
          'size': files.first.metadata?['size'],
          'created_at': files.first.createdAt,
          'updated_at': files.first.updatedAt,
        };
      }

      return null;
    } catch (e) {
      throw Exception('Failed to get file info: $e');
    }
  }

  // List files in directory
  Future<List<String>> listFiles({
    required String bucket,
    required String path,
  }) async {
    try {
      final List<FileObject> files =
          await _supabase.storage.from(bucket).list(path: path);

      return files.map((file) => file.name).toList();
    } catch (e) {
      throw Exception('Failed to list files: $e');
    }
  }

  // Get storage usage for user
  Future<Map<String, dynamic>> getUserStorageUsage(String userId) async {
    try {
      int totalSize = 0;
      int fileCount = 0;

      // Check avatars
      final avatarFiles = await _supabase.storage
          .from(AppConstants.userAvatarsBucket)
          .list(path: 'avatars/$userId');

      for (final file in avatarFiles) {
        fileCount++;
        totalSize += file.metadata?['size'] as int? ?? 0;
      }

      // Check documents
      final documentFiles = await _supabase.storage
          .from(AppConstants.documentsImagesBucket)
          .list(path: 'documents/$userId');

      for (final file in documentFiles) {
        fileCount++;
        totalSize += file.metadata?['size'] as int? ?? 0;
      }

      // Check signatures
      final signatureFiles = await _supabase.storage
          .from(AppConstants.signaturesBucket)
          .list(path: 'signatures/$userId');

      for (final file in signatureFiles) {
        fileCount++;
        totalSize += file.metadata?['size'] as int? ?? 0;
      }

      return {
        'total_size_bytes': totalSize,
        'total_files': fileCount,
        'total_size_mb': (totalSize / (1024 * 1024)).toStringAsFixed(2),
      };
    } catch (e) {
      throw Exception('Failed to get storage usage: $e');
    }
  }

  // Validate file size
  bool isValidFileSize(Uint8List fileBytes,
      {int maxSizeBytes = AppConstants.maxImageSize}) {
    return fileBytes.length <= maxSizeBytes;
  }

  // Validate file type by extension
  bool isValidImageType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return AppConstants.allowedImageTypes.contains(extension);
  }

  // Generate unique filename
  String generateUniqueFileName(String originalFileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = originalFileName.split('.').last;
    final nameWithoutExtension = originalFileName.split('.').first;
    return '${timestamp}_$nameWithoutExtension.$extension';
  }

  // Extract filename from URL
  String getFileNameFromUrl(String url) {
    final uri = Uri.parse(url);
    return uri.pathSegments.last;
  }

  // Extract path from URL
  String getPathFromUrl(String url, String bucketName) {
    final uri = Uri.parse(url);
    final bucketIndex = uri.pathSegments.indexOf(bucketName);
    if (bucketIndex != -1 && bucketIndex < uri.pathSegments.length - 1) {
      return uri.pathSegments.sublist(bucketIndex + 1).join('/');
    }
    return '';
  }
}
