import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../l10n/app_localizations.dart';
import '../widgets/custom_bottom_nav_bar.dart';
import 'home_screen.dart';
import 'cars_screen.dart';
import 'bookings_screen.dart';
import 'rental_extension_screen.dart';
import 'profile_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({Key? key}) : super(key: key);

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CarsScreen(),
    const BookingsScreen(),
    const RentalExtensionScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onNavigationTap(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });

      // إضافة haptic feedback
      HapticFeedback.selectionClick();

      // تحريك الصفحة
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      // تشغيل الرسم المتحرك للأيقونة
      _animationController.forward().then((_) {
        _animationController.reverse();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    // إعداد عناصر الـ Navigation Bar
    final navItems = [
      BottomNavItem(
        icon: Icons.home_outlined,
        activeIcon: Icons.home_rounded,
        label: localizations?.home ?? 'الرئيسية',
      ),
      BottomNavItem(
        icon: Icons.directions_car_outlined,
        activeIcon: Icons.directions_car_rounded,
        label: localizations?.cars ?? 'السيارات',
      ),
      BottomNavItem(
        icon: Icons.calendar_month_outlined,
        activeIcon: Icons.calendar_month_rounded,
        label: localizations?.bookings ?? 'حجوزاتي',
      ),
      BottomNavItem(
        icon: Icons.schedule_outlined,
        activeIcon: Icons.schedule_rounded,
        label: 'تمديد الإيجار',
      ),
      BottomNavItem(
        icon: Icons.person_outline_rounded,
        activeIcon: Icons.person_rounded,
        label: localizations?.profile ?? 'الملف الشخصي',
      ),
    ];

    return Scaffold(
      extendBody: false,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _screens,
      ),
      bottomNavigationBar: CustomBottomNavBar(
        currentIndex: _currentIndex,
        onTap: _onNavigationTap,
        items: navItems,
      ),
    );
  }
}
