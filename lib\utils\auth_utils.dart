import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../screens/auth/login_screen.dart';

/// دالة مساعدة لتسجيل الخروج وإعادة التوجيه إلى شاشة تسجيل الدخول
class AuthUtils {
  /// تسجيل خروج المستخدم مع إعادة توجيه إلى شاشة تسجيل الدخول
  static Future<void> signOutAndRedirect(BuildContext context) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // تسجيل خروج المستخدم
      await authProvider.signOut();

      // إغلاق جميع الشاشات والعودة إلى شاشة تسجيل الدخول
      if (context.mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    } catch (error) {
      // في حالة حدوث خطأ، إغلاق مؤشر التحميل وإظهار رسالة خطأ
      if (context.mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تسجيل الخروج: $error'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => signOutAndRedirect(context),
            ),
          ),
        );
      }
    }
  }

  /// إظهار نافذة تأكيد تسجيل الخروج
  static Future<void> showSignOutDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.logout, color: Colors.red),
            SizedBox(width: 8),
            Text('تسجيل الخروج'),
          ],
        ),
        content: const Text(
          'هل أنت متأكد من تسجيل الخروج؟\nسيتم إغلاق جلستك وإعادتك إلى شاشة تسجيل الدخول.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      await signOutAndRedirect(context);
    }
  }

  /// التحقق من حالة المصادقة وإعادة التوجيه المناسب
  static Future<void> checkAuthAndRedirect(BuildContext context) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      if (!authProvider.isAuthenticated) {
        // المستخدم غير مسجل الدخول، إعادة توجيه إلى شاشة تسجيل الدخول
        if (context.mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
            (route) => false,
          );
        }
      }
    } catch (error) {
      debugPrint('خطأ في التحقق من حالة المصادقة: $error');
    }
  }
}
