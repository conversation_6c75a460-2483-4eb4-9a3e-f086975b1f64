import 'package:flutter/material.dart';
import '../screens/auth/login_screen.dart';
import '../screens/profile/bookings_screen.dart';
import '../screens/profile/edit_profile_screen.dart';
import '../screens/profile/payment_methods_screen.dart';
import '../screens/profile/help_center_screen.dart';
import '../screens/profile/notifications_screen.dart';
import '../screens/profile/account_info_screen.dart';
import '../screens/profile/document_upload_screen.dart';
import '../screens/rental_extension_screen.dart';

class AppRoutes {
  static const String login = '/login';
  static const String editProfile = '/edit-profile';
  static const String documentUpload = '/document-upload';
  static const String bookings = '/bookings';
  static const String paymentMethods = '/payment-methods';
  static const String accountInfo = '/account-info';
  static const String notifications = '/notifications';
  static const String rentalExtension = '/rental-extension';
  static const String help = '/help';
  static const String helpCenter = '/help-center';
  static const String terms = '/terms';
  static const String privacy = '/privacy';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case login:
        return _createRoute(LoginScreen());
      case editProfile:
        return _createRoute(EditProfileScreen());
      case documentUpload:
        return _createRoute(DocumentUploadScreen());
      case bookings:
        return _createRoute(BookingsScreen());
      case paymentMethods:
        return _createRoute(PaymentMethodsScreen());
      case accountInfo:
        return _createRoute(AccountInfoScreen());
      case notifications:
        return _createRoute(NotificationsScreen());
      case rentalExtension:
        return _createRoute(RentalExtensionScreen());
      case help:
      case helpCenter:
        return _createRoute(HelpCenterScreen());
      case terms:
        return _createRoute(_createPlaceholderScreen(
            'الشروط والأحكام', 'شاشة الشروط والأحكام'));
      case privacy:
        return _createRoute(
            _createPlaceholderScreen('سياسة الخصوصية', 'شاشة سياسة الخصوصية'));
      default:
        return _createRoute(_createPlaceholderScreen(
            'غير موجود', 'الصفحة المطلوبة غير موجودة'));
    }
  }

  static PageRouteBuilder _createRoute(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, _) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.ease;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  static Widget _createPlaceholderScreen(String title, String description) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: Color(0xFF1E88E5),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.construction,
                size: 80,
                color: Colors.grey[400],
              ),
              SizedBox(height: 20),
              Text(
                title,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 10),
              Text(
                description,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 20),
              Text(
                'ستكون متاحة قريباً...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
