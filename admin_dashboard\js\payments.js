// ملف JavaScript لإدارة المدفوعات في لوحة التحكم

// متغيرات عامة للمدفوعات
let paymentsData = [];
let paymentsCurrentPage = 1;
const itemsPerPage = 10;
let totalPages = 1;

// بيانات وهمية للمدفوعات (للاختبار)
const mockPaymentsData = [
    {
        id: 'PAY001',
        booking_id: 'BOOK001',
        user_id: 'user1',
        user_name: 'أحمد محمد',
        user_email: '<EMAIL>',
        amount: 450.00,
        currency: 'AED',
        payment_method: 'credit_card',
        payment_gateway: 'stripe',
        transaction_id: 'TXN_123456789',
        status: 'completed',
        payment_date: new Date('2024-01-15T10:30:00'),
        description: 'دفع حجز سيارة BMW X5',
        fees: 13.50,
        net_amount: 436.50,
        refund_amount: 0,
        refund_status: null,
        metadata: {
            card_last4: '1234',
            card_brand: 'visa',
            receipt_url: 'https://receipt.stripe.com/123'
        },
        created_at: new Date('2024-01-15T10:30:00'),
        updated_at: new Date('2024-01-15T10:30:00')
    },
    {
        id: 'PAY002',
        booking_id: 'BOOK002',
        user_id: 'user2',
        user_name: 'فاطمة علي',
        user_email: '<EMAIL>',
        amount: 320.00,
        currency: 'AED',
        payment_method: 'bank_transfer',
        payment_gateway: 'bank',
        transaction_id: 'BANK_987654321',
        status: 'pending',
        payment_date: new Date('2024-01-14T15:45:00'),
        description: 'دفع حجز سيارة Toyota Corolla',
        fees: 0,
        net_amount: 320.00,
        refund_amount: 0,
        refund_status: null,
        metadata: {
            bank_name: 'Emirates NBD',
            account_number: '*****1234'
        },
        created_at: new Date('2024-01-14T15:45:00'),
        updated_at: new Date('2024-01-14T15:45:00')
    },
    {
        id: 'PAY003',
        booking_id: 'BOOK003',
        user_id: 'user3',
        user_name: 'محمد خالد',
        user_email: '<EMAIL>',
        amount: 850.00,
        currency: 'AED',
        payment_method: 'credit_card',
        payment_gateway: 'paypal',
        transaction_id: 'PAYPAL_555777999',
        status: 'refunded',
        payment_date: new Date('2024-01-10T09:20:00'),
        description: 'دفع حجز سيارة Mercedes C-Class',
        fees: 25.50,
        net_amount: 824.50,
        refund_amount: 850.00,
        refund_status: 'completed',
        metadata: {
            paypal_email: '<EMAIL>',
            receipt_url: 'https://paypal.com/receipt/123'
        },
        created_at: new Date('2024-01-10T09:20:00'),
        updated_at: new Date('2024-01-12T14:30:00')
    },
    {
        id: 'PAY004',
        booking_id: 'BOOK004',
        user_id: 'user4',
        user_name: 'سارة أحمد',
        user_email: '<EMAIL>',
        amount: 290.00,
        currency: 'AED',
        payment_method: 'apple_pay',
        payment_gateway: 'stripe',
        transaction_id: 'APPLE_888999111',
        status: 'failed',
        payment_date: new Date('2024-01-13T12:15:00'),
        description: 'دفع حجز سيارة Nissan Altima',
        fees: 8.70,
        net_amount: 281.30,
        refund_amount: 0,
        refund_status: null,
        metadata: {
            failure_reason: 'insufficient_funds',
            retry_count: 2
        },
        created_at: new Date('2024-01-13T12:15:00'),
        updated_at: new Date('2024-01-13T12:20:00')
    },
    {
        id: 'PAY005',
        booking_id: 'BOOK005',
        user_id: 'user5',
        user_name: 'عبدالله حسن',
        user_email: '<EMAIL>',
        amount: 680.00,
        currency: 'AED',
        payment_method: 'google_pay',
        payment_gateway: 'stripe',
        transaction_id: 'GOOGLE_444555666',
        status: 'completed',
        payment_date: new Date('2024-01-12T16:40:00'),
        description: 'دفع حجز سيارة Audi A4',
        fees: 20.40,
        net_amount: 659.60,
        refund_amount: 0,
        refund_status: null,
        metadata: {
            google_transaction_id: 'GOOGLE_444555666',
            receipt_url: 'https://pay.google.com/receipt/123'
        },
        created_at: new Date('2024-01-12T16:40:00'),
        updated_at: new Date('2024-01-12T16:40:00')
    }
];

// تهيئة صفحة المدفوعات
function initializePayments() {
    console.log('تهيئة صفحة المدفوعات...');
    
    // تحميل البيانات
    loadPaymentsData();
    
    // ربط الأحداث
    bindPaymentEvents();
    
    // تحديث الإحصائيات
    updatePaymentStats();
}

// تحميل بيانات المدفوعات
async function loadPaymentsData() {
    try {
        showLoading(true);
        
        // في الواقع، هذا سيكون استدعاء API
        // const response = await fetchPayments(currentPage, itemsPerPage, getFilters());
        // paymentsData = response.data;
        // totalPages = response.totalPages;
        
        // استخدام البيانات الوهمية للآن
        await new Promise(resolve => setTimeout(resolve, 800));
        paymentsData = mockPaymentsData;
        totalPages = Math.ceil(paymentsData.length / itemsPerPage);
        
        displayPayments();
        updatePagination();
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات المدفوعات:', error);
        showNotification('خطأ في تحميل بيانات المدفوعات', 'error');
    } finally {
        showLoading(false);
    }
}

// عرض المدفوعات في الجدول
function displayPayments() {
    const tbody = document.querySelector('#paymentsTable tbody');
    if (!tbody) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentPayments = paymentsData.slice(startIndex, endIndex);
    
    tbody.innerHTML = '';
    
    currentPayments.forEach(payment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="payment-id">
                    <strong>${payment.id}</strong>
                    <small>معرف المعاملة: ${payment.transaction_id}</small>
                </div>
            </td>
            <td>
                <div class="customer-info">
                    <strong>${payment.user_name}</strong>
                    <small>${payment.user_email}</small>
                </div>
            </td>
            <td>
                <div class="amount-info">
                    <strong>${payment.amount.toFixed(2)} ${payment.currency}</strong>
                    ${payment.fees > 0 ? `<small>رسوم: ${payment.fees.toFixed(2)}</small>` : ''}
                </div>
            </td>
            <td>
                <span class="payment-method">
                    <i class="fas ${getPaymentMethodIcon(payment.payment_method)}"></i>
                    ${getPaymentMethodName(payment.payment_method)}
                </span>
            </td>
            <td>
                <span class="status-badge status-${payment.status.replace('_', '-')}">
                    ${getPaymentStatusText(payment.status)}
                </span>
            </td>
            <td class="payment-date">
                ${formatDateTime(payment.payment_date)}
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon" onclick="viewPaymentDetails('${payment.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${payment.status === 'completed' && payment.refund_amount === 0 ? `
                        <button class="btn-icon btn-warning" onclick="showRefundModal('${payment.id}')" title="استرداد">
                            <i class="fas fa-undo"></i>
                        </button>
                    ` : ''}
                    <button class="btn-icon" onclick="downloadReceipt('${payment.id}')" title="تحميل الإيصال">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // تحديث معلومات العدد
    updatePaymentCount();
}

// ربط الأحداث
function bindPaymentEvents() {
    // فلترة المدفوعات
    const statusFilter = document.getElementById('statusFilter');
    const methodFilter = document.getElementById('methodFilter');
    const searchInput = document.getElementById('searchPayments');
    const dateFromInput = document.getElementById('dateFrom');
    const dateToInput = document.getElementById('dateTo');
    
    if (statusFilter) {
        statusFilter.addEventListener('change', filterPayments);
    }
    
    if (methodFilter) {
        methodFilter.addEventListener('change', filterPayments);
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterPayments, 300));
    }
    
    if (dateFromInput) {
        dateFromInput.addEventListener('change', filterPayments);
    }
    
    if (dateToInput) {
        dateToInput.addEventListener('change', filterPayments);
    }
    
    // تصدير البيانات
    const exportBtn = document.getElementById('exportPayments');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportPayments);
    }
    
    // تحديث البيانات
    const refreshBtn = document.getElementById('refreshPayments');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            currentPage = 1;
            loadPaymentsData();
        });
    }
}

// فلترة المدفوعات
function filterPayments() {
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const methodFilter = document.getElementById('methodFilter')?.value || '';
    const searchText = document.getElementById('searchPayments')?.value.toLowerCase() || '';
    const dateFrom = document.getElementById('dateFrom')?.value || '';
    const dateTo = document.getElementById('dateTo')?.value || '';
    
    let filteredData = mockPaymentsData.filter(payment => {
        // فلترة بالحالة
        if (statusFilter && payment.status !== statusFilter) {
            return false;
        }
        
        // فلترة بطريقة الدفع
        if (methodFilter && payment.payment_method !== methodFilter) {
            return false;
        }
        
        // فلترة بالبحث النصي
        if (searchText) {
            const searchableText = `${payment.id} ${payment.user_name} ${payment.user_email} ${payment.transaction_id} ${payment.description}`.toLowerCase();
            if (!searchableText.includes(searchText)) {
                return false;
            }
        }
        
        // فلترة بالتاريخ
        if (dateFrom) {
            const paymentDate = new Date(payment.payment_date);
            const fromDate = new Date(dateFrom);
            if (paymentDate < fromDate) {
                return false;
            }
        }
        
        if (dateTo) {
            const paymentDate = new Date(payment.payment_date);
            const toDate = new Date(dateTo);
            toDate.setHours(23, 59, 59, 999);
            if (paymentDate > toDate) {
                return false;
            }
        }
        
        return true;
    });
    
    paymentsData = filteredData;
    totalPages = Math.ceil(paymentsData.length / itemsPerPage);
    currentPage = 1;
    
    displayPayments();
    updatePagination();
    updatePaymentStats();
}

// عرض تفاصيل المدفوعة
function viewPaymentDetails(paymentId) {
    const payment = mockPaymentsData.find(p => p.id === paymentId);
    if (!payment) {
        showNotification('لم يتم العثور على بيانات المدفوعة', 'error');
        return;
    }
    
    // ملء بيانات المدفوعة في الموديل
    document.getElementById('detailPaymentId').textContent = payment.id;
    document.getElementById('detailTransactionId').textContent = payment.transaction_id;
    document.getElementById('detailCustomerName').textContent = payment.user_name;
    document.getElementById('detailCustomerEmail').textContent = payment.user_email;
    document.getElementById('detailBookingId').textContent = payment.booking_id;
    document.getElementById('detailAmount').textContent = `${payment.amount.toFixed(2)} ${payment.currency}`;
    document.getElementById('detailFees').textContent = `${payment.fees.toFixed(2)} ${payment.currency}`;
    document.getElementById('detailNetAmount').textContent = `${payment.net_amount.toFixed(2)} ${payment.currency}`;
    document.getElementById('detailPaymentMethod').innerHTML = `
        <i class="fas ${getPaymentMethodIcon(payment.payment_method)}"></i>
        ${getPaymentMethodName(payment.payment_method)}
    `;
    document.getElementById('detailPaymentGateway').textContent = payment.payment_gateway.toUpperCase();
    document.getElementById('detailStatus').innerHTML = `
        <span class="status-badge status-${payment.status.replace('_', '-')}">
            ${getPaymentStatusText(payment.status)}
        </span>
    `;
    document.getElementById('detailPaymentDate').textContent = formatDateTime(payment.payment_date);
    document.getElementById('detailDescription').textContent = payment.description || 'لا يوجد وصف';
    
    // معلومات الاسترداد
    const refundInfo = document.getElementById('detailRefundInfo');
    if (payment.refund_amount > 0) {
        refundInfo.style.display = 'block';
        document.getElementById('detailRefundAmount').textContent = `${payment.refund_amount.toFixed(2)} ${payment.currency}`;
        document.getElementById('detailRefundStatus').innerHTML = `
            <span class="status-badge status-${payment.refund_status.replace('_', '-')}">
                ${getRefundStatusText(payment.refund_status)}
            </span>
        `;
    } else {
        refundInfo.style.display = 'none';
    }
    
    // معلومات إضافية
    const metadataContainer = document.getElementById('detailMetadata');
    metadataContainer.innerHTML = '';
    
    if (payment.metadata) {
        Object.entries(payment.metadata).forEach(([key, value]) => {
            const item = document.createElement('div');
            item.className = 'metadata-item';
            item.innerHTML = `
                <strong>${getMetadataLabel(key)}:</strong>
                <span>${value}</span>
            `;
            metadataContainer.appendChild(item);
        });
    }
    
    // عرض الموديل
    showModal('paymentDetailsModal');
}

// عرض موديل الاسترداد
function showRefundModal(paymentId) {
    const payment = mockPaymentsData.find(p => p.id === paymentId);
    if (!payment) {
        showNotification('لم يتم العثور على بيانات المدفوعة', 'error');
        return;
    }
    
    // ملء بيانات الاسترداد
    document.getElementById('refundPaymentId').value = paymentId;
    document.getElementById('refundMaxAmount').textContent = payment.amount.toFixed(2);
    document.getElementById('refundAmount').max = payment.amount;
    document.getElementById('refundAmount').value = payment.amount;
    document.getElementById('refundReason').value = '';
    
    // عرض معلومات المدفوعة
    document.getElementById('refundPaymentInfo').innerHTML = `
        <div class="payment-summary">
            <h4>${payment.user_name}</h4>
            <p>معرف المدفوعة: ${payment.id}</p>
            <p>معرف المعاملة: ${payment.transaction_id}</p>
            <p>المبلغ الأصلي: ${payment.amount.toFixed(2)} ${payment.currency}</p>
        </div>
    `;
    
    showModal('refundModal');
}

// معالجة الاسترداد
function processRefund() {
    const paymentId = document.getElementById('refundPaymentId').value;
    const refundAmount = parseFloat(document.getElementById('refundAmount').value);
    const refundReason = document.getElementById('refundReason').value.trim();
    
    if (!refundAmount || refundAmount <= 0) {
        showNotification('يرجى إدخال مبلغ الاسترداد', 'error');
        return;
    }
    
    if (!refundReason) {
        showNotification('يرجى إدخال سبب الاسترداد', 'error');
        return;
    }
    
    // عرض تأكيد
    if (!confirm(`هل أنت متأكد من استرداد مبلغ ${refundAmount.toFixed(2)} درهم؟`)) {
        return;
    }
    
    // محاكاة معالجة الاسترداد
    showLoading(true);
    
    setTimeout(() => {
        // تحديث بيانات المدفوعة
        const payment = mockPaymentsData.find(p => p.id === paymentId);
        if (payment) {
            payment.refund_amount = refundAmount;
            payment.refund_status = 'completed';
            payment.status = 'refunded';
            payment.updated_at = new Date();
        }
        
        showLoading(false);
        hideModal('refundModal');
        
        showNotification('تم تنفيذ الاسترداد بنجاح', 'success');
        
        // تحديث العرض
        displayPayments();
        updatePaymentStats();
        
    }, 2000);
}

// تحميل الإيصال
function downloadReceipt(paymentId) {
    const payment = mockPaymentsData.find(p => p.id === paymentId);
    if (!payment) {
        showNotification('لم يتم العثور على بيانات المدفوعة', 'error');
        return;
    }
    
    // في الواقع، هذا سيكون رابط لتحميل الإيصال من الخادم
    showNotification('جاري تحضير الإيصال للتحميل...', 'info');
    
    // محاكاة التحميل
    setTimeout(() => {
        // إنشاء رابط وهمي للتحميل
        const link = document.createElement('a');
        link.href = '#'; // في الواقع سيكون رابط الإيصال
        link.download = `receipt_${payment.id}.pdf`;
        
        showNotification('تم تحضير الإيصال', 'success');
    }, 1000);
}

// تصدير المدفوعات
function exportPayments() {
    // محاكاة التصدير
    showLoading(true);
    
    setTimeout(() => {
        const csvContent = generatePaymentsCSV(paymentsData);
        downloadCSV(csvContent, 'payments_export.csv');
        showLoading(false);
        showNotification('تم تصدير البيانات بنجاح', 'success');
    }, 1500);
}

// إنشاء محتوى CSV للمدفوعات
function generatePaymentsCSV(payments) {
    const headers = [
        'معرف المدفوعة',
        'معرف المعاملة',
        'معرف الحجز',
        'اسم العميل',
        'البريد الإلكتروني',
        'المبلغ',
        'العملة',
        'طريقة الدفع',
        'بوابة الدفع',
        'الحالة',
        'الرسوم',
        'صافي المبلغ',
        'مبلغ الاسترداد',
        'حالة الاسترداد',
        'تاريخ الدفع',
        'الوصف'
    ];
    
    let csvContent = headers.join(',') + '\n';
    
    payments.forEach(payment => {
        const row = [
            payment.id,
            payment.transaction_id,
            payment.booking_id,
            `"${payment.user_name}"`,
            payment.user_email,
            payment.amount,
            payment.currency,
            getPaymentMethodName(payment.payment_method),
            payment.payment_gateway,
            getPaymentStatusText(payment.status),
            payment.fees,
            payment.net_amount,
            payment.refund_amount,
            payment.refund_status ? getRefundStatusText(payment.refund_status) : '',
            formatDateTime(payment.payment_date),
            `"${payment.description || ''}"`
        ];
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

// تحديث إحصائيات المدفوعات
function updatePaymentStats() {
    const stats = calculatePaymentStats(paymentsData);
    
    // تحديث الإحصائيات في الصفحة
    const totalAmountEl = document.getElementById('totalPaymentAmount');
    const completedPaymentsEl = document.getElementById('completedPayments');
    const pendingPaymentsEl = document.getElementById('pendingPayments');
    const refundedAmountEl = document.getElementById('refundedAmount');
    
    if (totalAmountEl) {
        totalAmountEl.textContent = `${stats.totalAmount.toFixed(2)} درهم`;
    }
    
    if (completedPaymentsEl) {
        completedPaymentsEl.textContent = stats.completedCount;
    }
    
    if (pendingPaymentsEl) {
        pendingPaymentsEl.textContent = stats.pendingCount;
    }
    
    if (refundedAmountEl) {
        refundedAmountEl.textContent = `${stats.refundedAmount.toFixed(2)} درهم`;
    }
}

// حساب إحصائيات المدفوعات
function calculatePaymentStats(payments) {
    const stats = {
        totalAmount: 0,
        completedCount: 0,
        pendingCount: 0,
        failedCount: 0,
        refundedAmount: 0,
        completedAmount: 0,
        averageAmount: 0
    };
    
    payments.forEach(payment => {
        stats.totalAmount += payment.amount;
        
        switch (payment.status) {
            case 'completed':
                stats.completedCount++;
                stats.completedAmount += payment.amount;
                break;
            case 'pending':
                stats.pendingCount++;
                break;
            case 'failed':
                stats.failedCount++;
                break;
            case 'refunded':
                stats.refundedAmount += payment.refund_amount;
                break;
        }
    });
    
    stats.averageAmount = payments.length > 0 ? stats.totalAmount / payments.length : 0;
    
    return stats;
}

// تحديث عدد المدفوعات
function updatePaymentCount() {
    const countElement = document.getElementById('paymentsCount');
    if (countElement) {
        const startIndex = (currentPage - 1) * itemsPerPage + 1;
        const endIndex = Math.min(currentPage * itemsPerPage, paymentsData.length);
        countElement.textContent = `عرض ${startIndex}-${endIndex} من ${paymentsData.length} مدفوعة`;
    }
}

// دوال مساعدة للمدفوعات
function getPaymentMethodIcon(method) {
    const icons = {
        'credit_card': 'fa-credit-card',
        'debit_card': 'fa-credit-card',
        'bank_transfer': 'fa-university',
        'apple_pay': 'fa-apple-pay',
        'google_pay': 'fa-google-pay',
        'paypal': 'fa-paypal',
        'cash': 'fa-money-bill',
        'wallet': 'fa-wallet'
    };
    
    return icons[method] || 'fa-credit-card';
}

function getPaymentMethodName(method) {
    const names = {
        'credit_card': 'بطاقة ائتمان',
        'debit_card': 'بطاقة خصم',
        'bank_transfer': 'تحويل مصرفي',
        'apple_pay': 'Apple Pay',
        'google_pay': 'Google Pay',
        'paypal': 'PayPal',
        'cash': 'نقداً',
        'wallet': 'محفظة إلكترونية'
    };
    
    return names[method] || method;
}

function getPaymentStatusText(status) {
    const statuses = {
        'pending': 'في الانتظار',
        'completed': 'مكتملة',
        'failed': 'فشلت',
        'cancelled': 'ملغية',
        'refunded': 'مستردة',
        'partially_refunded': 'مستردة جزئياً'
    };
    
    return statuses[status] || status;
}

function getRefundStatusText(status) {
    const statuses = {
        'pending': 'استرداد في الانتظار',
        'completed': 'تم الاسترداد',
        'failed': 'فشل الاسترداد',
        'cancelled': 'إلغاء الاسترداد'
    };
    
    return statuses[status] || status;
}

function getMetadataLabel(key) {
    const labels = {
        'card_last4': 'آخر 4 أرقام البطاقة',
        'card_brand': 'نوع البطاقة',
        'receipt_url': 'رابط الإيصال',
        'bank_name': 'اسم البنك',
        'account_number': 'رقم الحساب',
        'paypal_email': 'البريد الإلكتروني PayPal',
        'google_transaction_id': 'معرف معاملة Google',
        'failure_reason': 'سبب الفشل',
        'retry_count': 'عدد المحاولات'
    };
    
    return labels[key] || key;
}

// التنقل بين الصفحات
function goToPaymentPage(page) {
    if (page >= 1 && page <= totalPages) {
        paymentsCurrentPage = page;
        displayPayments();
        updatePagination();
    }
}

function updatePagination() {
    const paginationContainer = document.querySelector('.pagination');
    if (!paginationContainer) return;
    
    let paginationHTML = '';
    
    // زر السابق
    paginationHTML += `
        <button class="page-btn ${paymentsCurrentPage === 1 ? 'disabled' : ''}" 
                onclick="goToPaymentPage(${paymentsCurrentPage - 1})" 
                ${paymentsCurrentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // أرقام الصفحات
    const startPage = Math.max(1, paymentsCurrentPage - 2);
    const endPage = Math.min(totalPages, paymentsCurrentPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="goToPaymentPage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="page-dots">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="page-btn ${i === paymentsCurrentPage ? 'active' : ''}" 
                    onclick="goToPaymentPage(${i})">
                ${i}
            </button>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<span class="page-dots">...</span>`;
        }
        paginationHTML += `<button class="page-btn" onclick="goToPaymentPage(${totalPages})">${totalPages}</button>`;
    }
    
    // زر التالي
    paginationHTML += `
        <button class="page-btn ${paymentsCurrentPage === totalPages ? 'disabled' : ''}" 
                onclick="goToPaymentPage(${paymentsCurrentPage + 1})" 
                ${paymentsCurrentPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// تصدير الوظائف للاستخدام العام
window.initializePayments = initializePayments;
window.viewPaymentDetails = viewPaymentDetails;
window.showRefundModal = showRefundModal;
window.processRefund = processRefund;
window.downloadReceipt = downloadReceipt;
window.exportPayments = exportPayments;
window.goToPaymentPage = goToPaymentPage;
