// ملف JavaScript لإدارة المستخدمين

// متغيرات خاصة بالمستخدمين
let usersData = [];
let filteredUsers = [];
let currentUsersPage = 1;
let usersPerPage = 10;
let totalUsersCount = 0;
let isLoadingUsers = false;

// تحميل بيانات المستخدمين
async function loadUsersData() {
    if (isLoadingUsers) return;
    
    try {
        isLoadingUsers = true;
        showLoading();
        
        // الحصول على معايير البحث والفلترة
        const searchTerm = document.getElementById('userSearch')?.value || '';
        const statusFilter = document.getElementById('userStatusFilter')?.value || '';
        const verificationFilter = document.getElementById('userVerificationFilter')?.value || '';
        
        // جلب البيانات من قاعدة البيانات
        const response = await fetchUsers({
            page: currentUsersPage,
            limit: usersPerPage,
            search: searchTerm,
            status: statusFilter,
            verification: verificationFilter
        });
        
        if (response && response.data) {
            usersData = response.data;
            filteredUsers = [...usersData];
            totalUsersCount = response.total || 0;
            
            displayUsersTable();
            setupUsersPagination();
        } else {
            throw new Error('فشل في جلب بيانات المستخدمين');
        }
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدمين:', error);
        
        // عرض رسالة خطأ في الجدول
        const tableBody = document.querySelector('#usersTable tbody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="text-red-600 mb-2">
                            <i class="fas fa-exclamation-triangle"></i>
                            حدث خطأ في تحميل البيانات
                        </div>
                        <button onclick="loadUsersData()" class="btn btn-sm btn-primary">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </td>
                </tr>
            `;
        }
        
        showNotification('فشل في تحميل بيانات المستخدمين', 'error');
    } finally {
        isLoadingUsers = false;
        hideLoading();
    }
}

// إنشاء بيانات تجريبية للمستخدمين
function generateMockUsersData() {
    const firstNames = [
        'أحمد', 'محمد', 'علي', 'حسن', 'يوسف', 'خالد', 'عبدالله', 'إبراهيم',
        'سارة', 'فاطمة', 'مريم', 'نور', 'ليلى', 'عائشة', 'خديجة', 'زينب'
    ];
    
    const lastNames = [
        'أحمد', 'محمد', 'علي', 'حسن', 'السالم', 'الأحمد', 'الحسن', 'العلي',
        'المحمد', 'الخالد', 'الناصر', 'الفارس', 'القاسم', 'الشامي', 'المري'
    ];
    
    const statuses = ['active', 'inactive', 'suspended', 'banned'];
    const verificationStatuses = ['pending', 'approved', 'rejected'];
    const nationalities = ['إماراتي', 'سعودي', 'مصري', 'لبناني', 'سوري', 'أردني', 'كويتي', 'قطري', 'بحريني', 'عماني'];
    
    const users = [];
    
    for (let i = 1; i <= 892; i++) {
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        const fullName = `${firstName} ${lastName}`;
        const email = `${firstName.toLowerCase().replace(/[أإآ]/g, 'a').replace(/[ة]/g, 'h')}.${lastName.toLowerCase().replace(/[أإآ]/g, 'a').replace(/[ة]/g, 'h')}${i}@example.com`;
        const phone = `+971-5${Math.floor(Math.random() * 10)}-${Math.floor(Math.random() * 9000000) + 1000000}`;
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const verificationStatus = verificationStatuses[Math.floor(Math.random() * verificationStatuses.length)];
        const nationality = nationalities[Math.floor(Math.random() * nationalities.length)];
        
        const birthYear = 1970 + Math.floor(Math.random() * 35); // من 1970 إلى 2004
        const dateOfBirth = new Date(birthYear, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
        const registrationDate = new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
        
        users.push({
            id: `user-${i}`,
            firstName: firstName,
            lastName: lastName,
            fullName: fullName,
            email: email,
            phone: phone,
            dateOfBirth: dateOfBirth,
            nationality: nationality,
            emiratesId: `784-${Math.floor(Math.random() * 9000000000) + 1000000000}-${Math.floor(Math.random() * 90) + 10}-${Math.floor(Math.random() * 9) + 1}`,
            address: getRandomAddress(),
            city: 'Dubai',
            country: 'UAE',
            profileImage: `https://ui-avatars.com/api/?name=${encodeURIComponent(fullName)}&background=667eea&color=fff&size=200`,
            status: status,
            verificationStatus: verificationStatus,
            isEmailVerified: Math.random() > 0.2,
            isPhoneVerified: Math.random() > 0.3,
            isDocumentsVerified: verificationStatus === 'approved',
            totalBookings: Math.floor(Math.random() * 20),
            totalSpent: Math.floor(Math.random() * 50000),
            lastLogin: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)),
            registrationDate: registrationDate,
            preferredLanguage: Math.random() > 0.3 ? 'ar' : 'en',
            notificationEnabled: Math.random() > 0.2,
            emergencyContactName: `${firstNames[Math.floor(Math.random() * firstNames.length)]} ${lastNames[Math.floor(Math.random() * lastNames.length)]}`,
            emergencyContactPhone: `+971-5${Math.floor(Math.random() * 10)}-${Math.floor(Math.random() * 9000000) + 1000000}`,
            notes: generateUserNotes()
        });
    }
    
    return users.sort((a, b) => b.registrationDate - a.registrationDate);
}

// الحصول على عنوان عشوائي
function getRandomAddress() {
    const areas = [
        'Dubai Marina', 'Downtown Dubai', 'Jumeirah Beach Residence', 'Business Bay',
        'DIFC', 'Palm Jumeirah', 'Jumeirah Lakes Towers', 'Arabian Ranches',
        'Dubai Sports City', 'Motor City', 'Discovery Gardens', 'International City'
    ];
    const area = areas[Math.floor(Math.random() * areas.length)];
    const buildingNumber = Math.floor(Math.random() * 200) + 1;
    const apartmentNumber = Math.floor(Math.random() * 50) + 1;
    
    return `شقة ${apartmentNumber}، مبنى ${buildingNumber}، ${area}، دبي، الإمارات العربية المتحدة`;
}

// إنشاء ملاحظات المستخدم
function generateUserNotes() {
    const notes = [
        'عميل مميز - يحتاج معاملة خاصة',
        'لديه حساسية من الروائح القوية',
        'يفضل السيارات الهجينة',
        'عميل متكرر - خصم خاص',
        'تاجر - يطلب فواتير دائماً',
        'سائح - يحتاج مساعدة إضافية',
        '',
        'لديه أطفال صغار',
        'يطلب مقاعد أطفال دائماً',
        'عميل VIP'
    ];
    return notes[Math.floor(Math.random() * notes.length)];
}

// عرض جدول المستخدمين
function displayUsersTable() {
    const tableBody = document.getElementById('usersTableBody');
    const startIndex = (currentUsersPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const usersToDisplay = filteredUsers.slice(startIndex, endIndex);
    
    tableBody.innerHTML = usersToDisplay.map(user => `
        <tr>
            <td>
                <img src="${user.profileImage}" alt="${user.fullName}" class="table-img" style="border-radius: 50%;">
            </td>
            <td>
                <div class="user-info">
                    <strong>${user.fullName}</strong>
                    <small class="text-muted">${user.nationality}</small>
                </div>
            </td>
            <td>
                <div class="contact-info">
                    ${user.email}
                    ${user.isEmailVerified ? '<i class="fas fa-check-circle text-success" title="محقق"></i>' : '<i class="fas fa-exclamation-circle text-warning" title="غير محقق"></i>'}
                </div>
            </td>
            <td>
                <div class="contact-info">
                    ${user.phone}
                    ${user.isPhoneVerified ? '<i class="fas fa-check-circle text-success" title="محقق"></i>' : '<i class="fas fa-exclamation-circle text-warning" title="غير محقق"></i>'}
                </div>
            </td>
            <td>
                <div class="date-info">
                    ${user.registrationDate.toLocaleDateString('ar-AE')}
                    <small class="text-muted">
                        آخر دخول: ${formatTimeAgo(user.lastLogin)}
                    </small>
                </div>
            </td>
            <td>
                <span class="status-badge verification-${user.verificationStatus}">
                    ${getVerificationStatusName(user.verificationStatus)}
                </span>
            </td>
            <td>
                <span class="status-badge status-${user.status}">
                    ${getUserStatusName(user.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewUser('${user.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editUser('${user.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn ${user.status === 'suspended' ? 'view' : 'delete'}" 
                            onclick="${user.status === 'suspended' ? `activateUser('${user.id}')` : `suspendUser('${user.id}')`}" 
                            title="${user.status === 'suspended' ? 'تفعيل' : 'إيقاف'}">
                        <i class="fas ${user.status === 'suspended' ? 'fa-check' : 'fa-ban'}"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    updateUsersPageInfo();
}

// إعداد ترقيم الصفحات للمستخدمين
function setupUsersPagination() {
    const totalPages = Math.ceil(totalUsersCount / usersPerPage);
    const paginationContainer = document.getElementById('usersPagination');
    
    // إخفاء الترقيم إذا كان هناك صفحة واحدة أو أقل
    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // زر السابق
    paginationHTML += `
        <button class="pagination-btn" ${currentUsersPage === 1 ? 'disabled' : ''} 
                onclick="changeUsersPage(${currentUsersPage - 1})">
            <i class="fas fa-chevron-right"></i> السابق
        </button>
    `;
    
    // أرقام الصفحات
    const startPage = Math.max(1, currentUsersPage - 2);
    const endPage = Math.min(totalPages, currentUsersPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="pagination-btn" onclick="changeUsersPage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="pagination-dots">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="pagination-btn ${i === currentUsersPage ? 'active' : ''}" 
                    onclick="changeUsersPage(${i})">${i}</button>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<span class="pagination-dots">...</span>`;
        }
        paginationHTML += `<button class="pagination-btn" onclick="changeUsersPage(${totalPages})">${totalPages}</button>`;
    }
    
    // زر التالي
    paginationHTML += `
        <button class="pagination-btn" ${currentUsersPage === totalPages ? 'disabled' : ''} 
                onclick="changeUsersPage(${currentUsersPage + 1})">
            التالي <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// تغيير صفحة المستخدمين
async function changeUsersPage(page) {
    const totalPages = Math.ceil(totalUsersCount / usersPerPage);
    if (page >= 1 && page <= totalPages && !isLoadingUsers) {
        currentUsersPage = page;
        await loadUsersData();
    }
}

// تحديث معلومات الصفحة
function updateUsersPageInfo() {
    const startIndex = (currentUsersPage - 1) * usersPerPage + 1;
    const endIndex = Math.min(currentUsersPage * usersPerPage, totalUsersCount);
    
    const pageInfoElement = document.getElementById('usersPageInfo');
    if (pageInfoElement) {
        if (totalUsersCount === 0) {
            pageInfoElement.textContent = 'لا توجد مستخدمين';
        } else {
            pageInfoElement.textContent = `عرض ${startIndex}-${endIndex} من ${totalUsersCount} مستخدم`;
        }
    }
}

// فلترة المستخدمين
async function filterUsers() {
    if (isLoadingUsers) return;
    
    try {
        currentUsersPage = 1;
        await loadUsersData();
    } catch (error) {
        console.error('خطأ في فلترة المستخدمين:', error);
        showNotification('حدث خطأ في البحث', 'error');
    }
}

// عرض تفاصيل المستخدم
function viewUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    // إنشاء مودال عرض المستخدم
    const modal = createUserViewModal(user);
    document.body.appendChild(modal);
    modal.classList.add('show');
}

// إنشاء مودال عرض المستخدم
function createUserViewModal(user) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'userViewModal';
    
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h2>معلومات المستخدم: ${user.fullName}</h2>
                <button class="close-btn" onclick="closeUserViewModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="user-view-content">
                    <div class="user-profile-header" style="display: flex; align-items: center; gap: 2rem; margin-bottom: 2rem; padding: 1.5rem; background: #f7fafc; border-radius: 12px;">
                        <img src="${user.profileImage}" alt="${user.fullName}" 
                             style="width: 80px; height: 80px; border-radius: 50%; border: 3px solid #667eea;">
                        <div>
                            <h3 style="margin: 0; color: #2d3748;">${user.fullName}</h3>
                            <p style="margin: 0.25rem 0; color: #718096;">${user.email}</p>
                            <p style="margin: 0.25rem 0; color: #718096;">${user.phone}</p>
                            <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                                <span class="status-badge status-${user.status}">${getUserStatusName(user.status)}</span>
                                <span class="status-badge verification-${user.verificationStatus}">${getVerificationStatusName(user.verificationStatus)}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="details-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">المعلومات الشخصية</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>الاسم الأول:</strong> ${user.firstName}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>اسم العائلة:</strong> ${user.lastName}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>تاريخ الميلاد:</strong> ${user.dateOfBirth.toLocaleDateString('ar-AE')}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>العمر:</strong> ${calculateAge(user.dateOfBirth)} سنة
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>الجنسية:</strong> ${user.nationality}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>رقم الهوية:</strong> ${user.emiratesId}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">معلومات الاتصال</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>البريد الإلكتروني:</strong> ${user.email}
                                ${user.isEmailVerified ? '<i class="fas fa-check-circle text-success" style="margin-right: 0.5rem;" title="محقق"></i>' : '<i class="fas fa-exclamation-circle text-warning" style="margin-right: 0.5rem;" title="غير محقق"></i>'}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>رقم الهاتف:</strong> ${user.phone}
                                ${user.isPhoneVerified ? '<i class="fas fa-check-circle text-success" style="margin-right: 0.5rem;" title="محقق"></i>' : '<i class="fas fa-exclamation-circle text-warning" style="margin-right: 0.5rem;" title="غير محقق"></i>'}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>العنوان:</strong> ${user.address}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>المدينة:</strong> ${user.city}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>البلد:</strong> ${user.country}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">معلومات الطوارئ</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>اسم جهة الاتصال:</strong> ${user.emergencyContactName}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>رقم الطوارئ:</strong> ${user.emergencyContactPhone}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">إحصائيات الحساب</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>إجمالي الحجوزات:</strong> ${user.totalBookings}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>إجمالي المبلغ المنفق:</strong> ${formatCurrency(user.totalSpent)}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>تاريخ التسجيل:</strong> ${user.registrationDate.toLocaleDateString('ar-AE')}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>آخر دخول:</strong> ${user.lastLogin.toLocaleDateString('ar-AE')} ${user.lastLogin.toLocaleTimeString('ar-AE')}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>اللغة المفضلة:</strong> ${user.preferredLanguage === 'ar' ? 'العربية' : 'الإنجليزية'}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">حالة التحقق</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>حالة التحقق العامة:</strong> 
                                <span class="status-badge verification-${user.verificationStatus}">${getVerificationStatusName(user.verificationStatus)}</span>
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>البريد الإلكتروني محقق:</strong> 
                                ${user.isEmailVerified ? '<span class="text-success">نعم ✓</span>' : '<span class="text-warning">لا ✗</span>'}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>رقم الهاتف محقق:</strong> 
                                ${user.isPhoneVerified ? '<span class="text-success">نعم ✓</span>' : '<span class="text-warning">لا ✗</span>'}
                            </div>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>الوثائق محققة:</strong> 
                                ${user.isDocumentsVerified ? '<span class="text-success">نعم ✓</span>' : '<span class="text-warning">لا ✗</span>'}
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">الإعدادات</h4>
                            <div class="detail-item" style="margin-bottom: 0.75rem;">
                                <strong>الإشعارات مفعلة:</strong> 
                                ${user.notificationEnabled ? '<span class="text-success">نعم ✓</span>' : '<span class="text-danger">لا ✗</span>'}
                            </div>
                        </div>
                    </div>
                    
                    ${user.notes ? `
                        <div class="notes-section" style="margin-top: 2rem;">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">ملاحظات</h4>
                            <p style="background: #f7fafc; padding: 1rem; border-radius: 8px; color: #4a5568;">${user.notes}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUserViewModal()">إغلاق</button>
                <button class="btn btn-primary" onclick="editUser('${user.id}'); closeUserViewModal();">تعديل المستخدم</button>
                <button class="btn btn-warning" onclick="viewUserBookings('${user.id}')">عرض الحجوزات</button>
            </div>
        </div>
    `;
    
    return modal;
}

// إغلاق مودال عرض المستخدم
function closeUserViewModal() {
    const modal = document.getElementById('userViewModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// تعديل المستخدم
function editUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    console.log(`تعديل المستخدم: ${user.fullName}`);
    showNotification('ميزة تعديل المستخدم قيد التطوير', 'info');
}

// إيقاف المستخدم
async function suspendUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    const userName = user.full_name || user.fullName || user.first_name + ' ' + user.last_name;
    
    if (confirm(`هل أنت متأكد من إيقاف المستخدم "${userName}"؟\n\nلن يتمكن من تسجيل الدخول أو إجراء حجوزات جديدة.`)) {
        try {
            showLoading();
            
            // تحديث حالة المستخدم في قاعدة البيانات
            const response = await updateUserInDB(userId, { status: 'suspended' });
            
            if (response) {
                hideLoading();
                
                // إعادة تحميل البيانات
                await loadUsersData();
                showNotification('تم إيقاف المستخدم بنجاح', 'success');
            } else {
                throw new Error('فشل في إيقاف المستخدم');
            }
            
        } catch (error) {
            console.error('خطأ في إيقاف المستخدم:', error);
            hideLoading();
            showNotification('حدث خطأ في إيقاف المستخدم', 'error');
        }
    }
}

// تفعيل المستخدم
async function activateUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    const userName = user.full_name || user.fullName || user.first_name + ' ' + user.last_name;
    
    if (confirm(`هل أنت متأكد من تفعيل المستخدم "${userName}"؟`)) {
        try {
            showLoading();
            
            // تحديث حالة المستخدم في قاعدة البيانات
            const response = await updateUserInDB(userId, { status: 'active' });
            
            if (response) {
                hideLoading();
                
                // إعادة تحميل البيانات
                await loadUsersData();
                showNotification('تم تفعيل المستخدم بنجاح', 'success');
            } else {
                throw new Error('فشل في تفعيل المستخدم');
            }
            
        } catch (error) {
            console.error('خطأ في تفعيل المستخدم:', error);
            hideLoading();
            showNotification('حدث خطأ في تفعيل المستخدم', 'error');
        }
    }
}

// عرض حجوزات المستخدم
function viewUserBookings(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    console.log(`عرض حجوزات المستخدم: ${user.fullName}`);
    
    // التبديل إلى قسم الحجوزات مع فلترة بناءً على اسم المستخدم
    showSection('bookings');
    setTimeout(() => {
        document.getElementById('bookingsSearch').value = user.fullName;
        filterBookings();
    }, 500);
    
    showNotification(`تم فلترة الحجوزات للمستخدم: ${user.fullName}`, 'info');
}

// تحقق من المستخدم
async function verifyUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    const userName = user.full_name || user.fullName || user.first_name + ' ' + user.last_name;
    
    if (confirm(`هل أنت متأكد من تحقيق المستخدم "${userName}"؟`)) {
        try {
            showLoading();
            
            // تحديث حالة التحقق في قاعدة البيانات
            const response = await updateUserInDB(userId, { 
                verification_status: 'approved',
                is_documents_verified: true 
            });
            
            if (response) {
                hideLoading();
                
                // إعادة تحميل البيانات
                await loadUsersData();
                showNotification('تم تحقيق المستخدم بنجاح', 'success');
            } else {
                throw new Error('فشل في تحقيق المستخدم');
            }
            
        } catch (error) {
            console.error('خطأ في تحقيق المستخدم:', error);
            hideLoading();
            showNotification('حدث خطأ في تحقيق المستخدم', 'error');
        }
    }
}

// رفض تحقيق المستخدم
async function rejectUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    const userName = user.full_name || user.fullName || user.first_name + ' ' + user.last_name;
    
    if (confirm(`هل أنت متأكد من رفض تحقيق المستخدم "${userName}"؟`)) {
        try {
            showLoading();
            
            // تحديث حالة التحقق في قاعدة البيانات
            const response = await updateUserInDB(userId, { 
                verification_status: 'rejected',
                is_documents_verified: false 
            });
            
            if (response) {
                hideLoading();
                
                // إعادة تحميل البيانات
                await loadUsersData();
                showNotification('تم رفض تحقيق المستخدم', 'warning');
            } else {
                throw new Error('فشل في رفض تحقيق المستخدم');
            }
            
        } catch (error) {
            console.error('خطأ في رفض تحقيق المستخدم:', error);
            hideLoading();
            showNotification('حدث خطأ في رفض تحقيق المستخدم', 'error');
        }
    }
}

// حساب العمر
function calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }
    
    return age;
}

// تنسيق الوقت المنقضي
function formatTimeAgo(date) {
    const now = new Date();
    const diffInMs = now - date;
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    
    if (diffInDays > 30) {
        return date.toLocaleDateString('ar-AE');
    } else if (diffInDays > 0) {
        return `منذ ${diffInDays} ${diffInDays === 1 ? 'يوم' : 'أيام'}`;
    } else if (diffInHours > 0) {
        return `منذ ${diffInHours} ${diffInHours === 1 ? 'ساعة' : 'ساعات'}`;
    } else if (diffInMinutes > 0) {
        return `منذ ${diffInMinutes} ${diffInMinutes === 1 ? 'دقيقة' : 'دقائق'}`;
    } else {
        return 'الآن';
    }
}

// الحصول على اسم حالة المستخدم
function getUserStatusName(status) {
    const statusNames = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'suspended': 'موقوف',
        'banned': 'محظور'
    };
    return statusNames[status] || status;
}

// الحصول على اسم حالة التحقق
function getVerificationStatusName(status) {
    const statusNames = {
        'pending': 'في الانتظار',
        'approved': 'محقق',
        'rejected': 'مرفوض'
    };
    return statusNames[status] || status;
}

// تصدير بيانات المستخدمين
async function exportUsers() {
    try {
        showLoading();
        
        // جلب جميع المستخدمين من قاعدة البيانات
        const allUsers = await fetchUsers({
            page: 1,
            limit: 10000, // جلب عدد كبير للحصول على جميع المستخدمين
            search: document.getElementById('searchUsers').value,
            status: document.getElementById('statusFilter').value,
            verification: document.getElementById('verificationFilter').value
        });
        
        if (!allUsers || !allUsers.data) {
            throw new Error('فشل في جلب بيانات المستخدمين');
        }
        
        const exportData = allUsers.data.map(user => ({
            'الاسم الكامل': (user.full_name || user.first_name + ' ' + user.last_name || ''),
            'البريد الإلكتروني': user.email || '',
            'رقم الهاتف': user.phone || '',
            'الجنسية': user.nationality || '',
            'رقم الهوية': user.emirates_id || '',
            'تاريخ الميلاد': user.date_of_birth ? new Date(user.date_of_birth).toLocaleDateString('ar-AE') : '',
            'العمر': user.date_of_birth ? calculateAge(new Date(user.date_of_birth)) : '',
            'العنوان': user.address || '',
            'المدينة': user.city || '',
            'البلد': user.country || '',
            'حالة المستخدم': getUserStatusName(user.status),
            'حالة التحقق': getVerificationStatusName(user.verification_status),
            'البريد محقق': user.is_email_verified ? 'نعم' : 'لا',
            'الهاتف محقق': user.is_phone_verified ? 'نعم' : 'لا',
            'الوثائق محققة': user.is_documents_verified ? 'نعم' : 'لا',
            'إجمالي الحجوزات': user.total_bookings || 0,
            'إجمالي المبلغ المنفق': user.total_spent || 0,
            'تاريخ التسجيل': user.created_at ? new Date(user.created_at).toLocaleDateString('ar-AE') : '',
            'آخر دخول': user.last_login ? new Date(user.last_login).toLocaleDateString('ar-AE') : '',
            'اللغة المفضلة': user.preferred_language === 'ar' ? 'العربية' : 'الإنجليزية',
            'الإشعارات مفعلة': user.notification_enabled ? 'نعم' : 'لا',
            'ملاحظات': user.notes || ''
        }));
        
        hideLoading();
        
        const csv = convertToCSV(exportData);
        const fileName = `users-${formatDate(new Date())}.csv`;
        downloadCSV(csv, fileName);
        
        showNotification('تم تصدير بيانات المستخدمين بنجاح', 'success');
        
    } catch (error) {
        console.error('خطأ في تصدير المستخدمين:', error);
        hideLoading();
        showNotification('حدث خطأ في تصدير بيانات المستخدمين', 'error');
    }
}

// إرسال إشعار للمستخدم
function sendNotificationToUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    console.log(`إرسال إشعار للمستخدم: ${user.fullName}`);
    showNotification('ميزة إرسال الإشعارات قيد التطوير', 'info');
}

// البحث المتقدم للمستخدمين
function advancedSearchUsers() {
    console.log('البحث المتقدم للمستخدمين');
    showNotification('ميزة البحث المتقدم قيد التطوير', 'info');
}
