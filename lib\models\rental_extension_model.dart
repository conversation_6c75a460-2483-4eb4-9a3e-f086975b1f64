import 'package:flutter/material.dart';

class RentalExtension {
  final String id;
  final String bookingId;
  final DateTime originalReturnDate;
  final DateTime newReturnDate;
  final int extensionDays;
  final double dailyRate;
  final double subtotal;
  final double vatRate;
  final double vatAmount;
  final double totalAmount;
  final ExtensionStatus status;
  final DateTime requestDate;
  final String? paymentMethod;
  final String? transactionId;
  final String? paymentIntentId; // معرف نية الدفع من Stripe

  RentalExtension({
    required this.id,
    required this.bookingId,
    required this.originalReturnDate,
    required this.newReturnDate,
    required this.extensionDays,
    required this.dailyRate,
    required this.subtotal,
    required this.vatRate,
    required this.vatAmount,
    required this.totalAmount,
    required this.status,
    required this.requestDate,
    this.paymentMethod,
    this.transactionId,
    this.paymentIntentId,
  });

  factory RentalExtension.fromJson(Map<String, dynamic> json) {
    return RentalExtension(
      id: json['id'] ?? '',
      bookingId: json['booking_id'] ?? '',
      originalReturnDate: DateTime.parse(json['original_return_date']),
      newReturnDate: DateTime.parse(json['new_return_date']),
      extensionDays: json['extension_days'] ?? 0,
      dailyRate: (json['daily_rate'] ?? 0).toDouble(),
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      vatRate: (json['vat_rate'] ?? 0).toDouble(),
      vatAmount: (json['vat_amount'] ?? 0).toDouble(),
      totalAmount: (json['total_amount'] ?? 0).toDouble(),
      status: ExtensionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ExtensionStatus.pending,
      ),
      requestDate: DateTime.parse(json['request_date']),
      paymentMethod: json['payment_method'],
      transactionId: json['transaction_id'],
      paymentIntentId: json['payment_intent_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'booking_id': bookingId,
      'original_return_date': originalReturnDate.toIso8601String(),
      'new_return_date': newReturnDate.toIso8601String(),
      'extension_days': extensionDays,
      'daily_rate': dailyRate,
      'subtotal': subtotal,
      'vat_rate': vatRate,
      'vat_amount': vatAmount,
      'total_amount': totalAmount,
      'status': status.toString().split('.').last,
      'request_date': requestDate.toIso8601String(),
      'payment_method': paymentMethod,
      'transaction_id': transactionId,
      'payment_intent_id': paymentIntentId,
    };
  }

  RentalExtension copyWith({
    String? id,
    String? bookingId,
    DateTime? originalReturnDate,
    DateTime? newReturnDate,
    int? extensionDays,
    double? dailyRate,
    double? subtotal,
    double? vatRate,
    double? vatAmount,
    double? totalAmount,
    ExtensionStatus? status,
    DateTime? requestDate,
    String? paymentMethod,
    String? transactionId,
    String? paymentIntentId,
  }) {
    return RentalExtension(
      id: id ?? this.id,
      bookingId: bookingId ?? this.bookingId,
      originalReturnDate: originalReturnDate ?? this.originalReturnDate,
      newReturnDate: newReturnDate ?? this.newReturnDate,
      extensionDays: extensionDays ?? this.extensionDays,
      dailyRate: dailyRate ?? this.dailyRate,
      subtotal: subtotal ?? this.subtotal,
      vatRate: vatRate ?? this.vatRate,
      vatAmount: vatAmount ?? this.vatAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      requestDate: requestDate ?? this.requestDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionId: transactionId ?? this.transactionId,
      paymentIntentId: paymentIntentId ?? this.paymentIntentId,
    );
  }
}

enum ExtensionStatus {
  pending,
  approved,
  rejected,
  paid,
  completed,
  cancelled,
}

extension ExtensionStatusExtension on ExtensionStatus {
  String get displayName {
    switch (this) {
      case ExtensionStatus.pending:
        return 'قيد المراجعة';
      case ExtensionStatus.approved:
        return 'موافق عليه';
      case ExtensionStatus.rejected:
        return 'مرفوض';
      case ExtensionStatus.paid:
        return 'مدفوع';
      case ExtensionStatus.completed:
        return 'مكتمل';
      case ExtensionStatus.cancelled:
        return 'ملغى';
    }
  }

  Color get color {
    switch (this) {
      case ExtensionStatus.pending:
        return Colors.orange;
      case ExtensionStatus.approved:
        return Colors.blue;
      case ExtensionStatus.rejected:
        return Colors.red;
      case ExtensionStatus.paid:
        return Colors.green;
      case ExtensionStatus.completed:
        return Colors.green.shade700;
      case ExtensionStatus.cancelled:
        return Colors.grey;
    }
  }
}
