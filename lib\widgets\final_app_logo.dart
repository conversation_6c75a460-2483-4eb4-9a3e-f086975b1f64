import 'package:flutter/material.dart';
import '../config/logo_config.dart';

// مكون الشعار النهائي المحسّن
class AppLogo extends StatelessWidget {
  final double size;
  final bool showText;
  final VoidCallback? onTap;
  final bool animated;

  const AppLogo({
    Key? key,
    this.size = LogoConfig.mediumSize,
    this.showText = false,
    this.onTap,
    this.animated = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget logo = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // الشعار
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(size * 0.25),
              boxShadow: [
                BoxShadow(
                  color: LogoConfig.primaryBlue.withOpacity(0.25),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(size * 0.25),
              child: LogoConfig.buildLogo(size: size),
            ),
          ),
        ),

        // النص
        if (showText) ...[
          const SizedBox(width: 8),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'دبي',
                style: TextStyle(
                  fontSize: size * 0.3,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  height: 1.0,
                ),
              ),
              Text(
                'كار رنتل',
                style: TextStyle(
                  fontSize: size * 0.22,
                  fontWeight: FontWeight.w500,
                  color: Colors.white.withOpacity(0.9),
                  height: 1.0,
                ),
              ),
            ],
          ),
        ],
      ],
    );

    if (animated) {
      return TweenAnimationBuilder<double>(
        duration: const Duration(seconds: 2),
        tween: Tween(begin: 0.8, end: 1.0),
        builder: (context, value, child) {
          return Transform.scale(
            scale: value,
            child: Opacity(opacity: value, child: logo),
          );
        },
      );
    }

    return logo;
  }
}

// شعار للاستخدام في الـ App Bar
class AppBarLogo extends StatelessWidget {
  final VoidCallback? onTap;

  const AppBarLogo({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: AppLogo(
        size: 40,
        showText: true,
        onTap: onTap,
      ),
    );
  }
}

// شعار للـ Splash Screen
class SplashLogo extends StatefulWidget {
  final VoidCallback? onComplete;

  const SplashLogo({Key? key, this.onComplete}) : super(key: key);

  @override
  State<SplashLogo> createState() => _SplashLogoState();
}

class _SplashLogoState extends State<SplashLogo> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        widget.onComplete?.call();
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              width: LogoConfig.splashSize,
              height: LogoConfig.splashSize,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(35),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(35),
                child: LogoConfig.buildLogo(
                  size: LogoConfig.splashSize,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// شعار مصغر للأماكن الصغيرة
class MiniLogo extends StatelessWidget {
  final double? size;

  const MiniLogo({Key? key, this.size}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logoSize = size ?? LogoConfig.miniSize;
    return Container(
      width: logoSize,
      height: logoSize,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(logoSize * 0.2),
        child: LogoConfig.buildLogo(size: logoSize),
      ),
    );
  }
}
