import 'package:flutter/material.dart';

class PriceBreakdown extends StatelessWidget {
  final double basePrice;
  final double taxes;
  final double serviceFee;
  final double? discount;
  final double? deliveryFee;
  final Map<String, double>? additionalServices;
  final int rentalDays;

  const PriceBreakdown({
    super.key,
    required this.basePrice,
    required this.taxes,
    required this.serviceFee,
    required this.rentalDays,
    this.discount,
    this.deliveryFee,
    this.additionalServices,
  });

  double get totalPrice {
    double total = basePrice + taxes + serviceFee;

    if (deliveryFee != null) {
      total += deliveryFee!;
    }

    if (additionalServices != null) {
      total +=
          additionalServices!.values.fold(0.0, (sum, price) => sum + price);
    }

    if (discount != null) {
      total -= discount!;
    }

    return total;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Price Breakdown',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Base price
          _buildPriceRow(
            'Base price ($rentalDays day${rentalDays > 1 ? 's' : ''})',
            basePrice,
            theme,
          ),

          // Additional services
          if (additionalServices != null && additionalServices!.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...additionalServices!.entries.map(
              (service) => _buildPriceRow(
                service.key,
                service.value,
                theme,
              ),
            ),
          ],

          // Delivery fee
          if (deliveryFee != null) ...[
            const SizedBox(height: 8),
            _buildPriceRow('Delivery fee', deliveryFee!, theme),
          ],

          // Service fee
          const SizedBox(height: 8),
          _buildPriceRow('Service fee', serviceFee, theme),

          // Taxes
          const SizedBox(height: 8),
          _buildPriceRow('Taxes', taxes, theme),

          // Discount
          if (discount != null && discount! > 0) ...[
            const SizedBox(height: 8),
            _buildPriceRow(
              'Discount',
              -discount!,
              theme,
              isDiscount: true,
            ),
          ],

          const Divider(height: 24),

          // Total
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'AED ${totalPrice.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(
    String label,
    double amount,
    ThemeData theme, {
    bool isDiscount = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        Text(
          '${isDiscount ? '-' : ''}AED ${amount.abs().toStringAsFixed(2)}',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: isDiscount ? Colors.green : theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}
