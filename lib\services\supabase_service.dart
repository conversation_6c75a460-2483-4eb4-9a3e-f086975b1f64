import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:io';

class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;

  static SupabaseClient get client => _client;

  // Authentication methods
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    return await _client.auth.signUp(
      email: email,
      password: password,
      data: data,
    );
  }

  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    return await _client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  static Future<void> signOut() async {
    await _client.auth.signOut();
  }

  // Database methods
  static Future<PostgrestList> select({
    required String table,
    String? filter,
    Object? filterValue,
  }) async {
    var query = _client.from(table).select();

    if (filter != null && filterValue != null) {
      query = query.eq(filter, filterValue);
    }

    return await query;
  }

  static Future<List<Map<String, dynamic>>> insert({
    required String table,
    required Map<String, dynamic> data,
  }) async {
    return await _client.from(table).insert(data).select();
  }

  static Future<List<Map<String, dynamic>>> update({
    required String table,
    required Map<String, dynamic> data,
    required String idColumn,
    required dynamic idValue,
  }) async {
    return await _client
        .from(table)
        .update(data)
        .eq(idColumn, idValue)
        .select();
  }

  static Future<void> delete({
    required String table,
    required String idColumn,
    required dynamic idValue,
  }) async {
    await _client.from(table).delete().eq(idColumn, idValue);
  }

  // Storage methods
  static Future<String> uploadFile({
    required String bucket,
    required String path,
    required File file,
  }) async {
    await _client.storage.from(bucket).upload(path, file);
    return _client.storage.from(bucket).getPublicUrl(path);
  }

  static Future<void> deleteFile({
    required String bucket,
    required String path,
  }) async {
    await _client.storage.from(bucket).remove([path]);
  }
}
