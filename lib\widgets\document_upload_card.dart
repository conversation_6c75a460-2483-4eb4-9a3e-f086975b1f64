import 'package:flutter/material.dart';
import '../providers/document_provider.dart';
import '../utils/app_theme.dart';

class DocumentUploadCard extends StatelessWidget {
  final DocumentInfo document;
  final String title;
  final String description;
  final VoidCallback? onTakePhoto;
  final VoidCallback? onSelectFromGallery;
  final VoidCallback? onDelete;

  const DocumentUploadCard({
    super.key,
    required this.document,
    required this.title,
    required this.description,
    this.onTakePhoto,
    this.onSelectFromGallery,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUploaded = document.url != null;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and status
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.mediumGrey,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),

            const SizedBox(height: 16),

            // Document preview or upload area
            if (isUploaded)
              _buildDocumentPreview()
            else
              _buildUploadArea(context),

            if (isUploaded) ...[
              const SizedBox(height: 16),
              _buildActionButtons(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color chipColor;
    String statusText;

    switch (document.status) {
      case VerificationStatus.notSubmitted:
        chipColor = AppTheme.mediumGrey;
        statusText = 'Not Uploaded';
        break;
      case VerificationStatus.pending:
        chipColor = Colors.orange;
        statusText = 'Pending';
        break;
      case VerificationStatus.verified:
        chipColor = AppTheme.successGreen;
        statusText = 'Verified';
        break;
      case VerificationStatus.rejected:
        chipColor = AppTheme.errorRed;
        statusText = 'Rejected';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDocumentPreview() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppTheme.lightGrey,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.borderGrey),
      ),
      child: Stack(
        children: [
          // Document preview (placeholder)
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.description,
                  size: 48,
                  color: AppTheme.royalBlue,
                ),
                const SizedBox(height: 8),
                Text(
                  'Document Uploaded',
                  style: TextStyle(
                    color: AppTheme.mediumGrey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          // Delete button
          if (onDelete != null)
            Positioned(
              top: 8,
              right: 8,
              child: GestureDetector(
                onTap: onDelete,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppTheme.errorRed.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: AppTheme.errorRed,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildUploadArea(BuildContext context) {
    return GestureDetector(
      onTap: () => _showImageSourceDialog(context),
      child: Container(
        height: 120,
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppTheme.lightGrey,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.royalBlue.withOpacity(0.3),
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_upload_outlined,
              size: 48,
              color: AppTheme.royalBlue,
            ),
            const SizedBox(height: 8),
            Text(
              'Tap to upload document',
              style: TextStyle(
                color: AppTheme.royalBlue,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Camera or Gallery',
              style: TextStyle(
                color: AppTheme.mediumGrey,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _showImageSourceDialog(context),
            icon: const Icon(Icons.refresh),
            label: const Text('Replace'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.royalBlue,
              side: BorderSide(color: AppTheme.royalBlue),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onDelete,
            icon: const Icon(Icons.delete_outline),
            label: const Text('Remove'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.errorRed,
              side: BorderSide(color: AppTheme.errorRed),
            ),
          ),
        ),
      ],
    );
  }

  void _showImageSourceDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () {
                Navigator.pop(context);
                onTakePhoto?.call();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Gallery'),
              onTap: () {
                Navigator.pop(context);
                onSelectFromGallery?.call();
              },
            ),
          ],
        ),
      ),
    );
  }
}
