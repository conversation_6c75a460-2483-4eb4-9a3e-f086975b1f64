import 'package:flutter/material.dart';

class RatingStars extends StatelessWidget {
  final double rating;
  final int maxStars;
  final double size;
  final Color activeColor;
  final Color inactiveColor;
  final bool showNumber;
  final bool allowHalfStars;

  const RatingStars({
    super.key,
    required this.rating,
    this.maxStars = 5,
    this.size = 16,
    this.activeColor = Colors.amber,
    this.inactiveColor = Colors.grey,
    this.showNumber = true,
    this.allowHalfStars = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(maxStars, (index) {
          if (allowHalfStars) {
            if (index < rating.floor()) {
              return Icon(
                Icons.star,
                size: size,
                color: activeColor,
              );
            } else if (index < rating && rating - index >= 0.5) {
              return Icon(
                Icons.star_half,
                size: size,
                color: activeColor,
              );
            } else {
              return Icon(
                Icons.star_border,
                size: size,
                color: inactiveColor,
              );
            }
          } else {
            return Icon(
              index < rating.round() ? Icons.star : Icons.star_border,
              size: size,
              color: index < rating.round() ? activeColor : inactiveColor,
            );
          }
        }),
        if (showNumber) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ],
    );
  }
}
