import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';

class CategoryCard extends StatelessWidget {
  final String category;
  final int count;
  final VoidCallback? onTap;

  const CategoryCard({
    Key? key,
    required this.category,
    required this.count,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100,
        margin: const EdgeInsets.only(left: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _getCategoryColors(category),
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: _getCategoryColors(category)[0].withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getCategoryIcon(category),
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                _getCategoryName(category),
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                '$count سيارة',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Color> _getCategoryColors(String category) {
    switch (category.toLowerCase()) {
      case 'economy':
        return [Colors.green, Colors.lightGreen];
      case 'standard':
        return [Colors.blue, Colors.lightBlue];
      case 'premium':
        return [Colors.purple, Colors.deepPurple];
      case 'luxury':
        return [Colors.amber, Colors.orange];
      case 'exotic':
        return [Colors.red, Colors.pink];
      case 'suv':
        return [Colors.teal, Colors.cyan];
      default:
        return [AppColors.primary, AppColors.primary.withOpacity(0.8)];
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'economy':
        return Icons.eco;
      case 'standard':
        return Icons.directions_car;
      case 'premium':
        return Icons.star;
      case 'luxury':
        return Icons.diamond;
      case 'exotic':
        return Icons.flash_on;
      case 'suv':
        return Icons.terrain;
      default:
        return Icons.directions_car;
    }
  }

  String _getCategoryName(String category) {
    switch (category.toLowerCase()) {
      case 'economy':
        return 'اقتصادية';
      case 'standard':
        return 'عادية';
      case 'premium':
        return 'مميزة';
      case 'luxury':
        return 'فاخرة';
      case 'exotic':
        return 'سوبر';
      case 'suv':
        return 'SUV';
      default:
        return category;
    }
  }
}
