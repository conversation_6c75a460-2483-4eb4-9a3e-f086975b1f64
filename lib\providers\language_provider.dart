import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _defaultLanguage = 'ar';

  String _currentLanguage = _defaultLanguage;
  Locale _locale = const Locale('ar');
  bool _isRtl = true;
  bool _isLoading = true;

  // Getters
  String get currentLanguage => _currentLanguage;
  Locale get locale => _locale;
  bool get isRtl => _isRtl;
  bool get isLoading => _isLoading;
  bool get isArabic => _currentLanguage == 'ar';
  bool get isEnglish => _currentLanguage == 'en';

  // Supported languages
  static const List<Locale> supportedLocales = [
    Locale('ar'), // Arabic
    Locale('en'), // English
  ];

  static const Map<String, String> languageNames = {
    'ar': 'العربية',
    'en': 'English',
  };

  LanguageProvider() {
    _loadLanguage();
  }

  // Load saved language preference
  Future<void> _loadLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey);

      if (savedLanguage != null && _isValidLanguage(savedLanguage)) {
        await changeLanguage(savedLanguage, saveToPrefs: false);
      } else {
        // Use system locale if supported
        final systemLocale = PlatformDispatcher.instance.locale;
        final systemLanguage = systemLocale.languageCode;

        if (_isValidLanguage(systemLanguage)) {
          await changeLanguage(systemLanguage, saveToPrefs: true);
        } else {
          await changeLanguage(_defaultLanguage, saveToPrefs: true);
        }
      }
    } catch (e) {
      // Fallback to default language
      await changeLanguage(_defaultLanguage, saveToPrefs: false);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Change language
  Future<void> changeLanguage(String languageCode,
      {bool saveToPrefs = true}) async {
    if (!_isValidLanguage(languageCode)) {
      throw ArgumentError('Unsupported language: $languageCode');
    }

    _currentLanguage = languageCode;
    _locale = Locale(languageCode);
    _isRtl = _isRightToLeft(languageCode);

    if (saveToPrefs) {
      await _saveLanguagePreference(languageCode);
    }

    notifyListeners();
  }

  // Alternative method name for compatibility
  Future<void> setLanguage(String languageCode) async {
    await changeLanguage(languageCode);
  }

  // Toggle between Arabic and English
  Future<void> toggleLanguage() async {
    final newLanguage = _currentLanguage == 'ar' ? 'en' : 'ar';
    await changeLanguage(newLanguage);
  }

  // Save language preference
  Future<void> _saveLanguagePreference(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      debugPrint('Failed to save language preference: $e');
    }
  }

  // Check if language is valid
  bool _isValidLanguage(String languageCode) {
    return supportedLocales
        .any((locale) => locale.languageCode == languageCode);
  }

  // Check if language is RTL
  bool _isRightToLeft(String languageCode) {
    const rtlLanguages = ['ar', 'fa', 'he', 'ur'];
    return rtlLanguages.contains(languageCode);
  }

  // Get language name
  String getLanguageName(String languageCode) {
    return languageNames[languageCode] ?? languageCode.toUpperCase();
  }

  // Get current language name
  String get currentLanguageName => getLanguageName(_currentLanguage);

  // Get text direction
  TextDirection get textDirection =>
      _isRtl ? TextDirection.rtl : TextDirection.ltr;

  // Get opposite text direction
  TextDirection get oppositeTextDirection =>
      _isRtl ? TextDirection.ltr : TextDirection.rtl;

  // Format text based on language direction
  String formatText(String text) {
    if (_isRtl) {
      return '\u202B$text\u202C'; // Right-to-left embedding
    } else {
      return '\u202A$text\u202C'; // Left-to-right embedding
    }
  }

  // Get localized font family
  String get fontFamily {
    switch (_currentLanguage) {
      case 'ar':
        return 'Cairo';
      case 'en':
        return 'Roboto';
      default:
        return 'Roboto';
    }
  }

  // Get appropriate alignment based on language
  Alignment get startAlignment =>
      _isRtl ? Alignment.centerRight : Alignment.centerLeft;
  Alignment get endAlignment =>
      _isRtl ? Alignment.centerLeft : Alignment.centerRight;

  // Get appropriate cross axis alignment
  CrossAxisAlignment get startCrossAxisAlignment =>
      _isRtl ? CrossAxisAlignment.end : CrossAxisAlignment.start;
  CrossAxisAlignment get endCrossAxisAlignment =>
      _isRtl ? CrossAxisAlignment.start : CrossAxisAlignment.end;

  // Get appropriate main axis alignment
  MainAxisAlignment get startMainAxisAlignment =>
      _isRtl ? MainAxisAlignment.end : MainAxisAlignment.start;
  MainAxisAlignment get endMainAxisAlignment =>
      _isRtl ? MainAxisAlignment.start : MainAxisAlignment.end;

  // Get appropriate text align
  TextAlign get startTextAlign => _isRtl ? TextAlign.right : TextAlign.left;
  TextAlign get endTextAlign => _isRtl ? TextAlign.left : TextAlign.right;

  // Get appropriate edge insets
  EdgeInsets symmetricHorizontal(double value) =>
      EdgeInsets.symmetric(horizontal: value);
  EdgeInsets onlyLeft(double value) =>
      _isRtl ? EdgeInsets.only(right: value) : EdgeInsets.only(left: value);
  EdgeInsets onlyRight(double value) =>
      _isRtl ? EdgeInsets.only(left: value) : EdgeInsets.only(right: value);

  EdgeInsets fromLTRB(double left, double top, double right, double bottom) {
    if (_isRtl) {
      return EdgeInsets.fromLTRB(right, top, left, bottom);
    } else {
      return EdgeInsets.fromLTRB(left, top, right, bottom);
    }
  }

  // Get icon based on direction
  IconData get backIcon =>
      _isRtl ? Icons.arrow_forward_ios : Icons.arrow_back_ios;
  IconData get forwardIcon =>
      _isRtl ? Icons.arrow_back_ios : Icons.arrow_forward_ios;

  // Format numbers based on locale
  String formatNumber(num number) {
    if (_isRtl) {
      // Convert Western-Arabic numerals to Eastern-Arabic numerals for Arabic
      const western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
      const eastern = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

      String result = number.toString();
      for (int i = 0; i < western.length; i++) {
        result = result.replaceAll(western[i], eastern[i]);
      }
      return result;
    } else {
      return number.toString();
    }
  }

  // Get appropriate border radius
  BorderRadius borderRadius({
    double topLeft = 0,
    double topRight = 0,
    double bottomLeft = 0,
    double bottomRight = 0,
  }) {
    if (_isRtl) {
      return BorderRadius.only(
        topLeft: Radius.circular(topRight),
        topRight: Radius.circular(topLeft),
        bottomLeft: Radius.circular(bottomRight),
        bottomRight: Radius.circular(bottomLeft),
      );
    } else {
      return BorderRadius.only(
        topLeft: Radius.circular(topLeft),
        topRight: Radius.circular(topRight),
        bottomLeft: Radius.circular(bottomLeft),
        bottomRight: Radius.circular(bottomRight),
      );
    }
  }

  // Reset to default language
  Future<void> resetToDefault() async {
    await changeLanguage(_defaultLanguage);
  }

  // Get list of available languages for UI
  List<Map<String, String>> get availableLanguages {
    return supportedLocales
        .map((locale) => {
              'code': locale.languageCode,
              'name': getLanguageName(locale.languageCode),
            })
        .toList();
  }
}
