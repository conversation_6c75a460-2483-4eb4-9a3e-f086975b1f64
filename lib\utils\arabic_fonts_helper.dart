import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ArabicFontsHelper {
  // خط عربي أساسي مع fallback
  static TextStyle getArabicTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    try {
      return GoogleFonts.cairo(
        fontSize: fontSize ?? 14.0,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
        // إضافة fallback fonts للنصوص العربية
        // تم إزالة fontFallback لأن المعامل غير معرف في GoogleFonts أو TextStyle
      );
    } catch (e) {
      // في حالة فشل Google Fonts، استخدم خط النظام
      return TextStyle(
        fontFamily: 'System',
        fontSize: fontSize ?? 14.0,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
      );
    }
  }

  // خط إنجليزي
  static TextStyle getEnglishTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    try {
      return GoogleFonts.inter(
        fontSize: fontSize ?? 14.0,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
      );
    } catch (e) {
      return TextStyle(
        fontFamily: 'System',
        fontSize: fontSize ?? 14.0,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
      );
    }
  }

  // تحديد ما إذا كان النص عربي أم لا
  static bool isArabicText(String text) {
    if (text.isEmpty) return false;

    final arabicRange = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]');
    return arabicRange.hasMatch(text);
  }

  // اختيار الخط المناسب بناءً على النص
  static TextStyle getAutoTextStyle(
    String text, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    if (isArabicText(text)) {
      return getArabicTextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
      );
    } else {
      return getEnglishTextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
      );
    }
  }
}
