import 'dart:convert';
import 'package:http/http.dart' as http;

class StripePaymentService {
  static const String _baseUrl = 'https://api.stripe.com/v1';
  static const String _secretKey =
      'sk_test_your_stripe_secret_key'; // يجب أن يكون في الخادم فقط

  // إنشاء نية دفع لتمديد الإيجار
  Future<Map<String, dynamic>> createPaymentIntent({
    required double amount,
    required String currency,
    required String extensionId,
    required String customerId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payment_intents'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'amount': (amount * 100).round().toString(), // تحويل إلى فلس
          'currency': currency.toLowerCase(),
          'customer': customerId,
          'metadata[extension_id]': extensionId,
          'metadata[type]': 'rental_extension',
          if (metadata != null)
            ...metadata.map(
                (key, value) => MapEntry('metadata[$key]', value.toString())),
          'automatic_payment_methods[enabled]': 'true',
          'setup_future_usage': 'on_session', // لحفظ طريقة الدفع للمستقبل
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('فشل في إنشاء نية الدفع: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال بـ Stripe: ${e.toString()}');
    }
  }

  // تأكيد نية الدفع
  Future<Map<String, dynamic>> confirmPaymentIntent({
    required String paymentIntentId,
    required String paymentMethodId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payment_intents/$paymentIntentId/confirm'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'payment_method': paymentMethodId,
          'return_url': 'https://your-app.com/return', // استبدل بالرابط الحقيقي
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('فشل في تأكيد الدفعة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في تأكيد الدفعة: ${e.toString()}');
    }
  }

  // جلب تفاصيل نية الدفع
  Future<Map<String, dynamic>> retrievePaymentIntent(
      String paymentIntentId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/payment_intents/$paymentIntentId'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('فشل في جلب تفاصيل الدفعة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في جلب تفاصيل الدفعة: ${e.toString()}');
    }
  }

  // إنشاء عميل في Stripe
  Future<Map<String, dynamic>> createCustomer({
    required String email,
    required String name,
    String? phone,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/customers'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'email': email,
          'name': name,
          if (phone != null) 'phone': phone,
          if (metadata != null)
            ...metadata.map(
                (key, value) => MapEntry('metadata[$key]', value.toString())),
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('فشل في إنشاء العميل: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في إنشاء العميل: ${e.toString()}');
    }
  }

  // إنشاء رابط دفع مؤقت
  Future<Map<String, dynamic>> createPaymentLink({
    required double amount,
    required String currency,
    required String extensionId,
    String? customerEmail,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // أولاً إنشاء منتج مؤقت
      final productResponse = await http.post(
        Uri.parse('$_baseUrl/products'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'name': 'تمديد إيجار السيارة',
          'description': 'تمديد إيجار السيارة - رقم التمديد: $extensionId',
          'metadata[extension_id]': extensionId,
          'metadata[type]': 'rental_extension',
        },
      );

      if (productResponse.statusCode != 200) {
        throw Exception('فشل في إنشاء المنتج: ${productResponse.body}');
      }

      final product = jsonDecode(productResponse.body);

      // ثم إنشاء سعر للمنتج
      final priceResponse = await http.post(
        Uri.parse('$_baseUrl/prices'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'product': product['id'],
          'unit_amount': (amount * 100).round().toString(),
          'currency': currency.toLowerCase(),
        },
      );

      if (priceResponse.statusCode != 200) {
        throw Exception('فشل في إنشاء السعر: ${priceResponse.body}');
      }

      final price = jsonDecode(priceResponse.body);

      // أخيراً إنشاء رابط الدفع
      final linkResponse = await http.post(
        Uri.parse('$_baseUrl/payment_links'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'line_items[0][price]': price['id'],
          'line_items[0][quantity]': '1',
          if (customerEmail != null) 'customer_creation': 'always',
          'after_completion[type]': 'redirect',
          'after_completion[redirect][url]':
              'https://your-app.com/payment-success',
          if (metadata != null)
            ...metadata.map(
                (key, value) => MapEntry('metadata[$key]', value.toString())),
        },
      );

      if (linkResponse.statusCode == 200) {
        return jsonDecode(linkResponse.body);
      } else {
        throw Exception('فشل في إنشاء رابط الدفع: ${linkResponse.body}');
      }
    } catch (e) {
      throw Exception('خطأ في إنشاء رابط الدفع: ${e.toString()}');
    }
  }

  // استرداد المدفوعات
  Future<Map<String, dynamic>> createRefund({
    required String paymentIntentId,
    double? amount,
    String? reason,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/refunds'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'payment_intent': paymentIntentId,
          if (amount != null) 'amount': (amount * 100).round().toString(),
          if (reason != null) 'reason': reason,
          if (metadata != null)
            ...metadata.map(
                (key, value) => MapEntry('metadata[$key]', value.toString())),
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('فشل في الاسترداد: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاسترداد: ${e.toString()}');
    }
  }

  // التحقق من حالة الدفعة
  Future<PaymentStatus> getPaymentStatus(String paymentIntentId) async {
    try {
      final paymentIntent = await retrievePaymentIntent(paymentIntentId);
      final status = paymentIntent['status'];

      switch (status) {
        case 'succeeded':
          return PaymentStatus.succeeded;
        case 'processing':
          return PaymentStatus.processing;
        case 'requires_payment_method':
          return PaymentStatus.requiresPaymentMethod;
        case 'requires_confirmation':
          return PaymentStatus.requiresConfirmation;
        case 'requires_action':
          return PaymentStatus.requiresAction;
        case 'canceled':
          return PaymentStatus.canceled;
        default:
          return PaymentStatus.unknown;
      }
    } catch (e) {
      throw Exception('خطأ في التحقق من حالة الدفعة: ${e.toString()}');
    }
  }
}

enum PaymentStatus {
  succeeded,
  processing,
  requiresPaymentMethod,
  requiresConfirmation,
  requiresAction,
  canceled,
  unknown,
}

extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.succeeded:
        return 'تم الدفع بنجاح';
      case PaymentStatus.processing:
        return 'جاري المعالجة';
      case PaymentStatus.requiresPaymentMethod:
        return 'يتطلب طريقة دفع';
      case PaymentStatus.requiresConfirmation:
        return 'يتطلب تأكيد';
      case PaymentStatus.requiresAction:
        return 'يتطلب إجراء إضافي';
      case PaymentStatus.canceled:
        return 'تم الإلغاء';
      case PaymentStatus.unknown:
        return 'حالة غير معروفة';
    }
  }

  bool get isCompleted => this == PaymentStatus.succeeded;
  bool get isProcessing => this == PaymentStatus.processing;
  bool get requiresAction =>
      this == PaymentStatus.requiresAction ||
      this == PaymentStatus.requiresConfirmation ||
      this == PaymentStatus.requiresPaymentMethod;
}
