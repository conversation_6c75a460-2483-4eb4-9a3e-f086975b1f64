import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../constants/app_colors.dart';
import '../../models/notification_model.dart';
import '../../services/notification_service.dart';
import '../../providers/auth_provider.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final NotificationService _notificationService = NotificationService();

  bool _pushNotifications = true;
  bool _emailNotifications = true;
  bool _smsNotifications = false;
  bool _bookingNotifications = true;
  bool _promotionalNotifications = true;
  bool _maintenanceNotifications = true;

  List<NotificationModel> _notifications = [];
  bool _isLoading = true;
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
    _loadNotificationSettings();
  }

  Future<void> _loadNotifications() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated || authProvider.currentUser == null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final notifications = await _notificationService.getUserNotifications(
        userId: authProvider.currentUser!.id,
      );

      setState(() {
        _notifications = notifications;
        _unreadCount = notifications.where((n) => !n.isRead).length;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في تحميل الإشعارات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadNotificationSettings() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated || authProvider.currentUser == null) {
      return;
    }

    try {
      final settings = await _notificationService.getNotificationSettings(
        authProvider.currentUser!.id,
      );

      if (mounted) {
        setState(() {
          _pushNotifications = settings['push_notifications'] ?? true;
          _emailNotifications = settings['email_notifications'] ?? true;
          _smsNotifications = settings['sms_notifications'] ?? false;
          // ترك الأنواع الأخرى محلياً فقط حالياً (غير محفوظة في القاعدة)
        });
      }
    } catch (e) {
      // Silent error handling for settings
    }
  }

  Future<void> _updateNotificationSettings() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated || authProvider.currentUser == null) {
      return;
    }

    try {
      await _notificationService.updateNotificationSettings(
        userId: authProvider.currentUser!.id,
        pushNotifications: _pushNotifications,
        emailNotifications: _emailNotifications,
        smsNotifications: _smsNotifications,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في حفظ الإعدادات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _markAllAsRead() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated || authProvider.currentUser == null) {
      return;
    }

    try {
      await _notificationService.markAllAsRead(authProvider.currentUser!.id);

      setState(() {
        _unreadCount = 0;
        _notifications =
            _notifications.map((n) => n.copyWith(isRead: true)).toList();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم وضع علامة على جميع الإشعارات كمقروءة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في تحديث الإشعارات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإشعارات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        backgroundColor: Colors.grey[50],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (_unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: Text(
                'قراءة الجميع',
                style: GoogleFonts.cairo(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      backgroundColor: Colors.grey[50],
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  _buildNotificationSettings(),
                  const SizedBox(height: 16),
                  _buildNotificationsList(),
                ],
              ),
            ),
    );
  }

  Widget _buildNotificationSettings() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.settings, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                'إعدادات الإشعارات',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Push Notifications
          _buildSwitchTile(
            title: 'الإشعارات الفورية',
            subtitle: 'تلقي إشعارات على الهاتف',
            value: _pushNotifications,
            onChanged: (value) async {
              setState(() {
                _pushNotifications = value;
              });
              await _updateNotificationSettings();
            },
          ),

          const Divider(height: 24),

          // Email Notifications
          _buildSwitchTile(
            title: 'إشعارات البريد الإلكتروني',
            subtitle: 'تلقي إشعارات على البريد الإلكتروني',
            value: _emailNotifications,
            onChanged: (value) async {
              setState(() {
                _emailNotifications = value;
              });
              await _updateNotificationSettings();
            },
          ),

          const Divider(height: 24),

          // SMS Notifications
          _buildSwitchTile(
            title: 'الرسائل النصية',
            subtitle: 'تلقي رسائل نصية للمواعيد المهمة',
            value: _smsNotifications,
            onChanged: (value) async {
              setState(() {
                _smsNotifications = value;
              });
              await _updateNotificationSettings();
            },
          ),

          const SizedBox(height: 16),

          Text(
            'أنواع الإشعارات',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),

          const SizedBox(height: 12),

          _buildSwitchTile(
            title: 'إشعارات الحجز',
            subtitle: 'تأكيد الحجز، التذكيرات، التحديثات',
            value: _bookingNotifications,
            onChanged: (value) async {
              setState(() {
                _bookingNotifications = value;
              });
              await _updateNotificationSettings();
            },
          ),

          const Divider(height: 24),

          _buildSwitchTile(
            title: 'العروض والخصومات',
            subtitle: 'إشعارات العروض الخاصة والخصومات',
            value: _promotionalNotifications,
            onChanged: (value) async {
              setState(() {
                _promotionalNotifications = value;
              });
              await _updateNotificationSettings();
            },
          ),

          const Divider(height: 24),

          _buildSwitchTile(
            title: 'إشعارات الصيانة',
            subtitle: 'تحديثات النظام والصيانة المجدولة',
            value: _maintenanceNotifications,
            onChanged: (value) async {
              setState(() {
                _maintenanceNotifications = value;
              });
              await _updateNotificationSettings();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: AppColors.primary,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ],
    );
  }

  Widget _buildNotificationsList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.notifications, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                'الإشعارات الأخيرة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
              const Spacer(),
              if (_unreadCount > 0)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$_unreadCount جديد',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          if (_notifications.isEmpty) ...[
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.notifications_none,
                    size: 64,
                    color: AppColors.onSurface.withOpacity(0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد إشعارات',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      color: AppColors.onSurface.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ستظهر الإشعارات هنا عند توفرها',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.onSurface.withOpacity(0.5),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ] else ...[
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _notifications.length,
              separatorBuilder: (context, index) => const Divider(height: 24),
              itemBuilder: (context, index) {
                return _buildNotificationItem(_notifications[index]);
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    return InkWell(
      onTap: () async {
        if (!notification.isRead) {
          try {
            await _notificationService.markAsRead(notification.id);
            setState(() {
              final index = _notifications.indexOf(notification);
              if (index != -1) {
                _notifications[index] =
                    notification.copyWith(isRead: true, readAt: DateTime.now());
              }
              _unreadCount = _notifications.where((n) => !n.isRead).length;
            });
          } catch (e) {
            // Silent error handling
          }
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: notification.isRead
              ? Colors.transparent
              : AppColors.primary.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildNotificationIcon(notification.type),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          notification.title,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: notification.isRead
                                ? FontWeight.w600
                                : FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                        ),
                      ),
                      if (!notification.isRead)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification.message,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.onSurface.withOpacity(0.7),
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 14,
                        color: AppColors.onSurface.withOpacity(0.5),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatNotificationTime(notification.createdAt),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: AppColors.onSurface.withOpacity(0.5),
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getNotificationTypeColor(notification.type)
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _getNotificationTypeText(notification.type),
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: _getNotificationTypeColor(notification.type),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(NotificationType type) {
    IconData icon;
    Color color;

    switch (type) {
      case NotificationType.booking:
        icon = Icons.car_rental;
        color = Colors.blue;
        break;
      case NotificationType.payment:
        icon = Icons.payment;
        color = Colors.green;
        break;
      case NotificationType.promotional:
        icon = Icons.local_offer;
        color = Colors.orange;
        break;
      case NotificationType.system:
        icon = Icons.info;
        color = Colors.purple;
        break;
      case NotificationType.reminder:
        icon = Icons.schedule;
        color = Colors.red;
        break;
      case NotificationType.maintenance:
        icon = Icons.build;
        color = Colors.redAccent;
        break;
      case NotificationType.verification:
        icon = Icons.verified_user;
        color = Colors.indigo;
        break;
      case NotificationType.welcome:
        icon = Icons.waving_hand;
        color = Colors.teal;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }

  Color _getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.booking:
        return Colors.blue;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.promotional:
        return Colors.orange;
      case NotificationType.system:
        return Colors.purple;
      case NotificationType.reminder:
        return Colors.red;
      case NotificationType.maintenance:
        return Colors.redAccent;
      case NotificationType.verification:
        return Colors.indigo;
      case NotificationType.welcome:
        return Colors.teal;
    }
  }

  String _getNotificationTypeText(NotificationType type) {
    switch (type) {
      case NotificationType.booking:
        return 'حجز';
      case NotificationType.payment:
        return 'دفع';
      case NotificationType.promotional:
        return 'عرض';
      case NotificationType.system:
        return 'نظام';
      case NotificationType.reminder:
        return 'تذكير';
      case NotificationType.maintenance:
        return 'صيانة';
      case NotificationType.verification:
        return 'تحقق';
      case NotificationType.welcome:
        return 'ترحيب';
    }
  }

  String _formatNotificationTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم${difference.inDays == 1 ? '' : 'ًا'}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة${difference.inHours == 1 ? '' : 'ًا'}';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة${difference.inMinutes == 1 ? '' : 'ًا'}';
    } else {
      return 'الآن';
    }
  }
}
