import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LogoConfig {
  static const String logoPath = 'assets/logos/app_logo.png';
  static const String appName = 'دبي كار رنتل';
  static const String appSlogan = 'أفضل خدمة تأجير السيارات في دبي';

  // ألوان الشعار الجديد (النسر الأزرق)
  static const Color primaryBlue = Color(0xFF1E88E5); // أزرق النسر
  static const Color darkBlue = Color(0xFF1565C0); // أزرق داكن
  static const Color lightBlue = Color(0xFF42A5F5); // أزرق فاتح

  // أحجام الشعار المختلفة
  static const double miniSize = 24;
  static const double smallSize = 32;
  static const double mediumSize = 48;
  static const double largeSize = 64;
  static const double splashSize = 140;

  // بناء الشعار مع معالجة محسّنة للأخطاء
  static Widget buildLogo({
    required double size,
    BoxFit fit = BoxFit.contain,
  }) {
    return FutureBuilder<bool>(
      future: _checkAssetExists(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingLogo(size);
        }

        if (snapshot.hasData && snapshot.data == true) {
          return Image.asset(
            logoPath,
            width: size,
            height: size,
            fit: fit,
            errorBuilder: (context, error, stackTrace) {
              print('فشل تحميل الشعار: $error');
              return _buildDefaultLogo(size);
            },
          );
        }

        return _buildDefaultLogo(size);
      },
    );
  }

  // التحقق من وجود الملف
  static Future<bool> _checkAssetExists() async {
    try {
      await rootBundle.load(logoPath);
      return true;
    } catch (e) {
      print('الشعار غير موجود في: $logoPath');
      return false;
    }
  }

  // شعار التحميل
  static Widget _buildLoadingLogo(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: primaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(size * 0.2),
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.3,
          height: size * 0.3,
          child: CircularProgressIndicator(
            color: primaryBlue,
            strokeWidth: 2,
          ),
        ),
      ),
    );
  }

  // الشعار الافتراضي (النسر الأزرق)
  static Widget _buildDefaultLogo(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [lightBlue, primaryBlue, darkBlue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(size * 0.25), // زوايا أكثر انحناء
        boxShadow: [
          BoxShadow(
            color: primaryBlue.withOpacity(0.3),
            blurRadius: size * 0.1,
            offset: Offset(0, size * 0.05),
          ),
        ],
      ),
      child: Stack(
        children: [
          // تأثير الإضاءة العلوية
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(size * 0.25),
              gradient: RadialGradient(
                colors: [
                  Colors.white.withOpacity(0.4),
                  Colors.white.withOpacity(0.2),
                  Colors.transparent,
                ],
                center: const Alignment(-0.2, -0.3),
                radius: 0.7,
              ),
            ),
          ),
          // رمز النسر المحسّن
          Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // ظل النسر
                Transform.translate(
                  offset: Offset(1, 1),
                  child: Icon(
                    Icons.south_america_outlined, // رمز يشبه النسر أكثر
                    color: Colors.black.withOpacity(0.2),
                    size: size * 0.65,
                  ),
                ),
                // النسر الرئيسي
                Icon(
                  Icons.south_america_outlined,
                  color: Colors.white,
                  size: size * 0.65,
                ),
                // تفاصيل إضافية للنسر
                Positioned(
                  top: size * 0.2,
                  child: Container(
                    width: size * 0.08,
                    height: size * 0.08,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
