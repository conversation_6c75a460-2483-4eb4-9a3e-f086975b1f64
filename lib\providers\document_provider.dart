import 'package:flutter/foundation.dart';

enum DocumentType {
  emiratesIdFront,
  emiratesIdBack,
  drivingLicense,
  passport,
}

enum VerificationStatus {
  notSubmitted,
  pending,
  verified,
  rejected,
}

class DocumentInfo {
  final DocumentType type;
  final String? url;
  final VerificationStatus status;
  final String? rejectionReason;
  final DateTime? uploadedAt;
  final DateTime? verifiedAt;

  DocumentInfo({
    required this.type,
    this.url,
    this.status = VerificationStatus.notSubmitted,
    this.rejectionReason,
    this.uploadedAt,
    this.verifiedAt,
  });

  DocumentInfo copyWith({
    DocumentType? type,
    String? url,
    VerificationStatus? status,
    String? rejectionReason,
    DateTime? uploadedAt,
    DateTime? verifiedAt,
  }) {
    return DocumentInfo(
      type: type ?? this.type,
      url: url ?? this.url,
      status: status ?? this.status,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      verifiedAt: verifiedAt ?? this.verifiedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'url': url,
      'status': status.name,
      'rejection_reason': rejectionReason,
      'uploaded_at': uploadedAt?.toIso8601String(),
      'verified_at': verifiedAt?.toIso8601String(),
    };
  }

  factory DocumentInfo.fromJson(Map<String, dynamic> json) {
    return DocumentInfo(
      type: DocumentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => DocumentType.emiratesIdFront,
      ),
      url: json['url'],
      status: VerificationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => VerificationStatus.notSubmitted,
      ),
      rejectionReason: json['rejection_reason'],
      uploadedAt: json['uploaded_at'] != null
          ? DateTime.parse(json['uploaded_at'])
          : null,
      verifiedAt: json['verified_at'] != null
          ? DateTime.parse(json['verified_at'])
          : null,
    );
  }
}

class DocumentProvider with ChangeNotifier {
  Map<DocumentType, DocumentInfo> _documents = {};
  bool _isUploading = false;
  String? _error;
  VerificationStatus _verificationStatus = VerificationStatus.notSubmitted;
  String? _verificationNotes;

  // Getters
  Map<DocumentType, DocumentInfo> get documents => _documents;
  bool get isUploading => _isUploading;
  String? get error => _error;
  VerificationStatus get verificationStatus => _verificationStatus;
  String? get verificationNotes => _verificationNotes;

  String? get emiratesIdFrontUrl =>
      _documents[DocumentType.emiratesIdFront]?.url;
  String? get emiratesIdBackUrl => _documents[DocumentType.emiratesIdBack]?.url;
  String? get drivingLicenseUrl => _documents[DocumentType.drivingLicense]?.url;
  String? get passportUrl => _documents[DocumentType.passport]?.url;

  bool get isDocumentsVerified =>
      _verificationStatus == VerificationStatus.verified;

  bool get areRequiredDocumentsUploaded {
    return _documents[DocumentType.emiratesIdFront]?.url != null &&
        _documents[DocumentType.emiratesIdBack]?.url != null &&
        _documents[DocumentType.drivingLicense]?.url != null;
  }

  // Methods
  Future<void> loadDocuments() async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      // Load documents from API
      _error = null;
    } catch (e) {
      _error = e.toString();
    }
    _setLoading(false);
  }

  Future<String?> uploadDocument(DocumentType type, String imagePath) async {
    _setLoading(true);
    try {
      // Simulate upload
      await Future.delayed(const Duration(seconds: 2));

      final now = DateTime.now();
      _documents[type] = DocumentInfo(
        type: type,
        url: 'https://example.com/documents/${type.name}_$now.jpg',
        status: VerificationStatus.pending,
        uploadedAt: now,
      );

      _updateVerificationStatus();
      _error = null;
      notifyListeners();
      return null; // Success
    } catch (e) {
      _error = e.toString();
      return _error;
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> uploadEmiratesIdFront(String imagePath) async {
    return await uploadDocument(DocumentType.emiratesIdFront, imagePath);
  }

  Future<String?> uploadEmiratesIdBack(String imagePath) async {
    return await uploadDocument(DocumentType.emiratesIdBack, imagePath);
  }

  Future<String?> uploadDrivingLicense(String imagePath) async {
    return await uploadDocument(DocumentType.drivingLicense, imagePath);
  }

  Future<String?> uploadPassport(String imagePath) async {
    return await uploadDocument(DocumentType.passport, imagePath);
  }

  Future<String?> deleteDocument(DocumentType type) async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      _documents.remove(type);
      _updateVerificationStatus();
      _error = null;
      notifyListeners();
      return null; // Success
    } catch (e) {
      _error = e.toString();
      return _error;
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> submitForVerification() async {
    if (!areRequiredDocumentsUploaded) {
      return 'Please upload all required documents first';
    }

    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      _verificationStatus = VerificationStatus.pending;
      _error = null;
      notifyListeners();
      return null; // Success
    } catch (e) {
      _error = e.toString();
      return _error;
    } finally {
      _setLoading(false);
    }
  }

  DocumentInfo? getDocument(DocumentType type) {
    return _documents[type];
  }

  void _updateVerificationStatus() {
    if (_documents.isEmpty) {
      _verificationStatus = VerificationStatus.notSubmitted;
    } else if (areRequiredDocumentsUploaded) {
      // Check if any document is rejected
      final hasRejected = _documents.values
          .any((doc) => doc.status == VerificationStatus.rejected);

      if (hasRejected) {
        _verificationStatus = VerificationStatus.rejected;
      } else {
        // Check if all documents are verified
        final allVerified = _documents.values
            .every((doc) => doc.status == VerificationStatus.verified);

        if (allVerified) {
          _verificationStatus = VerificationStatus.verified;
        } else {
          _verificationStatus = VerificationStatus.pending;
        }
      }
    } else {
      _verificationStatus = VerificationStatus.notSubmitted;
    }
  }

  void _setLoading(bool loading) {
    _isUploading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  String getDocumentStatusText(DocumentType type) {
    final doc = _documents[type];
    if (doc == null) return 'Not uploaded';

    switch (doc.status) {
      case VerificationStatus.notSubmitted:
        return 'Not uploaded';
      case VerificationStatus.pending:
        return 'Under review';
      case VerificationStatus.verified:
        return 'Verified';
      case VerificationStatus.rejected:
        return 'Rejected';
    }
  }

  String getDocumentTypeDisplayName(DocumentType type) {
    switch (type) {
      case DocumentType.emiratesIdFront:
        return 'Emirates ID (Front)';
      case DocumentType.emiratesIdBack:
        return 'Emirates ID (Back)';
      case DocumentType.drivingLicense:
        return 'Driving License';
      case DocumentType.passport:
        return 'Passport';
    }
  }

  bool isDocumentRequired(DocumentType type) {
    switch (type) {
      case DocumentType.emiratesIdFront:
      case DocumentType.emiratesIdBack:
      case DocumentType.drivingLicense:
        return true;
      case DocumentType.passport:
        return false;
    }
  }

  // Load user documents from backend
  Future<void> loadUserDocuments() async {
    try {
      _setLoading(true);
      _setError(null);

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Initialize empty documents
      for (DocumentType type in DocumentType.values) {
        _documents[type] = DocumentInfo(type: type);
      }

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Submit documents for review
  Future<bool> submitDocumentsForReview() async {
    try {
      _setLoading(true);
      _setError(null);

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Update verification status
      _verificationStatus = VerificationStatus.pending;
      notifyListeners();

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }
}
