import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';

class ReviewsScreen extends StatelessWidget {
  final String carId;

  const ReviewsScreen({
    super.key,
    required this.carId,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.reviews),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppTheme.royalBlue),
      ),
      body: const Center(
        child: Text(
          'Reviews Screen - Coming Soon',
          style: TextStyle(
            fontSize: 18,
            color: AppTheme.mediumGrey,
          ),
        ),
      ),
    );
  }
}
