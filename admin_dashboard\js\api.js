// ملف JavaScript للتعامل مع API ووظائف قاعدة البيانات

// إعدادات API المحسنة
const SUPABASE_CONFIG = {
    url: 'https://zvzaixlygdhloganycjt.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2emFpeGx5Z2RobG9nYW55Y2p0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjE4ODUsImV4cCI6MjA3MDM5Nzg4NX0.xI_qvs6kupVt0EM6nnFzgfe1QBmUkelNO7c0O64GCXQ'
};

const API_CONFIG = {
    baseURL: SUPABASE_CONFIG.url,
    timeout: 30000, // زيادة المهلة الزمنية إلى 30 ثانية
    retryAttempts: 3, // عدد محاولات الإعادة
    retryDelay: 2000, // تأخير بين المحاولات
    headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_CONFIG.anon<PERSON><PERSON>,
        'Authorization': `Bearer ${SUPABASE_CONFIG.anonKey}`,
        'Prefer': 'return=minimal'
    }
};

// حالة الاتصال
let connectionStatus = {
    isOnline: navigator.onLine,
    lastCheck: Date.now(),
    failedRequests: 0
};

// التحقق من حالة الاتصال
async function checkConnection() {
    try {
        const response = await fetch(`${SUPABASE_CONFIG.url}/rest/v1/`);
        connectionStatus.isOnline = response.ok;
        connectionStatus.lastCheck = Date.now();
        connectionStatus.failedRequests = 0;
        return true;
    } catch (error) {
        connectionStatus.isOnline = false;
        connectionStatus.failedRequests++;
        console.error('خطأ في الاتصال:', error);
        return false;
    }
}

// فئة API المحسنة للتعامل مع الطلبات
class APIClient {
    constructor(config = API_CONFIG) {
        this.baseURL = config.baseURL;
        this.timeout = config.timeout;
        this.headers = config.headers;
        this.retryAttempts = config.retryAttempts;
        this.retryDelay = config.retryDelay;
        this.requestQueue = [];
        this.isProcessingQueue = false;
    }
    
    // طلب GET محسن مع إعادة المحاولة
    async get(endpoint, params = {}) {
        return await this.executeWithRetry(async () => {
            const url = new URL(`${this.baseURL}/rest/v1/${endpoint}`);
            
            // إضافة المعاملات إلى URL
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== undefined) {
                    if (Array.isArray(params[key])) {
                        params[key].forEach(value => url.searchParams.append(key, value));
                    } else {
                        url.searchParams.append(key, params[key]);
                    }
                }
            });
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            
            try {
                const response = await fetch(url.toString(), {
                    method: 'GET',
                    headers: this.headers,
                    signal: controller.signal,
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                clearTimeout(timeoutId);
                return await this.handleResponse(response);
            } catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        });
    }
    
    // طلب POST محسن
    async post(endpoint, data = {}) {
        return await this.executeWithRetry(async () => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            
            try {
                const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify(data),
                signal: controller.signal,
                mode: 'cors',
                credentials: 'omit',
                cache: 'no-cache',
                redirect: 'follow'
            });
                
                clearTimeout(timeoutId);
                return await this.handleResponse(response);
            } catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        });
    }
    
    // طلب PUT محسن
    async put(endpoint, data = {}) {
        return await this.executeWithRetry(async () => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            
            try {
                const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'PUT',
                headers: this.headers,
                body: JSON.stringify(data),
                signal: controller.signal,
                mode: 'cors',
                credentials: 'omit',
                cache: 'no-cache',
                redirect: 'follow'
            });
                
                clearTimeout(timeoutId);
                return await this.handleResponse(response);
            } catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        });
    }
    
    // طلب DELETE
    async delete(endpoint) {
        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'DELETE',
                headers: this.headers,
                signal: AbortSignal.timeout(this.timeout),
                mode: 'cors',
                credentials: 'omit',
                cache: 'no-cache',
                redirect: 'follow'
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    }
    
    // طلب PUT/POST مع ملفات
    async postWithFiles(endpoint, formData) {
        try {
            const headers = { ...this.headers };
            delete headers['Content-Type']; // للملفات، لا نضع Content-Type
            
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'POST',
                headers: headers,
                body: formData,
                signal: AbortSignal.timeout(this.timeout),
                mode: 'cors',
                credentials: 'omit',
                cache: 'no-cache',
                redirect: 'follow'
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    }
    
    // تنفيذ الطلب مع إعادة المحاولة
    async executeWithRetry(requestFunction) {
        let lastError;
        
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const result = await requestFunction();
                connectionStatus.failedRequests = 0; // إعادة تعيين عداد الأخطاء
                return result;
            } catch (error) {
                lastError = error;
                connectionStatus.failedRequests++;
                
                console.warn(`محاولة ${attempt} فشلت:`, error.message);
                
                // عدم إعادة المحاولة في حالات معينة
                if (this.shouldNotRetry(error) || attempt === this.retryAttempts) {
                    break;
                }
                
                // انتظار قبل إعادة المحاولة
                await this.delay(this.retryDelay * attempt);
            }
        }
        
        throw this.handleError(lastError);
    }
    
    // تحديد ما إذا كان يجب عدم إعادة المحاولة
    shouldNotRetry(error) {
        // عدم إعادة المحاولة للأخطاء 4xx (عدا 429)
        if (error.status >= 400 && error.status < 500 && error.status !== 429) {
            return true;
        }
        
        // عدم إعادة المحاولة لأخطاء التفويض
        if (error.status === 401 || error.status === 403) {
            return true;
        }
        
        return false;
    }
    
    // تأخير
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // معالجة الاستجابة المحسنة
    async handleResponse(response) {
        // تحديث حالة الاتصال
        connectionStatus.isOnline = true;
        connectionStatus.lastCheck = Date.now();
        
        if (!response.ok) {
            let errorData = {};
            try {
                const text = await response.text();
                errorData = text ? JSON.parse(text) : {};
            } catch (e) {
                // تجاهل أخطاء تحليل JSON
            }
            
            const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            error.status = response.status;
            error.statusText = response.statusText;
            error.details = errorData;
            throw error;
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            const text = await response.text();
            return text ? JSON.parse(text) : null;
        }
        
        return await response.text();
    }
    
    // معالجة الأخطاء المحسنة
    handleError(error) {
        console.error('API Error:', error);
        
        // تحديث حالة الاتصال
        if (error.name === 'AbortError' || error.name === 'TypeError') {
            connectionStatus.isOnline = false;
        }
        
        // رسائل خطأ مخصصة
        if (error.name === 'AbortError') {
            return new Error('انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.');
        }
        
        if (!navigator.onLine) {
            return new Error('لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال.');
        }
        
        if (error.status === 401) {
            return new Error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
        }
        
        if (error.status === 403) {
            return new Error('ليس لديك صلاحية للوصول إلى هذا المورد.');
        }
        
        if (error.status === 404) {
            return new Error('المورد المطلوب غير موجود.');
        }
        
        if (error.status === 429) {
            return new Error('تم تجاوز حد الطلبات. يرجى المحاولة لاحقاً.');
        }
        
        if (error.status >= 500) {
            return new Error('خطأ في الخادم. يرجى المحاولة لاحقاً.');
        }
        
        return error;
    }
}

// إنشاء instance من API client
const apiClient = new APIClient();

// =========================
// دوال API للسيارات
// =========================

// جلب جميع السيارات
async function fetchCars(page = 1, limit = 10, filters = {}) {
    try {
        // التأكد من صحة القيم
        const validPage = Math.max(1, parseInt(page) || 1);
        const validLimit = Math.max(1, parseInt(limit) || 10);
        
        const params = {
            limit: validLimit,
            offset: (validPage - 1) * validLimit,
            order: 'created_at.desc'
        };
        
        // إضافة الفلاتر
        if (filters.status) {
            params['status'] = `eq.${filters.status}`;
        }
        if (filters.category) {
            params['category'] = `eq.${filters.category}`;
        }
        if (filters.make) {
            params['make'] = `eq.${filters.make}`;
        }
        if (filters.search) {
            params['or'] = `name.ilike.%${filters.search}%,make.ilike.%${filters.search}%,model.ilike.%${filters.search}%`;
        }
        if (filters.availability_status) {
            params['availability_status'] = `eq.${filters.availability_status}`;
        }
        
        const response = await apiClient.get('/cars', params);
        
        // جلب العدد الإجمالي
        const countParams = { ...params };
        delete countParams.limit;
        delete countParams.offset;
        delete countParams.order;
        countParams.select = 'count';
        
        const countResponse = await apiClient.get('/cars', countParams);
        
        return {
            data: response || [],
            total: countResponse?.[0]?.count || 0,
            page: validPage,
            limit: validLimit,
            totalPages: Math.ceil((countResponse?.[0]?.count || 0) / validLimit)
        };
    } catch (error) {
        console.error('خطأ في جلب السيارات:', error);
        throw error;
    }
}

// جلب سيارة بالـ ID
async function fetchCarById(carId) {
    try {
        const response = await apiClient.get(`/cars/${carId}`);
        return response;
    } catch (error) {
        console.error('خطأ في جلب تفاصيل السيارة:', error);
        throw error;
    }
}

// إضافة سيارة جديدة
async function createCar(carData) {
    try {
        const response = await apiClient.post('/cars', carData);
        return response;
    } catch (error) {
        console.error('خطأ في إضافة السيارة:', error);
        throw error;
    }
}

// تحديث سيارة
async function updateCar(carId, carData) {
    try {
        const response = await apiClient.put(`/cars/${carId}`, carData);
        return response;
    } catch (error) {
        console.error('خطأ في تحديث السيارة:', error);
        throw error;
    }
}

// حذف سيارة
async function deleteCar(carId) {
    try {
        const response = await apiClient.delete(`/cars/${carId}`);
        return response;
    } catch (error) {
        console.error('خطأ في حذف السيارة:', error);
        throw error;
    }
}

// رفع صور السيارة
async function uploadCarImages(carId, images) {
    try {
        const formData = new FormData();
        Array.from(images).forEach((image, index) => {
            formData.append(`images`, image);
        });
        
        const response = await apiClient.postWithFiles(`/cars/${carId}/images`, formData);
        return response;
    } catch (error) {
        console.error('خطأ في رفع صور السيارة:', error);
        throw error;
    }
}

// =========================
// دوال API للحجوزات
// =========================

// جلب جميع الحجوزات
async function fetchBookings(page = 1, limit = 10, filters = {}) {
    try {
        // التأكد من صحة القيم
        const validPage = Math.max(1, parseInt(page) || 1);
        const validLimit = Math.max(1, parseInt(limit) || 10);
        
        const params = {
            limit: validLimit,
            offset: (validPage - 1) * validLimit,
            order: 'created_at.desc',
            select: '*,cars(name,make,model),profiles(full_name,phone)'
        };
        
        // إضافة الفلاتر
        if (filters.status) {
            params['status'] = `eq.${filters.status}`;
        }
        if (filters.payment_status) {
            params['payment_status'] = `eq.${filters.payment_status}`;
        }
        if (filters.start_date) {
            params['start_date'] = `gte.${filters.start_date}`;
        }
        if (filters.end_date) {
            params['end_date'] = `lte.${filters.end_date}`;
        }
        if (filters.search) {
            params['or'] = `booking_number.ilike.%${filters.search}%,customer_name.ilike.%${filters.search}%`;
        }
        
        const response = await apiClient.get('/bookings', params);
        
        // جلب العدد الإجمالي
        const countParams = { ...params };
        delete countParams.limit;
        delete countParams.offset;
        delete countParams.order;
        delete countParams.select;
        countParams.select = 'count';
        
        const countResponse = await apiClient.get('/bookings', countParams);
        
        return {
            data: response || [],
            total: countResponse?.[0]?.count || 0,
            page: validPage,
            limit: validLimit,
            totalPages: Math.ceil((countResponse?.[0]?.count || 0) / validLimit)
        };
    } catch (error) {
        console.error('خطأ في جلب الحجوزات:', error);
        throw error;
    }
}

// جلب حجز بالـ ID
async function fetchBookingById(bookingId) {
    try {
        const response = await apiClient.get(`/bookings/${bookingId}`);
        return response;
    } catch (error) {
        console.error('خطأ في جلب تفاصيل الحجز:', error);
        throw error;
    }
}

// إنشاء حجز جديد
async function createBooking(bookingData) {
    try {
        const response = await apiClient.post('/bookings', bookingData);
        return response;
    } catch (error) {
        console.error('خطأ في إنشاء الحجز:', error);
        throw error;
    }
}

// تحديث حالة الحجز
async function updateBookingStatus(bookingId, status, notes = '') {
    try {
        const response = await apiClient.put(`/bookings/${bookingId}/status`, {
            status,
            notes
        });
        return response;
    } catch (error) {
        console.error('خطأ في تحديث حالة الحجز:', error);
        throw error;
    }
}

// إلغاء الحجز
async function cancelBooking(bookingId, reason = '') {
    try {
        const response = await apiClient.put(`/bookings/${bookingId}/cancel`, {
            cancellation_reason: reason
        });
        return response;
    } catch (error) {
        console.error('خطأ في إلغاء الحجز:', error);
        throw error;
    }
}

// =========================
// دوال API للمستخدمين
// =========================

// جلب جميع المستخدمين
async function fetchUsers(page = 1, limit = 10, filters = {}) {
    try {
        // التأكد من صحة القيم
        const validPage = Math.max(1, parseInt(page) || 1);
        const validLimit = Math.max(1, parseInt(limit) || 10);
        
        const params = {
            limit: validLimit,
            offset: (validPage - 1) * validLimit,
            order: 'created_at.desc',
            select: '*'
        };
        
        // إضافة الفلاتر
        if (filters.status) {
            params['status'] = `eq.${filters.status}`;
        }
        if (filters.verification_status) {
            params['verification_status'] = `eq.${filters.verification_status}`;
        }
        if (filters.search) {
            params['or'] = `full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`;
        }
        if (filters.user_type) {
            params['user_type'] = `eq.${filters.user_type}`;
        }
        
        const response = await apiClient.get('/profiles', params);
        
        // جلب العدد الإجمالي
        const countParams = { ...params };
        delete countParams.limit;
        delete countParams.offset;
        delete countParams.order;
        delete countParams.select;
        countParams.select = 'count';
        
        const countResponse = await apiClient.get('/profiles', countParams);
        
        return {
            data: response || [],
            total: countResponse?.[0]?.count || 0,
            page: validPage,
            limit: validLimit,
            totalPages: Math.ceil((countResponse?.[0]?.count || 0) / validLimit)
        };
    } catch (error) {
        console.error('خطأ في جلب المستخدمين:', error);
        throw error;
    }
}

// جلب مستخدم بالـ ID
async function fetchUserById(userId) {
    try {
        const response = await apiClient.get(`/users/${userId}`);
        return response;
    } catch (error) {
        console.error('خطأ في جلب تفاصيل المستخدم:', error);
        throw error;
    }
}

// تحديث حالة المستخدم
async function updateUserStatus(userId, status) {
    try {
        const response = await apiClient.put(`/users/${userId}/status`, {
            status
        });
        return response;
    } catch (error) {
        console.error('خطأ في تحديث حالة المستخدم:', error);
        throw error;
    }
}

// تحقيق المستخدم
async function verifyUser(userId, verification_status, notes = '') {
    try {
        const response = await apiClient.put(`/users/${userId}/verify`, {
            verification_status,
            verification_notes: notes
        });
        return response;
    } catch (error) {
        console.error('خطأ في تحقيق المستخدم:', error);
        throw error;
    }
}

// =========================
// دوال API للمدفوعات
// =========================

// جلب جميع المدفوعات
async function fetchPayments(page = 1, limit = 10, filters = {}) {
    try {
        // التأكد من صحة القيم
        const validPage = Math.max(1, parseInt(page) || 1);
        const validLimit = Math.max(1, parseInt(limit) || 10);
        
        const params = {
            limit: validLimit,
            offset: (validPage - 1) * validLimit,
            order: 'created_at.desc',
            select: '*,bookings(booking_number,customer_name,total_amount),profiles(full_name,email)'
        };
        
        // إضافة الفلاتر
        if (filters.status) {
            params['status'] = `eq.${filters.status}`;
        }
        if (filters.payment_method) {
            params['payment_method'] = `eq.${filters.payment_method}`;
        }
        if (filters.start_date) {
            params['created_at'] = `gte.${filters.start_date}`;
        }
        if (filters.end_date) {
            params['created_at'] = `lte.${filters.end_date}`;
        }
        if (filters.search) {
            params['or'] = `transaction_id.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%`;
        }
        
        const response = await apiClient.get('/payments', params);
        
        // جلب العدد الإجمالي
        const countParams = { ...params };
        delete countParams.limit;
        delete countParams.offset;
        delete countParams.order;
        delete countParams.select;
        countParams.select = 'count';
        
        const countResponse = await apiClient.get('/payments', countParams);
        
        return {
            data: response || [],
            total: countResponse?.[0]?.count || 0,
            page: validPage,
            limit: validLimit,
            totalPages: Math.ceil((countResponse?.[0]?.count || 0) / validLimit)
        };
    } catch (error) {
        console.error('خطأ في جلب المدفوعات:', error);
        throw error;
    }
}

// استرداد مبلغ
async function refundPayment(paymentId, amount, reason = '') {
    try {
        const response = await apiClient.post(`/payments/${paymentId}/refund`, {
            refund_amount: amount,
            refund_reason: reason
        });
        return response;
    } catch (error) {
        console.error('خطأ في استرداد المبلغ:', error);
        throw error;
    }
}

// =========================
// دوال API للتقييمات
// =========================

// جلب جميع التقييمات
async function fetchReviews(page = 1, limit = 10, filters = {}) {
    try {
        const params = {
            page,
            limit,
            ...filters
        };
        
        const response = await apiClient.get('/reviews', params);
        return response;
    } catch (error) {
        console.error('خطأ في جلب التقييمات:', error);
        throw error;
    }
}

// تحقيق التقييم
async function verifyReview(reviewId) {
    try {
        const response = await apiClient.put(`/reviews/${reviewId}/verify`);
        return response;
    } catch (error) {
        console.error('خطأ في تحقيق التقييم:', error);
        throw error;
    }
}

// حذف التقييم
async function deleteReview(reviewId) {
    try {
        const response = await apiClient.delete(`/reviews/${reviewId}`);
        return response;
    } catch (error) {
        console.error('خطأ في حذف التقييم:', error);
        throw error;
    }
}

// =========================
// دوال API للمواقع
// =========================

// جلب جميع المواقع
async function fetchLocations() {
    try {
        const response = await apiClient.get('/locations');
        return response;
    } catch (error) {
        console.error('خطأ في جلب المواقع:', error);
        throw error;
    }
}

// إضافة موقع جديد
async function createLocation(locationData) {
    try {
        const response = await apiClient.post('/locations', locationData);
        return response;
    } catch (error) {
        console.error('خطأ في إضافة الموقع:', error);
        throw error;
    }
}

// تحديث الموقع
async function updateLocation(locationId, locationData) {
    try {
        const response = await apiClient.put(`/locations/${locationId}`, locationData);
        return response;
    } catch (error) {
        console.error('خطأ في تحديث الموقع:', error);
        throw error;
    }
}

// حذف الموقع
async function deleteLocation(locationId) {
    try {
        const response = await apiClient.delete(`/locations/${locationId}`);
        return response;
    } catch (error) {
        console.error('خطأ في حذف الموقع:', error);
        throw error;
    }
}

// =========================
// دوال API للكوبونات
// =========================

// جلب جميع الكوبونات
async function fetchCoupons(page = 1, limit = 10) {
    try {
        const params = { page, limit };
        const response = await apiClient.get('/coupons', params);
        return response;
    } catch (error) {
        console.error('خطأ في جلب الكوبونات:', error);
        throw error;
    }
}

// إضافة كوبون جديد
async function createCoupon(couponData) {
    try {
        const response = await apiClient.post('/coupons', couponData);
        return response;
    } catch (error) {
        console.error('خطأ في إضافة الكوبون:', error);
        throw error;
    }
}

// تحديث الكوبون
async function updateCoupon(couponId, couponData) {
    try {
        const response = await apiClient.put(`/coupons/${couponId}`, couponData);
        return response;
    } catch (error) {
        console.error('خطأ في تحديث الكوبون:', error);
        throw error;
    }
}

// حذف الكوبون
async function deleteCoupon(couponId) {
    try {
        const response = await apiClient.delete(`/coupons/${couponId}`);
        return response;
    } catch (error) {
        console.error('خطأ في حذف الكوبون:', error);
        throw error;
    }
}

// =========================
// دوال API للإحصائيات
// =========================

// جلب إحصائيات لوحة التحكم
async function fetchDashboardStats(startDate = null, endDate = null) {
    try {
        // جلب جميع السيارات
        const allCars = await apiClient.get('/cars', {
            select: 'id,availability_status'
        });
        
        // جلب جميع الحجوزات
        let bookingsParams = {
            select: 'id,status,total_amount,created_at'
        };
        if (startDate) bookingsParams['created_at'] = `gte.${startDate}`;
        if (endDate) bookingsParams['created_at'] = `lte.${endDate}`;
        
        const allBookings = await apiClient.get('/bookings', bookingsParams);
        
        // جلب جميع المستخدمين
        const allUsers = await apiClient.get('/profiles', {
            select: 'id,status'
        });
        
        // حساب الإحصائيات
        const totalCars = allCars?.length || 0;
        const availableCars = allCars?.filter(c => c.availability_status === 'available')?.length || 0;
        const totalBookings = allBookings?.length || 0;
        const activeBookings = allBookings?.filter(b => b.status === 'active')?.length || 0;
        const totalUsers = allUsers?.length || 0;
        const verifiedUsers = allUsers?.filter(u => u.status === 'active')?.length || 0;
        
        // حساب الإيرادات
        const completedBookings = allBookings?.filter(b => b.status === 'completed') || [];
        const totalRevenue = completedBookings.reduce((sum, booking) => {
            return sum + (parseFloat(booking.total_amount) || 0);
        }, 0);
        
        return {
            totalCars,
            availableCars,
            totalBookings,
            activeBookings,
            totalUsers,
            verifiedUsers,
            totalRevenue,
            monthlyRevenue: totalRevenue // مؤقتاً نفس القيمة
        };
    } catch (error) {
        console.error('خطأ في جلب إحصائيات لوحة التحكم:', error);
        throw error;
    }
}

// جلب إحصائيات الإيرادات
async function fetchRevenueStats(period = 'month') {
    try {
        const response = await apiClient.get('/stats/revenue', { period });
        return response;
    } catch (error) {
        console.error('خطأ في جلب إحصائيات الإيرادات:', error);
        throw error;
    }
}

// جلب إحصائيات الحجوزات
async function fetchBookingStats(period = 'month') {
    try {
        const response = await apiClient.get('/stats/bookings', { period });
        return response;
    } catch (error) {
        console.error('خطأ في جلب إحصائيات الحجوزات:', error);
        throw error;
    }
}

// =========================
// دوال API للإعدادات
// =========================

// جلب إعدادات التطبيق
async function fetchAppSettings() {
    try {
        const response = await apiClient.get('/settings');
        return response;
    } catch (error) {
        console.error('خطأ في جلب إعدادات التطبيق:', error);
        throw error;
    }
}

// حفظ إعدادات التطبيق
async function saveAppSettings(settings) {
    try {
        const response = await apiClient.put('/settings', settings);
        return response;
    } catch (error) {
        console.error('خطأ في حفظ إعدادات التطبيق:', error);
        throw error;
    }
}

// =========================
// دوال مساعدة لمعالجة الأخطاء
// =========================

// معالج الأخطاء العام المحسن
function handleAPIError(error, context = 'العملية') {
    let errorMessage = `حدث خطأ في ${context}`;
    
    // تحديث حالة الاتصال
    updateConnectionStatus(false, error.message);
    
    if (error.status === 401) {
        // إعادة توجيه لصفحة تسجيل الدخول
        showNotification('انتهت جلسة العمل، يرجى تسجيل الدخول مرة أخرى', 'error');
        setTimeout(() => {
            window.location.href = '/login.html';
        }, 2000);
        return;
    }
    
    if (error.message) {
        if (error.message.includes('Network')) {
            errorMessage = 'خطأ في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.';
        } else if (error.message.includes('timeout') || error.message.includes('انتهت مهلة')) {
            errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
        } else if (error.message.includes('Unauthorized')) {
            errorMessage = 'غير مخول للوصول. يرجى تسجيل الدخول مرة أخرى.';
        } else if (error.message.includes('Forbidden')) {
            errorMessage = 'ليس لديك صلاحية لتنفيذ هذه العملية.';
        } else if (error.message.includes('Not Found')) {
            errorMessage = 'البيانات المطلوبة غير موجودة.';
        } else {
            errorMessage = error.message;
        }
    }
    
    // رسائل خطأ مخصصة حسب رمز الحالة
    switch (error.status) {
        case 403:
            errorMessage = 'ليس لديك صلاحية للوصول لهذه البيانات';
            break;
        case 404:
            errorMessage = 'البيانات المطلوبة غير موجودة';
            break;
        case 422:
            errorMessage = 'البيانات المدخلة غير صحيحة';
            break;
        case 429:
            errorMessage = 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً';
            break;
        case 500:
            errorMessage = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
            break;
    }
    
    console.error(`API Error in ${context}:`, error);
    showNotification(errorMessage, 'error');
    
    return {
        success: false,
        error: errorMessage,
        status: error.status,
        context
    };
}

// إعادة المحاولة التلقائية
async function retryAPICall(apiFunction, maxRetries = 3, delay = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await apiFunction();
        } catch (error) {
            lastError = error;
            console.warn(`محاولة ${attempt} فشلت:`, error.message);
            
            if (attempt < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, delay * attempt));
            }
        }
    }
    
    throw lastError;
}

// تحديث حالة الاتصال
function updateConnectionStatus(isConnected, errorMessage = null) {
    connectionStatus.isOnline = isConnected;
    connectionStatus.lastCheck = Date.now();
    
    if (!isConnected && errorMessage) {
        connectionStatus.lastError = errorMessage;
    }
    
    // إرسال حدث لتحديث واجهة المستخدم
    window.dispatchEvent(new CustomEvent('connectionStatusChanged', {
        detail: { ...connectionStatus }
    }));
}

// فحص حالة الاتصال المحسن
async function checkConnection() {
    try {
        await apiClient.get('/profiles', { limit: 1 });
        updateConnectionStatus(true);
        return true;
    } catch (error) {
        updateConnectionStatus(false, error.message);
        return false;
    }
}

// مراقب حالة الاتصال المحسن
function setupConnectionMonitor() {
    // فحص فوري
    checkConnection();
    
    // فحص دوري كل 30 ثانية
    setInterval(async () => {
        await checkConnection();
    }, 30000);
    
    // فحص عند استعادة التركيز على النافذة
    window.addEventListener('focus', () => {
        checkConnection();
    });
    
    window.addEventListener('online', () => {
        showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        setTimeout(() => checkConnection(), 1000);
        // يمكن إضافة منطق لإعادة تحميل البيانات
    });
    
    window.addEventListener('offline', () => {
        showNotification('تم فقدان الاتصال بالإنترنت', 'warning');
        updateConnectionStatus(false, 'لا يوجد اتصال بالإنترنت');
    });
}

// تهيئة مراقب الاتصال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', setupConnectionMonitor);

// =========================
// دوال محاكاة البيانات (للتطوير والاختبار)
// =========================

// في حالة عدم وجود خادم API، يمكن استخدام هذه الدوال للمحاكاة
const MOCK_MODE = false; // تم تعطيل وضع المحاكاة - متصل بقاعدة البيانات الحقيقية

// وسائل مساعدة للمحاكاة
function mockDelay(ms = 1000) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function mockSuccess(data) {
    return Promise.resolve({ success: true, data });
}

function mockError(message) {
    return Promise.reject(new Error(message));
}

if (MOCK_MODE) {
    console.log('تشغيل وضع المحاكاة للبيانات');
} else {
    console.log('متصل بقاعدة البيانات الحقيقية - Supabase');
}

// تصدير الدوال للاستخدام في ملفات أخرى
window.apiClient = apiClient;
window.API_CONFIG = API_CONFIG;
window.handleAPIError = handleAPIError;
window.retryAPICall = retryAPICall;
window.fetchCars = fetchCars;
window.fetchBookings = fetchBookings;
window.fetchUsers = fetchUsers;
window.fetchPayments = fetchPayments;
window.fetchDashboardStats = fetchDashboardStats;
window.checkConnection = checkConnection;
window.connectionStatus = connectionStatus;
window.updateConnectionStatus = updateConnectionStatus;
