import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/car_model.dart';
import '../models/booking_model.dart';
import '../data/dummy_data.dart';

class CarService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get all cars with pagination and filters
  Future<List<CarModel>> getCars({
    int page = 1,
    int limit = 20,
    String? category,
    double? minPrice,
    double? maxPrice,
    bool? availableOnly,
  }) async {
    try {
      print('🔍 جاري جلب السيارات من قاعدة البيانات...');

      // التأكد من وجود اتصال بقاعدة البيانات أولاً
      final hasConnection = await checkDatabaseConnection();
      if (!hasConnection) {
        print('❌ لا يوجد اتصال بقاعدة البيانات');
        throw Exception('فقدان الاتصال بقاعدة البيانات');
      }

      var queryBuilder = _supabase.from('cars').select();

      if (category != null && category.isNotEmpty) {
        queryBuilder = queryBuilder.eq('category', category);
      }

      if (minPrice != null) {
        queryBuilder = queryBuilder.gte('daily_price', minPrice);
      }

      if (maxPrice != null) {
        queryBuilder = queryBuilder.lte('daily_price', maxPrice);
      }

      if (availableOnly == true) {
        queryBuilder = queryBuilder.eq('availability_status', 'available');
      }

      final response = await queryBuilder
          .range((page - 1) * limit, page * limit - 1)
          .timeout(const Duration(seconds: 10));

      print('📡 تم جلب ${response.length} سيارة من قاعدة البيانات');

      if (response.isEmpty) {
        print('⚠️ لا توجد سيارات تطابق المعايير المحددة');
        return [];
      }

      final cars =
          (response as List).map((car) => CarModel.fromJson(car)).toList();
      print('✅ تم تحليل ${cars.length} سيارة بنجاح');
      return cars;
    } catch (e) {
      print('❌ خطأ في جلب السيارات: $e');
      // لا نستخدم بيانات وهمية، نرجع قائمة فارغة
      return [];
    }
  }

  // Get user bookings
  Future<List<BookingModel>> getBookings({
    required String userId,
    String? status,
  }) async {
    try {
      var query = _supabase.from('bookings').select().eq('user_id', userId);

      if (status != null && status.isNotEmpty) {
        query = query.eq('status', status);
      }

      final response = await query.order('created_at', ascending: false);
      return (response as List)
          .map((booking) => BookingModel.fromJson(booking))
          .toList();
    } catch (e) {
      print('Error fetching bookings: $e');
      // Return dummy bookings as fallback
      return DummyData.getDummyBookings();
    }
  }

  // Get featured cars
  Future<List<CarModel>> getFeaturedCars({int limit = 6}) async {
    try {
      print('🔍 جاري جلب السيارات المميزة من قاعدة البيانات...');

      // التأكد من وجود اتصال بقاعدة البيانات أولاً
      final hasConnection = await checkDatabaseConnection();
      if (!hasConnection) {
        print('❌ لا يوجد اتصال بقاعدة البيانات');
        throw Exception('فقدان الاتصال بقاعدة البيانات');
      }

      final response = await _supabase
          .from('cars')
          .select()
          .eq('is_featured', true)
          .eq('availability_status', 'available')
          .limit(limit)
          .timeout(const Duration(seconds: 10));

      print('📡 استجابة قاعدة البيانات: ${response.length} سجلات');

      if (response.isEmpty) {
        print(
            '⚠️ لا توجد سيارات مميزة في قاعدة البيانات - محاولة جلب السيارات المتاحة');
        // إذا لم توجد سيارات مميزة، جلب السيارات المتاحة
        final fallbackResponse = await _supabase
            .from('cars')
            .select()
            .eq('availability_status', 'available')
            .limit(limit)
            .timeout(const Duration(seconds: 10));

        if (fallbackResponse.isEmpty) {
          print('❌ لا توجد سيارات في قاعدة البيانات على الإطلاق');
          return [];
        }

        final cars = (fallbackResponse as List)
            .map((car) => CarModel.fromJson(car))
            .toList();
        print('✅ تم جلب ${cars.length} سيارة متاحة كبديل');
        return cars;
      }

      final cars =
          (response as List).map((car) => CarModel.fromJson(car)).toList();
      print('✅ تم جلب ${cars.length} سيارة مميزة بنجاح');
      return cars;
    } catch (e) {
      print('❌ خطأ في جلب السيارات المميزة: $e');
      // لا نستخدم بيانات وهمية، نرجع قائمة فارغة
      return [];
    }
  }

  // Get popular categories with car counts
  Future<Map<String, int>> getPopularCategories() async {
    try {
      print('🔍 جاري جلب الفئات الشائعة من قاعدة البيانات...');

      // التأكد من وجود اتصال بقاعدة البيانات أولاً
      final hasConnection = await checkDatabaseConnection();
      if (!hasConnection) {
        print('❌ لا يوجد اتصال بقاعدة البيانات');
        throw Exception('فقدان الاتصال بقاعدة البيانات');
      }

      final response = await _supabase
          .from('cars')
          .select('category')
          .eq('availability_status', 'available')
          .timeout(const Duration(seconds: 10));

      print('📡 استجابة قاعدة البيانات للفئات: ${response.length} سجلات');

      if (response.isEmpty) {
        print('⚠️ لا توجد فئات في قاعدة البيانات');
        return {};
      }

      Map<String, int> categories = {};
      for (var car in response as List) {
        String category = car['category'] ?? 'غير محدد';
        categories[category] = (categories[category] ?? 0) + 1;
      }

      print('✅ تم جلب ${categories.length} فئة بنجاح: $categories');
      return categories;
    } catch (e) {
      print('❌ خطأ في جلب الفئات: $e');
      // لا نستخدم فئات افتراضية، نرجع خريطة فارغة
      return {};
    }
  }

  // Get all unique categories
  Future<List<String>> getAllCategories() async {
    try {
      print('🔍 جاري جلب جميع الفئات...');

      final response = await _supabase
          .from('cars')
          .select('category')
          .eq('availability_status', 'available');

      if (response.isEmpty) {
        print('⚠️ لا توجد سيارات متاحة في قاعدة البيانات');
        return ['luxury', 'economy', 'suv', 'sports', 'sedan', 'convertible'];
      }

      Set<String> uniqueCategories = {};
      for (var car in response as List) {
        String category = car['category']?.toString().toLowerCase() ?? '';
        if (category.isNotEmpty) {
          uniqueCategories.add(category);
        }
      }

      List<String> categories = uniqueCategories.toList();
      print('✅ تم جلب ${categories.length} فئة: $categories');

      // إضافة فئات افتراضية إذا كانت القائمة فارغة
      if (categories.isEmpty) {
        categories = [
          'luxury',
          'economy',
          'suv',
          'sports',
          'sedan',
          'convertible'
        ];
      }

      return categories;
    } catch (e) {
      print('❌ خطأ في جلب الفئات: $e');
      return ['luxury', 'economy', 'suv', 'sports', 'sedan', 'convertible'];
    }
  }

  // Check database connectivity and data availability
  Future<bool> checkDatabaseConnection() async {
    try {
      print('🔍 فحص اتصال قاعدة البيانات...');

      final response = await _supabase.from('cars').select('id').limit(1);

      final count = response.length;
      print('✅ تم الاتصال بنجاح. عدد السيارات في القاعدة: $count');

      return count > 0;
    } catch (e) {
      print('❌ خطأ في الاتصال بقاعدة البيانات: $e');
      return false;
    }
  }

  // Simple test for Supabase connection
  Future<bool> testSupabaseConnection() async {
    try {
      print('🧪 اختبار اتصال Supabase...');

      // Try a simple query to test connection
      final response = await _supabase
          .from('cars')
          .select('id')
          .limit(1)
          .timeout(Duration(seconds: 5));

      print('✅ تم الاتصال بـ Supabase بنجاح');
      print('📊 استجابة الاختبار: ${response.length} سجلات');
      return true;
    } catch (e) {
      print('❌ فشل الاتصال بـ Supabase: $e');
      return false;
    }
  }

  // Add sample data to database (for testing purposes)

  Future<bool> addSampleDataIfEmpty() async {
    try {
      // Check if database has data
      final hasData = await checkDatabaseConnection();

      if (hasData) {
        print('✅ قاعدة البيانات تحتوي على بيانات بالفعل');
        return true;
      }

      print('🔄 إضافة بيانات تجريبية لقاعدة البيانات...');

      // Sample car data
      final sampleCars = [
        {
          'make': 'Toyota',
          'model': 'Camry',
          'year': 2023,
          'category': 'sedan',
          'transmission': 'automatic',
          'fuel_type': 'petrol',
          'seating_capacity': 5,
          'daily_rate': 150.0,
          'description_en': 'Comfortable sedan perfect for city driving',
          'description_ar': 'سيارة سيدان مريحة مثالية للقيادة في المدينة',
          'availability_status': 'available',
          'is_featured': true,
          'image_urls': ['https://example.com/car1.jpg'],
          'location': {
            'lat': 25.276987,
            'lng': 55.296249,
            'address': 'Dubai Marina'
          },
          'features': ['GPS', 'Air Conditioning', 'Bluetooth']
        },
        {
          'make': 'BMW',
          'model': 'X5',
          'year': 2023,
          'category': 'suv',
          'transmission': 'automatic',
          'fuel_type': 'petrol',
          'seating_capacity': 7,
          'daily_rate': 350.0,
          'description_en': 'Luxury SUV with premium features',
          'description_ar': 'سيارة دفع رباعي فاخرة مع مزايا متميزة',
          'availability_status': 'available',
          'is_featured': true,
          'image_urls': ['https://example.com/car2.jpg'],
          'location': {
            'lat': 25.276987,
            'lng': 55.296249,
            'address': 'Dubai Mall'
          },
          'features': ['GPS', 'Leather Seats', 'Sunroof', 'Premium Sound']
        },
        {
          'make': 'Mercedes',
          'model': 'C-Class',
          'year': 2023,
          'category': 'luxury',
          'transmission': 'automatic',
          'fuel_type': 'petrol',
          'seating_capacity': 5,
          'daily_rate': 280.0,
          'description_en': 'Premium luxury sedan',
          'description_ar': 'سيارة سيدان فاخرة متميزة',
          'availability_status': 'available',
          'is_featured': true,
          'image_urls': ['https://example.com/car3.jpg'],
          'location': {
            'lat': 25.276987,
            'lng': 55.296249,
            'address': 'Business Bay'
          },
          'features': [
            'GPS',
            'Leather Seats',
            'Premium Sound',
            'Climate Control'
          ]
        }
      ];

      await _supabase.from('cars').insert(sampleCars);
      print('✅ تم إضافة ${sampleCars.length} سيارة تجريبية بنجاح');

      return true;
    } catch (e) {
      print('❌ خطأ في إضافة البيانات التجريبية: $e');
      return false;
    }
  }

  // Get car details by ID
  Future<CarModel?> getCarDetails(String carId) async {
    try {
      final response =
          await _supabase.from('cars').select().eq('id', carId).single();

      return CarModel.fromJson(response);
    } catch (e) {
      print('Error fetching car details: $e');
      // Try to find in dummy data
      try {
        return DummyData.getDummyCars().firstWhere((car) => car.id == carId);
      } catch (e) {
        return null;
      }
    }
  }

  // Search cars by query
  Future<List<CarModel>> searchCars({
    required String query,
    int limit = 20,
  }) async {
    try {
      final response = await _supabase
          .from('cars')
          .select()
          .or('make.ilike.%$query%,model.ilike.%$query%,description_en.ilike.%$query%,description_ar.ilike.%$query%')
          .eq('availability_status', 'available')
          .limit(limit);

      return (response as List).map((car) => CarModel.fromJson(car)).toList();
    } catch (e) {
      print('Error searching cars: $e');
      // Search in dummy data
      final dummyCars = DummyData.getDummyCars();
      return dummyCars
          .where((car) =>
              car.make.toLowerCase().contains(query.toLowerCase()) ||
              car.model.toLowerCase().contains(query.toLowerCase()) ||
              (car.description?.toLowerCase().contains(query.toLowerCase()) ??
                  false))
          .toList();
    }
  }

  // Create a new booking
  Future<BookingModel?> createBooking({
    required String userId,
    required String carId,
    required DateTime startDate,
    required DateTime endDate,
    required double totalAmount,
    required Map<String, dynamic> pickupLocation,
    required Map<String, dynamic> returnLocation,
    double? dailyRate,
  }) async {
    try {
      // الحصول على السعر اليومي من السيارة إذا لم يتم تمريره
      double finalDailyRate = dailyRate ?? 0.0;
      if (finalDailyRate == 0.0) {
        final car = await getCarDetails(carId);
        if (car != null) {
          finalDailyRate = car.dailyPrice;
        }
      }

      // حساب عدد الأيام بشكل صحيح (مع ضمان أن يكون على الأقل يوم واحد)
      final durationInDays = endDate.difference(startDate).inDays;
      final rentalDays = durationInDays <= 0 ? 1 : durationInDays;

      print('📅 تواريخ الحجز:');
      print('   من: ${startDate.toLocal()}');
      print('   إلى: ${endDate.toLocal()}');
      print('   الفرق: $durationInDays أيام');
      print('   أيام الإيجار: $rentalDays أيام');

      final bookingData = {
        'booking_number': 'DCR-${DateTime.now().millisecondsSinceEpoch}',
        'user_id': userId,
        'car_id': carId,
        'pickup_date': startDate.toIso8601String(),
        'return_date': endDate.toIso8601String(),
        'pickup_date_time': startDate.toIso8601String(),
        'return_date_time': endDate.toIso8601String(),
        'pickup_location': pickupLocation,
        'return_location': returnLocation,
        'pickup_location_lat': pickupLocation['latitude'] ?? 0.0,
        'pickup_location_lng': pickupLocation['longitude'] ?? 0.0,
        'pickup_address': pickupLocation['address'] ?? '',
        'pickup_type': 'pickup',
        'return_location_lat': returnLocation['latitude'],
        'return_location_lng': returnLocation['longitude'],
        'return_address': returnLocation['address'],
        'return_type': 'return',
        'rental_duration_days': rentalDays,
        'daily_rate': finalDailyRate,
        'subtotal': totalAmount * 0.95, // المبلغ قبل الضريبة (95%)
        'tax_amount': totalAmount * 0.05, // ضريبة 5%
        'total_amount': totalAmount,
        'status': 'pending',
        'payment_status': 'pending',
        'terms_accepted': true,
        'terms_accepted_at': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('bookings')
          .insert(bookingData)
          .select()
          .single();

      return BookingModel.fromJson(response);
    } catch (e) {
      print('Error creating booking: $e');
      return null;
    }
  }

  // Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    try {
      await _supabase
          .from('bookings')
          .update({'status': status}).eq('id', bookingId);
      return true;
    } catch (e) {
      print('Error updating booking status: $e');
      return false;
    }
  }

  // Cancel booking
  Future<bool> cancelBooking(String bookingId) async {
    return await updateBookingStatus(bookingId, 'cancelled');
  }

  // Get booking statistics for a user
  Future<Map<String, dynamic>> getBookingStats(String userId) async {
    try {
      final bookings = await getBookings(userId: userId);

      return {
        'total': bookings.length,
        'active':
            bookings.where((b) => b.status == BookingStatus.active).length,
        'completed':
            bookings.where((b) => b.status == BookingStatus.completed).length,
        'cancelled':
            bookings.where((b) => b.status == BookingStatus.cancelled).length,
        'total_spent': bookings
            .where((b) => b.status == BookingStatus.completed)
            .fold(0.0, (sum, b) => sum + b.totalAmount),
      };
    } catch (e) {
      print('Error fetching booking stats: $e');
      return {
        'total': 0,
        'active': 0,
        'completed': 0,
        'cancelled': 0,
        'total_spent': 0.0,
      };
    }
  }

  // Get cars near a specific location
  Future<List<CarModel>> getCarsNearLocation({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
    int limit = 20,
  }) async {
    try {
      // For now, return all available cars since we don't have PostGIS distance calculations
      final response = await _supabase
          .from('cars')
          .select()
          .eq('availability_status', 'available')
          .limit(limit);

      return (response as List).map((car) => CarModel.fromJson(car)).toList();
    } catch (e) {
      print('Error fetching cars near location: $e');
      return DummyData.getDummyCars();
    }
  }

  // Update car availability
  Future<bool> updateCarAvailability(String carId, String status) async {
    try {
      await _supabase
          .from('cars')
          .update({'availability_status': status}).eq('id', carId);
      return true;
    } catch (e) {
      print('Error updating car availability: $e');
      return false;
    }
  }

  // Get car rental history
  Future<List<BookingModel>> getCarRentalHistory(String carId) async {
    try {
      final response = await _supabase
          .from('bookings')
          .select()
          .eq('car_id', carId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((booking) => BookingModel.fromJson(booking))
          .toList();
    } catch (e) {
      print('Error fetching car rental history: $e');
      return [];
    }
  }
}
