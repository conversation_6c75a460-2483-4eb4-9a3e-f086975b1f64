import 'package:equatable/equatable.dart';
import 'car_model.dart';
import 'user_model.dart';

class BookingModel extends Equatable {
  final String id;
  final String bookingNumber;
  final String userId;
  final String carId;
  final UserModel? user;
  final CarModel? car;

  // تواريخ الحجز (مطابقة لقاعدة البيانات)
  final DateTime pickupDate;
  final DateTime returnDate;
  final DateTime pickupDateTime; // pickup_date_time في قاعدة البيانات
  final DateTime returnDateTime; // return_date_time في قاعدة البيانات
  final DateTime? actualPickupDate;
  final DateTime? actualReturnDate;

  // مواقع الاستلام والتسليم
  final Map<String, dynamic> pickupLocation; // JSONB في قاعدة البيانات
  final Map<String, dynamic> returnLocation; // JSONB في قاعدة البيانات
  final double pickupLocationLat;
  final double pickupLocationLng;
  final String pickupAddress;
  final String pickupType;
  final double? returnLocationLat;
  final double? returnLocationLng;
  final String? returnAddress;
  final String returnType;

  // التكاليف والتسعير
  final int? rentalDurationHours;
  final int rentalDurationDays;
  final double hourlyRate;
  final double dailyRate;
  final double subtotal;
  final double taxAmount;
  final double securityDeposit;
  final double deliveryFee;
  final double additionalFees;
  final double discountAmount;
  final double totalAmount;
  final Map<String, dynamic>? pricing; // JSONB في قاعدة البيانات

  // الحالة
  final BookingStatus status;
  final String paymentStatus;

  // معلومات الدفع
  final Map<String, dynamic>? payment; // JSONB في قاعدة البيانات
  final String? paymentMethod;
  final String? paymentIntentId;

  // التوقيعات والوثائق
  final String? pickupSignatureUrl;
  final String? returnSignatureUrl;
  final String? signatureUrl;
  final String? contractUrl;
  final List<String> pickupInspectionPhotos;
  final List<String> returnInspectionPhotos;
  final List<String> documentUrls;

  // الشروط والأحكام
  final bool termsAccepted;
  final DateTime? termsAcceptedAt;

  // الطلبات والملاحظات
  final String? specialRequests;
  final String? pickupNotes;
  final String? returnNotes;
  final String? notes;

  // الإلغاء
  final Map<String, dynamic>? cancellation; // JSONB في قاعدة البيانات
  final String? cancellationReason;
  final DateTime? cancelledAt;
  final String? cancelledBy;

  // معلومات إضافية
  final int? driverAge;
  final List<String> additionalDrivers;
  final String? emergencyContactName;
  final String? emergencyContactPhone;

  // سجل الحالات
  final List<Map<String, dynamic>> statusHistory; // JSONB في قاعدة البيانات

  final DateTime createdAt;
  final DateTime updatedAt;

  const BookingModel({
    required this.id,
    required this.bookingNumber,
    required this.userId,
    required this.carId,
    this.user,
    this.car,
    required this.pickupDate,
    required this.returnDate,
    required this.pickupDateTime,
    required this.returnDateTime,
    this.actualPickupDate,
    this.actualReturnDate,
    required this.pickupLocation,
    required this.returnLocation,
    required this.pickupLocationLat,
    required this.pickupLocationLng,
    required this.pickupAddress,
    required this.pickupType,
    this.returnLocationLat,
    this.returnLocationLng,
    this.returnAddress,
    required this.returnType,
    this.rentalDurationHours,
    required this.rentalDurationDays,
    required this.hourlyRate,
    required this.dailyRate,
    required this.subtotal,
    required this.taxAmount,
    required this.securityDeposit,
    required this.deliveryFee,
    required this.additionalFees,
    required this.discountAmount,
    required this.totalAmount,
    this.pricing,
    required this.status,
    required this.paymentStatus,
    this.payment,
    this.paymentMethod,
    this.paymentIntentId,
    this.pickupSignatureUrl,
    this.returnSignatureUrl,
    this.signatureUrl,
    this.contractUrl,
    required this.pickupInspectionPhotos,
    required this.returnInspectionPhotos,
    required this.documentUrls,
    required this.termsAccepted,
    this.termsAcceptedAt,
    this.specialRequests,
    this.pickupNotes,
    this.returnNotes,
    this.notes,
    this.cancellation,
    this.cancellationReason,
    this.cancelledAt,
    this.cancelledBy,
    this.driverAge,
    required this.additionalDrivers,
    this.emergencyContactName,
    this.emergencyContactPhone,
    required this.statusHistory,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'] as String,
      bookingNumber: json['booking_number'] as String,
      userId: json['user_id'] as String,
      carId: json['car_id'] as String,
      user: json['user'] != null
          ? UserModel.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      car: json['car'] != null
          ? CarModel.fromJson(json['car'] as Map<String, dynamic>)
          : null,
      pickupDate: DateTime.parse(json['pickup_date'] as String),
      returnDate: DateTime.parse(json['return_date'] as String),
      pickupDateTime: DateTime.parse(json['pickup_date_time'] as String),
      returnDateTime: DateTime.parse(json['return_date_time'] as String),
      actualPickupDate: json['actual_pickup_date'] != null
          ? DateTime.parse(json['actual_pickup_date'] as String)
          : null,
      actualReturnDate: json['actual_return_date'] != null
          ? DateTime.parse(json['actual_return_date'] as String)
          : null,
      pickupLocation:
          Map<String, dynamic>.from(json['pickup_location'] as Map? ?? {}),
      returnLocation:
          Map<String, dynamic>.from(json['return_location'] as Map? ?? {}),
      pickupLocationLat: (json['pickup_location_lat'] as num).toDouble(),
      pickupLocationLng: (json['pickup_location_lng'] as num).toDouble(),
      pickupAddress: json['pickup_address'] as String,
      pickupType: json['pickup_type'] as String? ?? 'pickup',
      returnLocationLat: json['return_location_lat'] != null
          ? (json['return_location_lat'] as num).toDouble()
          : null,
      returnLocationLng: json['return_location_lng'] != null
          ? (json['return_location_lng'] as num).toDouble()
          : null,
      returnAddress: json['return_address'] as String?,
      returnType: json['return_type'] as String? ?? 'return',
      rentalDurationHours: json['rental_duration_hours'] as int?,
      rentalDurationDays: json['rental_duration_days'] as int,
      hourlyRate: (json['hourly_rate'] as num).toDouble(),
      dailyRate: (json['daily_rate'] as num).toDouble(),
      subtotal: (json['subtotal'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num? ?? 0).toDouble(),
      securityDeposit: (json['security_deposit'] as num? ?? 1000).toDouble(),
      deliveryFee: (json['delivery_fee'] as num? ?? 0).toDouble(),
      additionalFees: (json['additional_fees'] as num? ?? 0).toDouble(),
      discountAmount: (json['discount_amount'] as num? ?? 0).toDouble(),
      totalAmount: (json['total_amount'] as num).toDouble(),
      pricing: json['pricing'] != null
          ? Map<String, dynamic>.from(json['pricing'] as Map)
          : null,
      status: BookingStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String),
        orElse: () => BookingStatus.pending,
      ),
      paymentStatus: json['payment_status'] as String? ?? 'pending',
      payment: json['payment'] != null
          ? Map<String, dynamic>.from(json['payment'] as Map)
          : null,
      paymentMethod: json['payment_method'] as String?,
      paymentIntentId: json['payment_intent_id'] as String?,
      pickupSignatureUrl: json['pickup_signature_url'] as String?,
      returnSignatureUrl: json['return_signature_url'] as String?,
      signatureUrl: json['signature_url'] as String?,
      contractUrl: json['contract_url'] as String?,
      pickupInspectionPhotos:
          List<String>.from(json['pickup_inspection_photos'] as List? ?? []),
      returnInspectionPhotos:
          List<String>.from(json['return_inspection_photos'] as List? ?? []),
      documentUrls: List<String>.from(json['document_urls'] as List? ?? []),
      termsAccepted: json['terms_accepted'] as bool? ?? false,
      termsAcceptedAt: json['terms_accepted_at'] != null
          ? DateTime.parse(json['terms_accepted_at'] as String)
          : null,
      specialRequests: json['special_requests'] as String?,
      pickupNotes: json['pickup_notes'] as String?,
      returnNotes: json['return_notes'] as String?,
      notes: json['notes'] as String?,
      cancellation: json['cancellation'] != null
          ? Map<String, dynamic>.from(json['cancellation'] as Map)
          : null,
      cancellationReason: json['cancellation_reason'] as String?,
      cancelledAt: json['cancelled_at'] != null
          ? DateTime.parse(json['cancelled_at'] as String)
          : null,
      cancelledBy: json['cancelled_by'] as String?,
      driverAge: json['driver_age'] as int?,
      additionalDrivers:
          List<String>.from(json['additional_drivers'] as List? ?? []),
      emergencyContactName: json['emergency_contact_name'] as String?,
      emergencyContactPhone: json['emergency_contact_phone'] as String?,
      statusHistory: List<Map<String, dynamic>>.from(
          json['status_history'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'booking_number': bookingNumber,
      'user_id': userId,
      'car_id': carId,
      'pickup_date': pickupDate.toIso8601String(),
      'return_date': returnDate.toIso8601String(),
      'pickup_date_time': pickupDateTime.toIso8601String(),
      'return_date_time': returnDateTime.toIso8601String(),
      'actual_pickup_date': actualPickupDate?.toIso8601String(),
      'actual_return_date': actualReturnDate?.toIso8601String(),
      'pickup_location': pickupLocation,
      'return_location': returnLocation,
      'pickup_location_lat': pickupLocationLat,
      'pickup_location_lng': pickupLocationLng,
      'pickup_address': pickupAddress,
      'pickup_type': pickupType,
      'return_location_lat': returnLocationLat,
      'return_location_lng': returnLocationLng,
      'return_address': returnAddress,
      'return_type': returnType,
      'rental_duration_hours': rentalDurationHours,
      'rental_duration_days': rentalDurationDays,
      'hourly_rate': hourlyRate,
      'daily_rate': dailyRate,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'security_deposit': securityDeposit,
      'delivery_fee': deliveryFee,
      'additional_fees': additionalFees,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'pricing': pricing,
      'status': status.name,
      'payment_status': paymentStatus,
      'payment': payment,
      'payment_method': paymentMethod,
      'payment_intent_id': paymentIntentId,
      'pickup_signature_url': pickupSignatureUrl,
      'return_signature_url': returnSignatureUrl,
      'signature_url': signatureUrl,
      'contract_url': contractUrl,
      'pickup_inspection_photos': pickupInspectionPhotos,
      'return_inspection_photos': returnInspectionPhotos,
      'document_urls': documentUrls,
      'terms_accepted': termsAccepted,
      'terms_accepted_at': termsAcceptedAt?.toIso8601String(),
      'special_requests': specialRequests,
      'pickup_notes': pickupNotes,
      'return_notes': returnNotes,
      'notes': notes,
      'cancellation': cancellation,
      'cancellation_reason': cancellationReason,
      'cancelled_at': cancelledAt?.toIso8601String(),
      'cancelled_by': cancelledBy,
      'driver_age': driverAge,
      'additional_drivers': additionalDrivers,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'status_history': statusHistory,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        bookingNumber,
        userId,
        carId,
        user,
        car,
        pickupDate,
        returnDate,
        pickupDateTime,
        returnDateTime,
        actualPickupDate,
        actualReturnDate,
        pickupLocation,
        returnLocation,
        pickupLocationLat,
        pickupLocationLng,
        pickupAddress,
        pickupType,
        returnLocationLat,
        returnLocationLng,
        returnAddress,
        returnType,
        rentalDurationHours,
        rentalDurationDays,
        hourlyRate,
        dailyRate,
        subtotal,
        taxAmount,
        securityDeposit,
        deliveryFee,
        additionalFees,
        discountAmount,
        totalAmount,
        pricing,
        status,
        paymentStatus,
        payment,
        paymentMethod,
        paymentIntentId,
        pickupSignatureUrl,
        returnSignatureUrl,
        signatureUrl,
        contractUrl,
        pickupInspectionPhotos,
        returnInspectionPhotos,
        documentUrls,
        termsAccepted,
        termsAcceptedAt,
        specialRequests,
        pickupNotes,
        returnNotes,
        notes,
        cancellation,
        cancellationReason,
        cancelledAt,
        cancelledBy,
        driverAge,
        additionalDrivers,
        emergencyContactName,
        emergencyContactPhone,
        statusHistory,
        createdAt,
        updatedAt,
      ];
}

enum BookingStatus {
  pending, // في الانتظار
  confirmed, // مؤكد
  carAssigned, // تم تعيين السيارة
  pickedUp, // تم الاستلام
  active, // نشط
  completed, // مكتمل
  cancelled, // ملغي
}

// إبقاء الكلاسات الأخرى للتوافق مع النموذج الحالي
class LocationInfo {
  final String address;
  final double? latitude;
  final double? longitude;
  final String? details;

  const LocationInfo({
    required this.address,
    this.latitude,
    this.longitude,
    this.details,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      address: json['address'] as String? ?? '',
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      details: json['details'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'details': details,
    };
  }
}

class PricingDetails {
  final double baseRate;
  final int days;
  final double subtotal;
  final double tax;
  final double total;
  final double deposit;

  const PricingDetails({
    required this.baseRate,
    required this.days,
    required this.subtotal,
    required this.tax,
    required this.total,
    required this.deposit,
  });

  factory PricingDetails.fromJson(Map<String, dynamic> json) {
    return PricingDetails(
      baseRate: (json['baseRate'] as num? ?? 0).toDouble(),
      days: json['days'] as int? ?? 1,
      subtotal: (json['subtotal'] as num? ?? 0).toDouble(),
      tax: (json['tax'] as num? ?? 0).toDouble(),
      total: (json['total'] as num? ?? 0).toDouble(),
      deposit: (json['deposit'] as num? ?? 1000).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'baseRate': baseRate,
      'days': days,
      'subtotal': subtotal,
      'tax': tax,
      'total': total,
      'deposit': deposit,
    };
  }
}

class BookingStatusHistory {
  final String id;
  final BookingStatus fromStatus;
  final BookingStatus toStatus;
  final DateTime changedAt;
  final String? reason;
  final String? changedBy;

  const BookingStatusHistory({
    required this.id,
    required this.fromStatus,
    required this.toStatus,
    required this.changedAt,
    this.reason,
    this.changedBy,
  });

  factory BookingStatusHistory.fromJson(Map<String, dynamic> json) {
    return BookingStatusHistory(
      id: json['id'] as String,
      fromStatus: BookingStatus.values.firstWhere(
        (e) => e.name == (json['fromStatus'] as String),
        orElse: () => BookingStatus.pending,
      ),
      toStatus: BookingStatus.values.firstWhere(
        (e) => e.name == (json['toStatus'] as String),
        orElse: () => BookingStatus.pending,
      ),
      changedAt: DateTime.parse(json['changedAt'] as String),
      reason: json['reason'] as String?,
      changedBy: json['changedBy'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromStatus': fromStatus.name,
      'toStatus': toStatus.name,
      'changedAt': changedAt.toIso8601String(),
      'reason': reason,
      'changedBy': changedBy,
    };
  }
}
