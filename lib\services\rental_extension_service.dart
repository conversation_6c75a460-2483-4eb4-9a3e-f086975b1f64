import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/rental_extension_model.dart';

class RentalExtensionService {
  static const String _baseUrl = 'https://api.dubaicarrental.com/v1';

  // طلب تمديد الإيجار
  Future<RentalExtension> requestExtension({
    required String bookingId,
    required int extensionDays,
    required String paymentMethodId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/rentals/extend'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'booking_id': bookingId,
          'extension_days': extensionDays,
          'payment_method_id': paymentMethodId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          return RentalExtension.fromJson(data['data']);
        } else {
          throw Exception(data['message'] ?? 'حدث خطأ أثناء طلب التمديد');
        }
      } else {
        throw Exception('فشل في الاتصال بالخادم');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: ${e.toString()}');
    }
  }

  // الحصول على تمديدات المستخدم
  Future<List<RentalExtension>> getUserExtensions({
    required String userId,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/users/$userId/extensions'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          final List<dynamic> extensionsJson = data['data'];
          return extensionsJson
              .map((json) => RentalExtension.fromJson(json))
              .toList();
        } else {
          throw Exception(data['message'] ?? 'حدث خطأ أثناء جلب التمديدات');
        }
      } else {
        throw Exception('فشل في الاتصال بالخادم');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: ${e.toString()}');
    }
  }

  // حساب تكلفة التمديد
  Future<Map<String, double>> calculateExtensionCost({
    required String bookingId,
    required int extensionDays,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/rentals/calculate-extension'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'booking_id': bookingId,
          'extension_days': extensionDays,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          final costData = data['data'];
          return {
            'daily_rate': (costData['daily_rate'] ?? 0).toDouble(),
            'subtotal': (costData['subtotal'] ?? 0).toDouble(),
            'vat_rate': (costData['vat_rate'] ?? 0).toDouble(),
            'vat_amount': (costData['vat_amount'] ?? 0).toDouble(),
            'total_amount': (costData['total_amount'] ?? 0).toDouble(),
          };
        } else {
          throw Exception(data['message'] ?? 'حدث خطأ أثناء حساب التكلفة');
        }
      } else {
        throw Exception('فشل في الاتصال بالخادم');
      }
    } catch (e) {
      // في حالة عدم وجود اتصال بالإنترنت، نعيد قيماً افتراضية
      return {
        'daily_rate': 176.63,
        'subtotal': 176.63 * extensionDays,
        'vat_rate': 5.0,
        'vat_amount': (176.63 * extensionDays) * 0.05,
        'total_amount': (176.63 * extensionDays) * 1.05,
      };
    }
  }

  // إلغاء تمديد الإيجار
  Future<bool> cancelExtension({
    required String extensionId,
  }) async {
    try {
      final response = await http.delete(
        Uri.parse('$_baseUrl/extensions/$extensionId'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // تحديث حالة التمديد
  Future<RentalExtension?> updateExtensionStatus({
    required String extensionId,
    required ExtensionStatus status,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/extensions/$extensionId/status'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'status': status.toString().split('.').last,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          return RentalExtension.fromJson(data['data']);
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // التحقق من إمكانية التمديد
  Future<bool> canExtendRental({
    required String bookingId,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/rentals/$bookingId/can-extend'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['can_extend'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      // في حالة عدم وجود اتصال، نفترض أنه يمكن التمديد
      return true;
    }
  }
}
