import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/car_model.dart';
import '../services/booking_service.dart';
import '../providers/auth_provider.dart';
import '../constants/app_colors.dart';

class BookingDialog extends StatefulWidget {
  final CarModel car;

  const BookingDialog({Key? key, required this.car}) : super(key: key);

  @override
  State<BookingDialog> createState() => _BookingDialogState();
}

class _BookingDialogState extends State<BookingDialog> {
  final BookingService _bookingService = BookingService();
  final _formKey = GlobalKey<FormState>();

  DateTime _startDate = DateTime.now().add(Duration(hours: 2));
  DateTime _endDate = DateTime.now().add(Duration(days: 1));

  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();

  bool _isLoading = false;
  Map<String, double> _costs = {};

  @override
  void initState() {
    super.initState();
    _calculateCosts();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _calculateCosts() {
    setState(() {
      _costs = _bookingService.calculateBookingCosts(
        dailyRate: widget.car.dailyPrice,
        startDate: _startDate,
        endDate: _endDate,
      );
    });
  }

  Future<void> _processQuickBooking() async {
    if (!_formKey.currentState!.validate()) return;

    print('🚀 بدء عملية الحجز...');

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user == null) {
      print('❌ المستخدم غير مسجل دخول');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يجب تسجيل الدخول أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    print('✓ المستخدم: ${user.email}');

    setState(() {
      _isLoading = true;
    });

    try {
      // التحقق من توفر السيارة
      final isAvailable = await _bookingService.isCarAvailableForBooking(
        carId: widget.car.id,
        startDate: _startDate,
        endDate: _endDate,
      );

      if (!isAvailable) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('عذراً، السيارة غير متاحة في التواريخ المحددة'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // معلومات العميل البسيطة
      final customerInfo = {
        'name': _nameController.text,
        'phone': _phoneController.text,
      };

      // مواقع افتراضية (مطار دبي)
      final defaultLocation = {
        'name': 'مطار دبي الدولي',
        'address': 'Dubai International Airport, Dubai, UAE',
        'lat': 25.2532,
        'lng': 55.3657,
      };

      print('💳 بدء عملية إنشاء الحجز والدفع...');

      // إنشاء الحجز مع الدفع
      final booking = await _bookingService.createBookingWithPayment(
        context: context,
        userId: user.id,
        car: widget.car,
        startDate: _startDate,
        endDate: _endDate,
        pickupLocation: defaultLocation,
        returnLocation: defaultLocation,
        customerInfo: customerInfo,
      );

      print('📋 نتيجة الحجز: ${booking != null ? 'نجح' : 'فشل'}');

      if (!mounted) return;

      if (booking != null) {
        print('🎉 إغلاق النافذة وعرض رسالة النجاح');
        Navigator.of(context).pop(); // إغلاق النافذة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🎉 تم الحجز والدفع بنجاح!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e'), backgroundColor: Colors.red),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(maxWidth: 400),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عنوان النافذة
              Text(
                'حجز سريع',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),

              // معلومات السيارة
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.directions_car, color: AppColors.primary),
                    SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${widget.car.make} ${widget.car.model}',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${widget.car.dailyPrice.toStringAsFixed(0)} درهم/يوم',
                            style: GoogleFonts.cairo(
                              color: AppColors.primary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),

              // التواريخ
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('من', style: GoogleFonts.cairo(fontSize: 12)),
                        GestureDetector(
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _startDate,
                              firstDate: DateTime.now(),
                              lastDate: DateTime.now().add(Duration(days: 365)),
                            );
                            if (date != null) {
                              setState(() {
                                _startDate = date;
                                if (_endDate.isBefore(_startDate)) {
                                  _endDate = _startDate.add(Duration(days: 1));
                                }
                                _calculateCosts();
                              });
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                              style: GoogleFonts.cairo(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('إلى', style: GoogleFonts.cairo(fontSize: 12)),
                        GestureDetector(
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _endDate,
                              firstDate: _startDate,
                              lastDate: DateTime.now().add(Duration(days: 365)),
                            );
                            if (date != null) {
                              setState(() {
                                _endDate = date;
                                _calculateCosts();
                              });
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${_endDate.day}/${_endDate.month}/${_endDate.year}',
                              style: GoogleFonts.cairo(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),

              // معلومات العميل
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.person, color: AppColors.primary),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الاسم';
                  }
                  return null;
                },
                style: GoogleFonts.cairo(),
              ),
              SizedBox(height: 12),

              TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: 'رقم الهاتف',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.phone, color: AppColors.primary),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال رقم الهاتف';
                  }
                  return null;
                },
                style: GoogleFonts.cairo(),
              ),
              SizedBox(height: 16),

              // ملخص التكلفة
              if (_costs.isNotEmpty)
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('عدد الأيام:', style: GoogleFonts.cairo()),
                          Text(
                            '${_costs['numberOfDays']?.toInt() ?? 0}',
                            style: GoogleFonts.cairo(),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'المجموع:',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${_costs['totalAmount']?.toStringAsFixed(0) ?? '0'} درهم',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 20),

              // الأزرار
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('إلغاء', style: GoogleFonts.cairo()),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _processQuickBooking,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isLoading
                          ? SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : Text(
                              'احجز واتم الدفع',
                              style: GoogleFonts.cairo(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
