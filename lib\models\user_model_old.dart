import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final String id;
  final String email;
  final String? fullName;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final String? licenseImageUrl;
  final DateTime? dateOfBirth;
  final String? address;
  final String? city;
  final String? country;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final bool isLicenseVerified;
  final bool isProfileComplete;
  final UserStatus status;
  final String? preferredLanguage;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.email,
    this.fullName,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.profileImageUrl,
    this.licenseImageUrl,
    this.dateOfBirth,
    this.address,
    this.city,
    this.country,
    this.emergencyContactName,
    this.emergencyContactPhone,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.isLicenseVerified,
    required this.isProfileComplete,
    required this.status,
    this.preferredLanguage,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory constructor to create from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      phoneNumber: json['phone'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      licenseImageUrl: json['license_image_url'] as String?,
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      address: json['address'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
      emergencyContactName: json['emergency_contact_name'] as String?,
      emergencyContactPhone: json['emergency_contact_phone'] as String?,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      isPhoneVerified: json['is_phone_verified'] as bool? ?? false,
      isLicenseVerified: json['is_license_verified'] as bool? ?? false,
      isProfileComplete: json['is_profile_complete'] as bool? ?? false,
      status: UserStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String? ?? 'active'),
        orElse: () => UserStatus.active,
      ),
      preferredLanguage: json['preferred_language'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phoneNumber,
      'profile_image_url': profileImageUrl,
      'license_image_url': licenseImageUrl,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'address': address,
      'city': city,
      'country': country,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'is_email_verified': isEmailVerified,
      'is_phone_verified': isPhoneVerified,
      'is_license_verified': isLicenseVerified,
      'is_profile_complete': isProfileComplete,
      'status': status.name,
      'preferred_language': preferredLanguage,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for immutable updates
  UserModel copyWith({
    String? id,
    String? email,
    String? fullName,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
    String? licenseImageUrl,
    DateTime? dateOfBirth,
    String? address,
    String? city,
    String? country,
    String? emergencyContactName,
    String? emergencyContactPhone,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isLicenseVerified,
    bool? isProfileComplete,
    UserStatus? status,
    String? preferredLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      licenseImageUrl: licenseImageUrl ?? this.licenseImageUrl,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isLicenseVerified: isLicenseVerified ?? this.isLicenseVerified,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      status: status ?? this.status,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        fullName,
        firstName,
        lastName,
        phoneNumber,
        profileImageUrl,
        licenseImageUrl,
        dateOfBirth,
        address,
        city,
        country,
        emergencyContactName,
        emergencyContactPhone,
        isEmailVerified,
        isPhoneVerified,
        isLicenseVerified,
        isProfileComplete,
        status,
        preferredLanguage,
        createdAt,
        updatedAt,
      ];
}

enum UserStatus {
  active,
  inactive,
  suspended,
  pending,
}

// Extension to get localized status names
extension UserStatusExtension on UserStatus {
  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'Active';
      case UserStatus.inactive:
        return 'Inactive';
      case UserStatus.suspended:
        return 'Suspended';
      case UserStatus.pending:
        return 'Pending';
    }
  }

  String get displayNameArabic {
    switch (this) {
      case UserStatus.active:
        return 'نشط';
      case UserStatus.inactive:
        return 'غير نشط';
      case UserStatus.suspended:
        return 'محظور';
      case UserStatus.pending:
        return 'في الانتظار';
    }
  }
}
