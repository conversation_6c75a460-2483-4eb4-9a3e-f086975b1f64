import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/auth_provider.dart';
import '../../constants/app_colors.dart';

class AccountInfoScreen extends StatelessWidget {
  const AccountInfoScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'معلومات الحساب',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (!authProvider.isAuthenticated) {
            return Center(
              child: Text('يرجى تسجيل الدخول أولاً'),
            );
          }

          final user = authProvider.currentUser!;

          return SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                // Profile Card
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Profile Picture
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primary.withOpacity(0.1),
                          border: Border.all(
                              color: AppColors.primary.withOpacity(0.3)),
                        ),
                        child: Icon(
                          Icons.person,
                          size: 40,
                          color: AppColors.primary,
                        ),
                      ),

                      SizedBox(height: 16),

                      Text(
                        user.fullName ?? 'المستخدم',
                        style: GoogleFonts.cairo(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.onSurface,
                        ),
                      ),

                      SizedBox(height: 4),

                      Text(
                        user.email ?? '',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),

                      SizedBox(height: 16),

                      // Status Badge
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: user.isVerified
                              ? Colors.green.withOpacity(0.1)
                              : Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: user.isVerified
                                ? Colors.green.withOpacity(0.3)
                                : Colors.orange.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              user.isVerified ? Icons.verified : Icons.warning,
                              size: 16,
                              color: user.isVerified
                                  ? Colors.green
                                  : Colors.orange,
                            ),
                            SizedBox(width: 8),
                            Text(
                              user.isVerified ? 'حساب موثق' : 'حساب غير موثق',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: user.isVerified
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 20),

                // Personal Information
                _buildInfoSection('المعلومات الشخصية', [
                  _buildInfoItem('الاسم الكامل', user.fullName ?? 'غير محدد'),
                  _buildInfoItem('الاسم الأول', user.firstName ?? 'غير محدد'),
                  _buildInfoItem('اسم العائلة', user.lastName ?? 'غير محدد'),
                  _buildInfoItem('رقم الهاتف', user.phone ?? 'غير محدد'),
                  _buildInfoItem('الجنسية', user.nationality ?? 'غير محدد'),
                  _buildInfoItem(
                      'تاريخ الميلاد',
                      user.dateOfBirth != null
                          ? '${user.dateOfBirth!.day}/${user.dateOfBirth!.month}/${user.dateOfBirth!.year}'
                          : 'غير محدد'),
                ]),

                SizedBox(height: 16),

                // Address Information
                _buildInfoSection('معلومات العنوان', [
                  _buildInfoItem('العنوان', user.address ?? 'غير محدد'),
                  _buildInfoItem('المدينة', user.city ?? 'غير محدد'),
                  _buildInfoItem('الإمارة', user.emirate ?? 'غير محدد'),
                  _buildInfoItem(
                      'الدولة', user.country ?? 'الإمارات العربية المتحدة'),
                ]),

                SizedBox(height: 16),

                // Emergency Contact
                _buildInfoSection('جهة الاتصال للطوارئ', [
                  _buildInfoItem(
                      'الاسم', user.emergencyContactName ?? 'غير محدد'),
                  _buildInfoItem(
                      'رقم الهاتف', user.emergencyContactPhone ?? 'غير محدد'),
                ]),

                SizedBox(height: 16),

                // Document Status
                _buildInfoSection('حالة الوثائق', [
                  _buildDocumentStatusItem('هوية الإمارات (الوجه الأمامي)',
                      user.emiratesIdFrontUrl != null),
                  _buildDocumentStatusItem('هوية الإمارات (الوجه الخلفي)',
                      user.emiratesIdBackUrl != null),
                  _buildDocumentStatusItem(
                      'رخصة القيادة', user.drivingLicenseUrl != null),
                  _buildDocumentStatusItem(
                      'جواز السفر', user.passportUrl != null),
                  _buildDocumentStatusItem(
                      'التوقيع الإلكتروني', user.signatureUrl != null),
                ]),

                // Document Upload Reminder
                if (!user.isDocumentsVerified ||
                    user.emiratesIdFrontUrl == null ||
                    user.emiratesIdBackUrl == null ||
                    user.drivingLicenseUrl == null ||
                    user.signatureUrl == null) ...[
                  SizedBox(height: 16),
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.warning_amber,
                                color: Colors.orange[700], size: 24),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'وثائقك غير مكتملة',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          'يرجى رفع جميع الوثائق المطلوبة لإتمام عملية التحقق والاستفادة من جميع خدمات التطبيق.',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.orange[700],
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                SizedBox(height: 16),

                // Verification Status
                _buildInfoSection('حالة التحقق', [
                  _buildVerificationItem(
                      'البريد الإلكتروني', user.isEmailVerified),
                  _buildVerificationItem('رقم الهاتف', user.isPhoneVerified),
                  _buildVerificationItem('الوثائق', user.isDocumentsVerified),
                  _buildVerificationItem(
                      'رخصة القيادة', user.isLicenseVerified),
                ]),

                SizedBox(height: 24),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(context, '/edit-profile');
                        },
                        icon: Icon(Icons.edit),
                        label: Text(
                          'تعديل الملف',
                          style: GoogleFonts.cairo(),
                        ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary),
                          padding: EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(context, '/document-upload');
                        },
                        icon: Icon(Icons.upload_file),
                        label: Text(
                          'رفع الوثائق',
                          style: GoogleFonts.cairo(),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationItem(String label, bool isVerified) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Icon(
                  isVerified ? Icons.check_circle : Icons.cancel,
                  size: 16,
                  color: isVerified ? Colors.green : Colors.red,
                ),
                SizedBox(width: 8),
                Text(
                  isVerified ? 'موثق' : 'غير موثق',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: isVerified ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentStatusItem(String label, bool isUploaded) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Icon(
                  isUploaded ? Icons.cloud_done : Icons.cloud_off,
                  size: 16,
                  color: isUploaded ? Colors.blue : Colors.grey,
                ),
                SizedBox(width: 8),
                Text(
                  isUploaded ? 'تم الرفع' : 'لم يتم الرفع',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: isUploaded ? Colors.blue : Colors.grey,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
