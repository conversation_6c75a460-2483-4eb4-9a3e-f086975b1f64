import 'package:flutter/material.dart';

class PriceSummaryCard extends StatelessWidget {
  final double basePrice;
  final double taxes;
  final double serviceFee;
  final double? discount;
  final double? deliveryFee;
  final Map<String, double>? additionalServices;
  final int rentalDays;
  final String carName;
  final VoidCallback? onEdit;

  const PriceSummaryCard({
    super.key,
    required this.basePrice,
    required this.taxes,
    required this.serviceFee,
    required this.rentalDays,
    required this.carName,
    this.discount,
    this.deliveryFee,
    this.additionalServices,
    this.onEdit,
  });

  double get totalPrice {
    double total = basePrice + taxes + serviceFee;

    if (deliveryFee != null) {
      total += deliveryFee!;
    }

    if (additionalServices != null) {
      total +=
          additionalServices!.values.fold(0.0, (sum, price) => sum + price);
    }

    if (discount != null) {
      total -= discount!;
    }

    return total;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Booking Summary',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (onEdit != null)
                  TextButton(
                    onPressed: onEdit,
                    child: const Text('Edit'),
                  ),
              ],
            ),

            const SizedBox(height: 8),

            Text(
              carName,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),

            Text(
              '$rentalDays day${rentalDays > 1 ? 's' : ''} rental',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),

            const Divider(height: 24),

            // Price breakdown
            _buildPriceRow('Subtotal', basePrice, theme),

            if (additionalServices != null &&
                additionalServices!.isNotEmpty) ...[
              const SizedBox(height: 4),
              ...additionalServices!.entries.map(
                (service) => _buildPriceRow(
                  service.key,
                  service.value,
                  theme,
                  isSmall: true,
                ),
              ),
            ],

            if (deliveryFee != null) ...[
              const SizedBox(height: 4),
              _buildPriceRow('Delivery', deliveryFee!, theme, isSmall: true),
            ],

            const SizedBox(height: 4),
            _buildPriceRow('Service fee', serviceFee, theme, isSmall: true),

            const SizedBox(height: 4),
            _buildPriceRow('Taxes', taxes, theme, isSmall: true),

            if (discount != null && discount! > 0) ...[
              const SizedBox(height: 4),
              _buildPriceRow(
                'Discount',
                -discount!,
                theme,
                isSmall: true,
                isDiscount: true,
              ),
            ],

            const Divider(height: 16),

            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'AED ${totalPrice.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRow(
    String label,
    double amount,
    ThemeData theme, {
    bool isSmall = false,
    bool isDiscount = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style:
              (isSmall ? theme.textTheme.bodySmall : theme.textTheme.bodyMedium)
                  ?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        Text(
          '${isDiscount ? '-' : ''}AED ${amount.abs().toStringAsFixed(2)}',
          style:
              (isSmall ? theme.textTheme.bodySmall : theme.textTheme.bodyMedium)
                  ?.copyWith(
            fontWeight: FontWeight.w500,
            color: isDiscount ? Colors.green : theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}
