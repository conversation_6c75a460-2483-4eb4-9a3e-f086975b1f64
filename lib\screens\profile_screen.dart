import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/auth_provider.dart';
import '../providers/language_provider.dart';
import '../providers/theme_provider.dart';
import '../constants/app_colors.dart';
import '../widgets/profile_menu_item.dart';
import '../widgets/user_stats_card.dart';
import '../utils/auth_utils.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildProfileHeader(),
              const SizedBox(height: 20),
              _buildMenuSection(),
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.primary, AppColors.primaryDark],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  // Profile Image
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                      image: authProvider.isAuthenticated &&
                              authProvider.currentUser?.profileImageUrl != null
                          ? DecorationImage(
                              image: NetworkImage(
                                  authProvider.currentUser!.profileImageUrl!),
                              fit: BoxFit.cover,
                            )
                          : null,
                    ),
                    child: authProvider.isAuthenticated &&
                            authProvider.currentUser?.profileImageUrl == null
                        ? const Icon(
                            Icons.person,
                            size: 40,
                            color: Colors.white,
                          )
                        : !authProvider.isAuthenticated
                            ? const Icon(
                                Icons.person_outline,
                                size: 40,
                                color: Colors.white,
                              )
                            : null,
                  ),
                  const SizedBox(width: 20),
                  // Profile Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (authProvider.isAuthenticated) ...[
                          Text(
                            authProvider.currentUser?.fullName ?? 'المستخدم',
                            style: GoogleFonts.cairo(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            authProvider.currentUser?.email ?? '',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'عضو مميز',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ] else ...[
                          Text(
                            'مرحباً بك!',
                            style: GoogleFonts.cairo(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(context, '/login');
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: AppColors.primary,
                            ),
                            child: Text('تسجيل الدخول'),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              if (authProvider.isAuthenticated) ...[
                const SizedBox(height: 20),

                // إحصائيات المستخدم
                UserStatsCard(),

                // Quick Actions
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildQuickAction(
                          icon: Icons.edit,
                          label: 'تعديل الملف',
                          onTap: () {
                            Navigator.pushNamed(context, '/edit-profile');
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildQuickAction(
                          icon: Icons.help_outline,
                          label: 'المساعدة',
                          onTap: () {
                            Navigator.pushNamed(context, '/help');
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildQuickAction(
                          icon: Icons.share,
                          label: 'مشاركة التطبيق',
                          onTap: () {
                            _shareApp();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _shareApp() {
    Share.share(
      'جرب تطبيق دبي لتأجير السيارات - أفضل الأسعار وأجمل السيارات في دبي!\n\nحمل التطبيق الآن من:\nhttps://play.google.com/store/apps/details?id=com.dubaicarrental.app',
      subject: 'تطبيق دبي لتأجير السيارات',
    );
  }

  void _launchPhone(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  void _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=استفسار عن تطبيق دبي لتأجير السيارات',
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _launchWhatsApp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse(
        'https://wa.me/$phoneNumber?text=مرحباً، أحتاج مساعدة في تطبيق دبي لتأجير السيارات');
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('اتصل بنا'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.phone, color: AppColors.primary),
              title: Text('اتصال هاتفي'),
              subtitle: Text('+971 4 123 4567'),
              onTap: () {
                Navigator.pop(context);
                _launchPhone('+97141234567');
              },
            ),
            ListTile(
              leading: Icon(Icons.email, color: AppColors.primary),
              title: Text('بريد إلكتروني'),
              subtitle: Text('<EMAIL>'),
              onTap: () {
                Navigator.pop(context);
                _launchEmail('<EMAIL>');
              },
            ),
            ListTile(
              leading: Icon(Icons.chat, color: AppColors.primary),
              title: Text('واتساب'),
              subtitle: Text('+971 50 123 4567'),
              onTap: () {
                Navigator.pop(context);
                _launchWhatsApp('+************');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection() {
    return Consumer3<AuthProvider, LanguageProvider, ThemeProvider>(
      builder: (context, authProvider, languageProvider, themeProvider, _) {
        return Column(
          children: [
            // Account Section
            if (authProvider.isAuthenticated) ...[
              _buildSectionHeader('الحساب'),
              ProfileMenuItem(
                icon: Icons.person_outline,
                title: 'معلومات الحساب',
                onTap: () {
                  Navigator.pushNamed(context, '/account-info');
                },
              ),
              ProfileMenuItem(
                icon: Icons.history,
                title: 'سجل الحجوزات',
                onTap: () {
                  Navigator.pushNamed(context, '/bookings');
                },
              ),
              ProfileMenuItem(
                icon: Icons.credit_card_outlined,
                title: 'طرق الدفع',
                onTap: () {
                  Navigator.pushNamed(context, '/payment-methods');
                },
              ),
              ProfileMenuItem(
                icon: Icons.security,
                title: 'رفع الوثائق',
                subtitle: 'هوية الإمارات ورخصة القيادة',
                onTap: () {
                  Navigator.pushNamed(context, '/document-upload');
                },
              ),
            ],

            // App Settings
            _buildSectionHeader('الإعدادات'),
            ProfileMenuItem(
              icon: Icons.language,
              title: 'اللغة',
              subtitle: languageProvider.isArabic ? 'العربية' : 'English',
              onTap: () {
                _showLanguageDialog();
              },
            ),
            ProfileMenuItem(
              icon: Icons.dark_mode_outlined,
              title: 'المظهر',
              subtitle: _getThemeName(themeProvider.themeMode),
              onTap: () {
                _showThemeDialog();
              },
            ),
            ProfileMenuItem(
              icon: Icons.notifications_none,
              title: 'الإشعارات',
              onTap: () {
                Navigator.pushNamed(context, '/notifications');
              },
            ),

            // Support & Info
            _buildSectionHeader('الدعم والمعلومات'),
            ProfileMenuItem(
              icon: Icons.help_outline,
              title: 'مركز المساعدة',
              onTap: () {
                Navigator.pushNamed(context, '/help-center');
              },
            ),
            ProfileMenuItem(
              icon: Icons.phone_outlined,
              title: 'اتصل بنا',
              onTap: () {
                _showContactDialog();
              },
            ),

            if (authProvider.isAuthenticated) ...[
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () => _showLogoutDialog(authProvider),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                    ),
                    child: Text('تسجيل الخروج'),
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.onSurfaceVariant,
        ),
      ),
    );
  }

  String _getThemeName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
      case ThemeMode.system:
        return 'تلقائي';
    }
  }

  void _showLanguageDialog() {
    final languageProvider =
        Provider.of<LanguageProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('العربية'),
              leading: Radio<bool>(
                value: true,
                groupValue: languageProvider.isArabic,
                onChanged: (value) {
                  languageProvider.setLanguage('ar');
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: Text('English'),
              leading: Radio<bool>(
                value: false,
                groupValue: languageProvider.isArabic,
                onChanged: (value) {
                  languageProvider.setLanguage('en');
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showThemeDialog() {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('اختر المظهر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('فاتح'),
              leading: Radio<ThemeMode>(
                value: ThemeMode.light,
                groupValue: themeProvider.themeMode,
                onChanged: (value) {
                  themeProvider.changeThemeMode(value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: Text('داكن'),
              leading: Radio<ThemeMode>(
                value: ThemeMode.dark,
                groupValue: themeProvider.themeMode,
                onChanged: (value) {
                  themeProvider.changeThemeMode(value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: Text('تلقائي'),
              leading: Radio<ThemeMode>(
                value: ThemeMode.system,
                groupValue: themeProvider.themeMode,
                onChanged: (value) {
                  themeProvider.changeThemeMode(value!);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog(AuthProvider authProvider) {
    AuthUtils.showSignOutDialog(context);
  }
}
