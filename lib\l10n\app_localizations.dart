import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('en', 'US'),
    Locale('ar', 'AE'),
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    _AppLocalizationsDelegate(),
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];

  bool get isArabic => locale.languageCode == 'ar';

  // Authentication strings
  String get login => isArabic ? 'تسجيل الدخول' : 'Login';
  String get register => isArabic ? 'إنشاء حساب' : 'Register';
  String get email => isArabic ? 'البريد الإلكتروني' : 'Email';
  String get password => isArabic ? 'كلمة المرور' : 'Password';
  String get forgotPassword =>
      isArabic ? 'نسيت كلمة المرور؟' : 'Forgot Password?';
  String get rememberMe => isArabic ? 'تذكرني' : 'Remember Me';
  String get dontHaveAccount =>
      isArabic ? 'ليس لديك حساب؟' : "Don't have an account?";
  String get alreadyHaveAccount =>
      isArabic ? 'لديك حساب بالفعل؟' : 'Already have an account?';
  String get signInWith => isArabic ? 'تسجيل الدخول باستخدام' : 'Sign in with';
  String get google => isArabic ? 'جوجل' : 'Google';
  String get facebook => isArabic ? 'فيسبوك' : 'Facebook';
  String get apple => isArabic ? 'أبل' : 'Apple';
  String get or => isArabic ? 'أو' : 'OR';
  String get continueAsGuest =>
      isArabic ? 'المتابعة كضيف' : 'Continue as Guest';

  // Validation messages
  String get emailRequired =>
      isArabic ? 'البريد الإلكتروني مطلوب' : 'Email is required';
  String get emailInvalid =>
      isArabic ? 'البريد الإلكتروني غير صحيح' : 'Invalid email format';
  String get passwordRequired =>
      isArabic ? 'كلمة المرور مطلوبة' : 'Password is required';
  String get passwordMinLength => isArabic
      ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
      : 'Password must be at least 6 characters';
  String get passwordsDoNotMatch =>
      isArabic ? 'كلمات المرور غير متطابقة' : 'Passwords do not match';

  // Error messages
  String get loginFailed => isArabic ? 'فشل في تسجيل الدخول' : 'Login failed';
  String get googleSignInFailed =>
      isArabic ? 'فشل في تسجيل الدخول بجوجل' : 'Google sign in failed';
  String get facebookSignInFailed =>
      isArabic ? 'فشل في تسجيل الدخول بفيسبوك' : 'Facebook sign in failed';
  String get invalidCredentials =>
      isArabic ? 'بيانات الدخول غير صحيحة' : 'Invalid credentials';
  String get accountNotFound =>
      isArabic ? 'الحساب غير موجود' : 'Account not found';
  String get accountDisabled =>
      isArabic ? 'الحساب معطل' : 'Account is disabled';
  String get tooManyAttempts => isArabic
      ? 'محاولات كثيرة، جرب لاحقاً'
      : 'Too many attempts. Try again later';
  String get networkError => isArabic ? 'خطأ في الشبكة' : 'Network error';
  String get unexpectedError =>
      isArabic ? 'خطأ غير متوقع' : 'Unexpected error occurred';

  // Registration strings
  String get fullName => isArabic ? 'الاسم الكامل' : 'Full Name';
  String get phoneNumber => isArabic ? 'رقم الهاتف' : 'Phone Number';
  String get confirmPassword =>
      isArabic ? 'تأكيد كلمة المرور' : 'Confirm Password';
  String get dateOfBirth => isArabic ? 'تاريخ الميلاد' : 'Date of Birth';
  String get nationality => isArabic ? 'الجنسية' : 'Nationality';
  String get agreeToTerms =>
      isArabic ? 'أوافق على الشروط والأحكام' : 'I agree to Terms & Conditions';
  String get subscribeToOffers =>
      isArabic ? 'الاشتراك في العروض' : 'Subscribe to offers';
  String get createAccount => isArabic ? 'إنشاء الحساب' : 'Create Account';
  String get signUp => isArabic ? 'إنشاء حساب' : 'Sign Up';

  // Profile strings
  String get profile => isArabic ? 'الملف الشخصي' : 'Profile';
  String get editProfile => isArabic ? 'تعديل الملف الشخصي' : 'Edit Profile';
  String get changePassword =>
      isArabic ? 'تغيير كلمة المرور' : 'Change Password';
  String get documents => isArabic ? 'الوثائق' : 'Documents';
  String get settings => isArabic ? 'الإعدادات' : 'Settings';
  String get logout => isArabic ? 'تسجيل الخروج' : 'Logout';

  // Car related strings
  String get cars => isArabic ? 'السيارات' : 'Cars';
  String get carDetails => isArabic ? 'تفاصيل السيارة' : 'Car Details';
  String get available => isArabic ? 'متاحة' : 'Available';
  String get unavailable => isArabic ? 'غير متاحة' : 'Unavailable';
  String get rentNow => isArabic ? 'احجز الآن' : 'Rent Now';
  String get bookNow => isArabic ? 'احجز الآن' : 'Book Now';
  String get pricePerDay => isArabic ? 'السعر لليوم' : 'Price per day';
  String get features => isArabic ? 'المميزات' : 'Features';
  String get specifications => isArabic ? 'المواصفات' : 'Specifications';
  String get reviews => isArabic ? 'التقييمات' : 'Reviews';
  String get rating => isArabic ? 'التقييم' : 'Rating';

  // Booking strings
  String get booking => isArabic ? 'الحجز' : 'Booking';
  String get bookings => isArabic ? 'الحجوزات' : 'Bookings';
  String get bookingDetails => isArabic ? 'تفاصيل الحجز' : 'Booking Details';
  String get bookingConfirmation =>
      isArabic ? 'تأكيد الحجز' : 'Booking Confirmation';
  String get pickupLocation => isArabic ? 'مكان الاستلام' : 'Pickup Location';
  String get dropoffLocation => isArabic ? 'مكان التسليم' : 'Drop-off Location';
  String get pickupDate => isArabic ? 'تاريخ الاستلام' : 'Pickup Date';
  String get dropoffDate => isArabic ? 'تاريخ التسليم' : 'Drop-off Date';
  String get totalDays => isArabic ? 'إجمالي الأيام' : 'Total Days';
  String get totalPrice => isArabic ? 'إجمالي السعر' : 'Total Price';
  String get paymentMethod => isArabic ? 'طريقة الدفع' : 'Payment Method';
  String get payNow => isArabic ? 'ادفع الآن' : 'Pay Now';
  String get confirm => isArabic ? 'تأكيد' : 'Confirm';
  String get cancel => isArabic ? 'إلغاء' : 'Cancel';
  String get cancelled => isArabic ? 'ملغى' : 'Cancelled';
  String get confirmed => isArabic ? 'مؤكد' : 'Confirmed';
  String get completed => isArabic ? 'مكتمل' : 'Completed';
  String get pending => isArabic ? 'في الانتظار' : 'Pending';

  // Payment strings
  String get payment => isArabic ? 'الدفع' : 'Payment';
  String get creditCard => isArabic ? 'بطاقة ائتمان' : 'Credit Card';
  String get debitCard => isArabic ? 'بطاقة خصم' : 'Debit Card';
  String get cash => isArabic ? 'نقداً' : 'Cash';
  String get cardNumber => isArabic ? 'رقم البطاقة' : 'Card Number';
  String get expiryDate => isArabic ? 'تاريخ الانتهاء' : 'Expiry Date';
  String get cvv => isArabic ? 'رمز الأمان' : 'CVV';
  String get cardHolderName =>
      isArabic ? 'اسم حامل البطاقة' : 'Cardholder Name';
  String get paymentSuccessful =>
      isArabic ? 'تم الدفع بنجاح' : 'Payment Successful';
  String get paymentFailed => isArabic ? 'فشل في الدفع' : 'Payment Failed';

  // General strings
  String get home => isArabic ? 'الرئيسية' : 'Home';
  String get ok => isArabic ? 'موافق' : 'OK';
  String get yes => isArabic ? 'نعم' : 'Yes';
  String get no => isArabic ? 'لا' : 'No';
  String get save => isArabic ? 'حفظ' : 'Save';
  String get edit => isArabic ? 'تعديل' : 'Edit';
  String get delete => isArabic ? 'حذف' : 'Delete';
  String get search => isArabic ? 'بحث' : 'Search';
  String get filter => isArabic ? 'فلترة' : 'Filter';
  String get sort => isArabic ? 'ترتيب' : 'Sort';
  String get loading => isArabic ? 'جاري التحميل...' : 'Loading...';
  String get noData => isArabic ? 'لا توجد بيانات' : 'No data available';
  String get retry => isArabic ? 'إعادة المحاولة' : 'Retry';
  String get back => isArabic ? 'رجوع' : 'Back';
  String get next => isArabic ? 'التالي' : 'Next';
  String get previous => isArabic ? 'السابق' : 'Previous';
  String get skip => isArabic ? 'تخطي' : 'Skip';
  String get done => isArabic ? 'تم' : 'Done';
  String get close => isArabic ? 'إغلاق' : 'Close';
  String get select => isArabic ? 'اختيار' : 'Select';
  String get selectDate => isArabic ? 'اختر التاريخ' : 'Select Date';
  String get selectTime => isArabic ? 'اختر الوقت' : 'Select Time';
  String get clear => isArabic ? 'مسح' : 'Clear';
  String get apply => isArabic ? 'تطبيق' : 'Apply';
  String get upload => isArabic ? 'رفع' : 'Upload';
  String get download => isArabic ? 'تنزيل' : 'Download';
  String get share => isArabic ? 'مشاركة' : 'Share';
  String get copy => isArabic ? 'نسخ' : 'Copy';
  String get paste => isArabic ? 'لصق' : 'Paste';
  String get cut => isArabic ? 'قص' : 'Cut';
  String get refresh => isArabic ? 'تحديث' : 'Refresh';
  String get update => isArabic ? 'تحديث' : 'Update';
  String get version => isArabic ? 'الإصدار' : 'Version';
  String get appName => isArabic ? 'تأجير سيارات دبي' : 'Dubai Car Rental';
  String get welcome => isArabic ? 'مرحباً' : 'Welcome';
  String get welcomeBack => isArabic ? 'مرحباً بعودتك' : 'Welcome Back';
  String get getStarted => isArabic ? 'ابدأ الآن' : 'Get Started';

  // Additional missing strings for login screen
  String get signIn => isArabic ? 'تسجيل الدخول' : 'Sign In';
  String get loginSubtitle => isArabic
      ? 'أهلاً بعودتك، سجل دخولك للمتابعة'
      : 'Welcome back, please sign in to continue';
  String get enterEmail => isArabic ? 'أدخل البريد الإلكتروني' : 'Enter email';
  String get enterPassword => isArabic ? 'أدخل كلمة المرور' : 'Enter password';
  String get passwordTooShort =>
      isArabic ? 'كلمة المرور قصيرة جداً' : 'Password is too short';
  String get orContinueWith =>
      isArabic ? 'أو تابع باستخدام' : 'Or continue with';
  String get loginWithGoogle =>
      isArabic ? 'تسجيل الدخول بجوجل' : 'Sign in with Google';
  String get termsAndConditions =>
      isArabic ? 'الشروط والأحكام' : 'Terms & Conditions';
  String get userNotFound => isArabic ? 'المستخدم غير موجود' : 'User not found';
  String get wrongPassword => isArabic ? 'كلمة مرور خاطئة' : 'Wrong password';
  String get dismiss => isArabic ? 'إغلاق' : 'Dismiss';
  String get selectLanguage => isArabic ? 'اختر اللغة' : 'Select Language';
  String get addedToFavorites =>
      isArabic ? 'تمت الإضافة للمفضلة' : 'Added to favorites';
  String get removedFromFavorites =>
      isArabic ? 'تم الحذف من المفضلة' : 'Removed from favorites';

  // Document verification strings
  String get documentsVerification =>
      isArabic ? 'التحقق من الوثائق' : 'Documents Verification';
  String get uploadDocuments => isArabic ? 'رفع الوثائق' : 'Upload Documents';
  String get emiratesId => isArabic ? 'الهوية الإماراتية' : 'Emirates ID';
  String get drivingLicense => isArabic ? 'رخصة القيادة' : 'Driving License';
  String get passport => isArabic ? 'جواز السفر' : 'Passport';
  String get frontSide => isArabic ? 'الوجه الأمامي' : 'Front Side';
  String get backSide => isArabic ? 'الوجه الخلفي' : 'Back Side';
  String get documentUploaded =>
      isArabic ? 'تم رفع الوثيقة' : 'Document Uploaded';
  String get documentPending =>
      isArabic ? 'الوثيقة قيد المراجعة' : 'Document Pending Review';
  String get documentVerified =>
      isArabic ? 'تم التحقق من الوثيقة' : 'Document Verified';
  String get documentRejected =>
      isArabic ? 'تم رفض الوثيقة' : 'Document Rejected';
  String get submitForReview =>
      isArabic ? 'إرسال للمراجعة' : 'Submit for Review';

  // Onboarding strings
  String get onboardingTitle1 => isArabic
      ? 'اختر من مجموعة واسعة من السيارات'
      : 'Choose from a wide range of cars';
  String get onboardingDesc1 => isArabic
      ? 'اكتشف مجموعة متنوعة من السيارات الفاخرة والاقتصادية'
      : 'Discover a variety of luxury and economy cars';
  String get onboardingTitle2 =>
      isArabic ? 'حجز سهل وسريع' : 'Easy and fast booking';
  String get onboardingDesc2 => isArabic
      ? 'احجز سيارتك في دقائق معدودة من خلال التطبيق'
      : 'Book your car in minutes through the app';
  String get onboardingTitle3 =>
      isArabic ? 'دفع آمن ومرن' : 'Safe and flexible payment';
  String get onboardingDesc3 => isArabic
      ? 'ادفع بأمان باستخدام طرق دفع متعددة'
      : 'Pay safely using multiple payment methods';

  // Additional strings that might be needed
  String get personalInfo =>
      isArabic ? 'المعلومات الشخصية' : 'Personal Information';
  String get additionalInfo =>
      isArabic ? 'معلومات إضافية' : 'Additional Information';
  String get emergencyContact =>
      isArabic ? 'جهة الاتصال للطوارئ' : 'Emergency Contact';
  String get address => isArabic ? 'العنوان' : 'Address';
  String get city => isArabic ? 'المدينة' : 'City';
  String get country => isArabic ? 'البلد' : 'Country';
  String get zipCode => isArabic ? 'الرمز البريدي' : 'ZIP Code';
  String get required => isArabic ? 'مطلوب' : 'Required';
  String get optional => isArabic ? 'اختياري' : 'Optional';
  String get male => isArabic ? 'ذكر' : 'Male';
  String get female => isArabic ? 'أنثى' : 'Female';
  String get other => isArabic ? 'أخرى' : 'Other';
  String get language => isArabic ? 'اللغة' : 'Language';
  String get arabic => isArabic ? 'العربية' : 'Arabic';
  String get english => isArabic ? 'الإنجليزية' : 'English';
  String get theme => isArabic ? 'المظهر' : 'Theme';
  String get light => isArabic ? 'فاتح' : 'Light';
  String get dark => isArabic ? 'داكن' : 'Dark';
  String get system => isArabic ? 'النظام' : 'System';
  String get notifications => isArabic ? 'الإشعارات' : 'Notifications';
  String get enableNotifications =>
      isArabic ? 'تفعيل الإشعارات' : 'Enable Notifications';
  String get privacyPolicy => isArabic ? 'سياسة الخصوصية' : 'Privacy Policy';
  String get termsOfService => isArabic ? 'شروط الخدمة' : 'Terms of Service';
  String get aboutUs => isArabic ? 'من نحن' : 'About Us';
  String get contactUs => isArabic ? 'اتصل بنا' : 'Contact Us';
  String get helpSupport => isArabic ? 'المساعدة والدعم' : 'Help & Support';
  String get faq => isArabic ? 'الأسئلة الشائعة' : 'FAQ';

  // Additional missing strings for better coverage
  String get uploadRequired =>
      isArabic ? 'مطلوب رفع الوثيقة' : 'Upload Required';
  String get uploadDocument => isArabic ? 'رفع وثيقة' : 'Upload Document';
  String get reuploadDocument =>
      isArabic ? 'إعادة رفع الوثيقة' : 'Re-upload Document';
  String get chooseFromGallery =>
      isArabic ? 'اختر من المعرض' : 'Choose from Gallery';
  String get takePhoto => isArabic ? 'التقاط صورة' : 'Take Photo';
  String get pickFile => isArabic ? 'اختر ملف' : 'Pick File';
  String get dailyRental => isArabic ? 'إيجار يومي' : 'Daily Rental';
  String get addToFavorites => isArabic ? 'أضف للمفضلة' : 'Add to Favorites';
  String get removeFromFavorites =>
      isArabic ? 'حذف من المفضلة' : 'Remove from Favorites';
  String get returnLocation => isArabic ? 'موقع الإرجاع' : 'Return Location';
  String get sameAsPickup => isArabic ? 'نفس موقع الاستلام' : 'Same as pickup';
  String get differentLocation =>
      isArabic ? 'موقع مختلف' : 'Different location';
  String get personalInformation =>
      isArabic ? 'المعلومات الشخصية' : 'Personal Information';
  String get error => isArabic ? 'خطأ' : 'Error';
  String get success => isArabic ? 'نجح' : 'Success';
  String get warning => isArabic ? 'تحذير' : 'Warning';
  String get info => isArabic ? 'معلومات' : 'Info';

  // Car details and specifications strings
  String get seats => isArabic ? 'مقاعد' : 'Seats';
  String get aedPerDay => isArabic ? 'درهم/يوم' : 'AED/Day';
  String get excludingTaxes => isArabic ? 'لا تشمل الضرائب' : 'Excluding taxes';
  String get overview => isArabic ? 'نظرة عامة' : 'Overview';
  String get location => isArabic ? 'الموقع' : 'Location';
  String get description => isArabic ? 'الوصف' : 'Description';
  String get noDescriptionAvailable =>
      isArabic ? 'لا يوجد وصف متاح' : 'No description available';
  String get brand => isArabic ? 'الماركة' : 'Brand';
  String get model => isArabic ? 'الموديل' : 'Model';
  String get year => isArabic ? 'السنة' : 'Year';
  String get type => isArabic ? 'النوع' : 'Type';
  String get transmission => isArabic ? 'ناقل الحركة' : 'Transmission';
  String get fuelType => isArabic ? 'نوع الوقود' : 'Fuel Type';
  String get doors => isArabic ? 'الأبواب' : 'Doors';
  String get color => isArabic ? 'اللون' : 'Color';
  String get notSpecified => isArabic ? 'غير محدد' : 'Not specified';
  String get pricing => isArabic ? 'التسعير' : 'Pricing';
  String get generalFeatures =>
      isArabic ? 'المميزات العامة' : 'General Features';
  String get safetyFeatures => isArabic ? 'مميزات الأمان' : 'Safety Features';
  String get comfortFeatures => isArabic ? 'مميزات الراحة' : 'Comfort Features';
  String get loginRequired =>
      isArabic ? 'تسجيل الدخول مطلوب' : 'Login Required';
  String get loginRequiredMessage => isArabic
      ? 'يجب تسجيل الدخول لإضافة السيارة للمفضلة'
      : 'Please login to add car to favorites';

  // Additional missing strings
  String get availablePickupLocations =>
      isArabic ? 'مواقع الاستلام المتاحة' : 'Available Pickup Locations';
  String get noReviews => isArabic ? 'لا توجد مراجعات' : 'No Reviews';
  String get beFirstToReview => isArabic
      ? 'كن أول من يراجع هذه السيارة'
      : 'Be the first to review this car';
  String get viewAllReviews =>
      isArabic ? 'عرض جميع المراجعات' : 'View All Reviews';
  String get days => isArabic ? 'أيام' : 'days';
  String get discount => isArabic ? 'خصم' : 'Discount';
  String get insurance => isArabic ? 'التأمين' : 'Insurance';
  String get serviceFee => isArabic ? 'رسوم الخدمة' : 'Service Fee';
  String get total => isArabic ? 'المجموع' : 'Total';
  String get extraFees => isArabic ? 'رسوم إضافية' : 'Extra Fees';

  // Booking related strings
  String get selectDatesFirst =>
      isArabic ? 'الرجاء اختيار التواريخ أولاً' : 'Please select dates first';
  String get mustAcceptTerms => isArabic
      ? 'يجب قبول الشروط والأحكام'
      : 'You must accept terms and conditions';
  String get bookCar => isArabic ? 'حجز السيارة' : 'Book Car';
  String get dateTime => isArabic ? 'التاريخ والوقت' : 'Date & Time';
  String get details => isArabic ? 'التفاصيل' : 'Details';
  String get extras => isArabic ? 'الإضافات' : 'Extras';
  String get review => isArabic ? 'مراجعة' : 'Review';
  String get pickupDateTime =>
      isArabic ? 'تاريخ ووقت الاستلام' : 'Pickup Date & Time';
  String get returnDateTime =>
      isArabic ? 'تاريخ ووقت الإرجاع' : 'Return Date & Time';
  String get selectPickupDate =>
      isArabic ? 'اختر تاريخ الاستلام' : 'Select Pickup Date';
  String get selectPickupTime =>
      isArabic ? 'اختر وقت الاستلام' : 'Select Pickup Time';
  String get selectReturnDate =>
      isArabic ? 'اختر تاريخ الإرجاع' : 'Select Return Date';
  String get selectReturnTime =>
      isArabic ? 'اختر وقت الإرجاع' : 'Select Return Time';
  String get rentalDuration => isArabic ? 'مدة الإيجار' : 'Rental Duration';
  String get estimatedCost => isArabic ? 'التكلفة المتوقعة' : 'Estimated Cost';
  String get aed => isArabic ? 'درهم' : 'AED';
  String get day => isArabic ? 'يوم' : 'day';
  String get pickupReturnLocations =>
      isArabic ? 'مواقع الاستلام والإرجاع' : 'Pickup & Return Locations';
  String get deliverToMyLocation =>
      isArabic ? 'التوصيل لموقعي' : 'Deliver to my location';
  String get additionalFee => isArabic ? 'رسوم إضافية' : 'Additional Fee';
  String get emergencyContactName =>
      isArabic ? 'اسم جهة الاتصال في الطوارئ' : 'Emergency Contact Name';
  String get emergencyContactPhone =>
      isArabic ? 'هاتف جهة الاتصال في الطوارئ' : 'Emergency Contact Phone';
  String get fieldRequired =>
      isArabic ? 'هذا الحقل مطلوب' : 'This field is required';
  String get additionalDrivers =>
      isArabic ? 'سائقون إضافيون' : 'Additional Drivers';
  String get addDriver => isArabic ? 'إضافة سائق' : 'Add Driver';
  String get noAdditionalDrivers =>
      isArabic ? 'لا توجد سائقون إضافيون' : 'No additional drivers';
  String get specialRequests => isArabic ? 'طلبات خاصة' : 'Special Requests';
  String get enterSpecialRequests =>
      isArabic ? 'أدخل الطلبات الخاصة' : 'Enter special requests';
  String get additionalServices =>
      isArabic ? 'خدمات إضافية' : 'Additional Services';
  String get selectAdditionalServices =>
      isArabic ? 'اختر الخدمات الإضافية' : 'Select additional services';
  String get childSeat => isArabic ? 'مقعد الطفل' : 'Child Seat';
  String get gpsNavigation => isArabic ? 'نظام GPS' : 'GPS Navigation';
  String get additionalInsurance =>
      isArabic ? 'تأمين إضافي' : 'Additional Insurance';
  String get unlimitedMileage =>
      isArabic ? 'مسافة غير محدودة' : 'Unlimited Mileage';
  String get bookingSummary => isArabic ? 'ملخص الحجز' : 'Booking Summary';
  String get returnDate => isArabic ? 'تاريخ الإرجاع' : 'Return Date';
  String get delivery => isArabic ? 'التوصيل' : 'Delivery';

  // Booking confirmation strings
  String get bookingConfirmed =>
      isArabic ? 'تم تأكيد الحجز' : 'Booking Confirmed';
  String get bookingConfirmationMessage => isArabic
      ? 'تم تأكيد حجزك بنجاح. ستحصل على رسالة تأكيد قريباً'
      : 'Your booking has been confirmed successfully. You will receive a confirmation message soon';
  String get bookingNumber => isArabic ? 'رقم الحجز' : 'Booking Number';
  String get totalAmount => isArabic ? 'المبلغ الإجمالي' : 'Total Amount';
  String get status => isArabic ? 'الحالة' : 'Status';
  String get bookingQRCode => isArabic ? 'رمز QR للحجز' : 'Booking QR Code';
  String get showQRCodeAtPickup =>
      isArabic ? 'اعرض هذا الرمز عند الاستلام' : 'Show this QR code at pickup';
  String get nextSteps => isArabic ? 'الخطوات التالية' : 'Next Steps';
  String get step1Title => isArabic ? 'تحضير الوثائق' : 'Prepare Documents';
  String get step1Description => isArabic
      ? 'تأكد من إحضار رخصة القيادة وبطاقة الهوية'
      : 'Make sure to bring your driving license and ID card';
  String get step2Title =>
      isArabic ? 'الوصول في الوقت المحدد' : 'Arrive on Time';
  String get step2Description => isArabic
      ? 'كن في موقع الاستلام في الوقت المحدد'
      : 'Be at the pickup location at the scheduled time';
  String get step3Title => isArabic ? 'فحص السيارة' : 'Inspect the Car';
  String get step3Description => isArabic
      ? 'افحص السيارة بعناية قبل الاستلام'
      : 'Carefully inspect the car before pickup';
  String get step4Title => isArabic ? 'الاستمتاع بالرحلة' : 'Enjoy Your Trip';
  String get step4Description =>
      isArabic ? 'استمتع برحلتك وقد بأمان' : 'Enjoy your trip and drive safely';
  String get backToHome => isArabic ? 'العودة للرئيسية' : 'Back to Home';
  String get viewMyBookings => isArabic ? 'عرض حجوزاتي' : 'View My Bookings';
  String get shareBooking => isArabic ? 'مشاركة الحجز' : 'Share Booking';
  String get active => isArabic ? 'نشط' : 'Active';

  // Booking screen specific terms
  String get iAgreeToTerms => isArabic
      ? 'أوافق على الشروط والأحكام'
      : 'I agree to terms and conditions';
  String get iAcceptCancellationPolicy =>
      isArabic ? 'أوافق على سياسة الإلغاء' : 'I accept cancellation policy';
  String get deposit => isArabic ? 'تأمين' : 'Deposit';
  String get proceedToPayment =>
      isArabic ? 'متابعة الدفع' : 'Proceed to Payment';
  String get driverName => isArabic ? 'اسم السائق' : 'Driver Name';
  String get driverLicense => isArabic ? 'رخصة القيادة' : 'Driver License';
  String get add => isArabic ? 'إضافة' : 'Add';

  // Payment screen strings
  String get bookingFailed => isArabic ? 'فشل في الحجز' : 'Booking Failed';
  String get savedCards => isArabic ? 'البطاقات المحفوظة' : 'Saved Cards';
  String get creditDebitCard =>
      isArabic ? 'بطاقة ائتمان/خصم' : 'Credit/Debit Card';
  String get visa => isArabic ? 'فيزا' : 'Visa';
  String get mastercard => isArabic ? 'ماستركارد' : 'Mastercard';
  String get amex => isArabic ? 'أمريكان إكسبريس' : 'American Express';
  String get applePay => isArabic ? 'أبل باي' : 'Apple Pay';
  String get payWithApplePay =>
      isArabic ? 'ادفع باستخدام أبل باي' : 'Pay with Apple Pay';
  String get googlePay => isArabic ? 'جوجل باي' : 'Google Pay';
  String get payWithGooglePay =>
      isArabic ? 'ادفع باستخدام جوجل باي' : 'Pay with Google Pay';
  String get cardDetails => isArabic ? 'تفاصيل البطاقة' : 'Card Details';
  String get invalidCardNumber =>
      isArabic ? 'رقم البطاقة غير صحيح' : 'Invalid card number';
  String get invalidExpiryDate =>
      isArabic ? 'تاريخ انتهاء الصلاحية غير صحيح' : 'Invalid expiry date';
  String get invalidCvv => isArabic ? 'رمز الأمان غير صحيح' : 'Invalid CVV';
  String get saveCardForFuture =>
      isArabic ? 'احفظ البطاقة للمستقبل' : 'Save card for future payments';
  String get securelyStored => isArabic ? 'محفوظة بشكل آمن' : 'Securely stored';
  String get securePaymentMessage => isArabic
      ? 'جميع المدفوعات آمنة ومشفرة'
      : 'All payments are secure and encrypted';

  // Signature screen strings
  String get pleaseSignFirst =>
      isArabic ? 'يرجى التوقيع أولاً' : 'Please sign first';
  String get signatureSaveFailed =>
      isArabic ? 'فشل في حفظ التوقيع' : 'Failed to save signature';
  String get signatureSaved => isArabic ? 'تم حفظ التوقيع' : 'Signature Saved';
  String get signatureSavedSuccessfully =>
      isArabic ? 'تم حفظ التوقيع بنجاح' : 'Signature saved successfully';
  String get continue_ => isArabic ? 'المتابعة' : 'Continue';
  String get pickupSignature =>
      isArabic ? 'توقيع الاستلام' : 'Pickup Signature';
  String get returnSignature => isArabic ? 'توقيع الإرجاع' : 'Return Signature';
  String get electronicSignature =>
      isArabic ? 'التوقيع الإلكتروني' : 'Electronic Signature';
  String get signatureInstructions => isArabic
      ? 'يرجى التوقيع في المنطقة أدناه'
      : 'Please sign in the area below';
  String get signHere => isArabic ? 'وقع هنا' : 'Sign Here';
  String get signed => isArabic ? 'تم التوقيع' : 'Signed';
  String get signatureDisclaimer => isArabic
      ? 'بتوقيعي أؤكد موافقتي على شروط وأحكام الإيجار'
      : 'By signing, I confirm my agreement to the rental terms and conditions';
  String get saveSignature => isArabic ? 'حفظ التوقيع' : 'Save Signature';

  // Documents screen strings
  String get requiredDocuments =>
      isArabic ? 'المستندات المطلوبة' : 'Required Documents';
  String get documentsDescription => isArabic
      ? 'يرجى رفع المستندات التالية لتفعيل حسابك'
      : 'Please upload the following documents to activate your account';
  String get emiratesIdFront =>
      isArabic ? 'الهوية الإماراتية (الأمام)' : 'Emirates ID (Front)';
  String get emiratesIdFrontDescription => isArabic
      ? 'صورة واضحة للجهة الأمامية من الهوية الإماراتية'
      : 'Clear photo of the front side of Emirates ID';
  String get emiratesIdBack =>
      isArabic ? 'الهوية الإماراتية (الخلف)' : 'Emirates ID (Back)';
  String get emiratesIdBackDescription => isArabic
      ? 'صورة واضحة للجهة الخلفية من الهوية الإماراتية'
      : 'Clear photo of the back side of Emirates ID';
  String get drivingLicenseDescription => isArabic
      ? 'صورة واضحة لرخصة القيادة السارية'
      : 'Clear photo of valid driving license';
  String get optionalDocuments =>
      isArabic ? 'المستندات الاختيارية' : 'Optional Documents';
  String get optionalDocumentsDescription => isArabic
      ? 'يمكنك رفع هذه المستندات لتسريع عملية التحقق'
      : 'You can upload these documents to speed up verification';
  String get passportDescription =>
      isArabic ? 'صورة واضحة لجواز السفر' : 'Clear photo of passport';
  String get importantInformation =>
      isArabic ? 'معلومات مهمة' : 'Important Information';
  String get documentTip1 => isArabic
      ? 'تأكد من وضوح جميع النصوص والتفاصيل'
      : 'Ensure all text and details are clear';
  String get documentTip2 => isArabic
      ? 'تجنب الصور المظللة أو الضبابية'
      : 'Avoid shadowy or blurry photos';
  String get documentTip3 => isArabic
      ? 'يجب أن تكون المستندات سارية المفعول'
      : 'Documents must be valid and not expired';
  String get documentTip4 => isArabic
      ? 'سيتم مراجعة المستندات خلال 24-48 ساعة'
      : 'Documents will be reviewed within 24-48 hours';
  String get selectImageSource =>
      isArabic ? 'اختر مصدر الصورة' : 'Select Image Source';
  String get camera => isArabic ? 'الكاميرا' : 'Camera';
  String get gallery => isArabic ? 'المعرض' : 'Gallery';
  String get deleteDocument => isArabic ? 'حذف المستند' : 'Delete Document';
  String get deleteDocumentConfirmation => isArabic
      ? 'هل أنت متأكد من حذف هذا المستند؟'
      : 'Are you sure you want to delete this document?';
  String get documentsSubmittedForReview => isArabic
      ? 'تم إرسال المستندات للمراجعة'
      : 'Documents submitted for review';

  // Verification status strings
  String get documentsRequired =>
      isArabic ? 'المستندات مطلوبة' : 'Documents Required';
  String get verificationPending =>
      isArabic ? 'التحقق معلق' : 'Verification Pending';
  String get documentsVerified =>
      isArabic ? 'تم التحقق من المستندات' : 'Documents Verified';
  String get documentsRejected =>
      isArabic ? 'تم رفض المستندات' : 'Documents Rejected';
  String get pleaseUploadDocuments => isArabic
      ? 'يرجى رفع المستندات المطلوبة'
      : 'Please upload required documents';
  String get documentsUnderReview =>
      isArabic ? 'المستندات قيد المراجعة' : 'Documents under review';
  String get allDocumentsVerified =>
      isArabic ? 'تم التحقق من جميع المستندات' : 'All documents verified';
  String get someDocumentsRejected =>
      isArabic ? 'تم رفض بعض المستندات' : 'Some documents rejected';

  // Additional strings
  String get submitFailed => isArabic ? 'فشل في الإرسال' : 'Submit Failed';

  // Document upload card strings
  String get imageLoadError =>
      isArabic ? 'خطأ في تحميل الصورة' : 'Image load error';
  String get uploaded => isArabic ? 'تم الرفع' : 'Uploaded';
  String get tapToUpload => isArabic ? 'اضغط للرفع' : 'Tap to Upload';
  String get supportedFormats => isArabic ? 'JPG, PNG, PDF' : 'JPG, PNG, PDF';
  String get uploading => isArabic ? 'جاري الرفع...' : 'Uploading...';
  String get replace => isArabic ? 'استبدال' : 'Replace';

  // Verification status card strings
  String get verificationStatus =>
      isArabic ? 'حالة التحقق' : 'Verification Status';
  String get adminNotes => isArabic ? 'ملاحظات الإدارة' : 'Admin Notes';
  String get verificationProgress =>
      isArabic ? 'تقدم التحقق' : 'Verification Progress';
  String get verified => isArabic ? 'تم التحقق' : 'Verified';
  String get rejected => isArabic ? 'مرفوض' : 'Rejected';
  String get underReview => isArabic ? 'قيد المراجعة' : 'Under Review';
  String get notSubmitted => isArabic ? 'لم يتم الإرسال' : 'Not Submitted';
  String get documentsVerifiedDescription => isArabic
      ? 'تم التحقق من جميع مستنداتك بنجاح. يمكنك الآن استئجار المركبات.'
      : 'All your documents have been successfully verified. You can now rent vehicles.';
  String get documentsRejectedDescription => isArabic
      ? 'تم رفض بعض مستنداتك. يرجى مراجعة الملاحظات وإعادة الإرسال.'
      : 'Some of your documents have been rejected. Please review the notes and resubmit.';
  String get documentsUnderReviewDescription => isArabic
      ? 'مستنداتك قيد المراجعة من قبل فريقنا. سنقوم بإشعارك بالنتيجة.'
      : 'Your documents are being reviewed by our team. We will notify you of the result.';
  String get documentsNotSubmittedDescription => isArabic
      ? 'لم يتم إرسال مستنداتك بعد. يرجى رفع المستندات المطلوبة للتحقق.'
      : 'Your documents have not been submitted yet. Please upload the required documents for verification.';
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
