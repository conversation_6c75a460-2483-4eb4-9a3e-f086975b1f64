import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
import '../services/storage_service.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  final SupabaseClient _supabase = Supabase.instance.client;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
  );
  final UserService _userService = UserService();
  final StorageService _storageService = StorageService();

  AuthState _state = AuthState.initial;
  UserModel? _currentUser;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthState get state => _state;
  UserModel? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated =>
      _state == AuthState.authenticated && _currentUser != null;
  User? get supabaseUser => _supabase.auth.currentUser;

  AuthProvider() {
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    try {
      _setState(AuthState.loading);

      // Listen to auth state changes
      _supabase.auth.onAuthStateChange.listen((data) {
        _handleAuthStateChange(data.event, data.session);
      });

      // Check if user is already logged in
      final session = _supabase.auth.currentSession;
      if (session != null) {
        await _loadCurrentUser();
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      _setError('Failed to initialize auth: ${e.toString()}');
    }
  }

  Future<void> _handleAuthStateChange(
      AuthChangeEvent event, Session? session) async {
    switch (event) {
      case AuthChangeEvent.signedIn:
        if (session != null) {
          await _loadCurrentUser();
        }
        break;
      case AuthChangeEvent.signedOut:
        _currentUser = null;
        _setState(AuthState.unauthenticated);
        break;
      case AuthChangeEvent.userUpdated:
        if (session != null) {
          await _loadCurrentUser();
        }
        break;
      default:
        break;
    }
  }

  Future<void> _loadCurrentUser() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user != null) {
        print('Loading user profile for: ${user.id}');

        try {
          final userModel = await _userService.getUserById(user.id);
          if (userModel != null) {
            _currentUser = userModel;
            _setState(AuthState.authenticated);
            print('User profile loaded successfully: ${userModel.email}');
            return;
          }
        } catch (e) {
          print('Error fetching user profile from database: $e');
        }

        // إذا فشل جلب البيانات من قاعدة البيانات، إنشاء مستخدم مؤقت من بيانات Supabase Auth
        print('Creating temporary user from Supabase Auth data...');
        _currentUser = UserModel.fromSupabaseUser(user);
        _setState(AuthState.authenticated);
        print('Temporary user profile created: ${user.email}');

        // محاولة إنشاء ملف تعريف في قاعدة البيانات في الخلفية
        _createUserProfileInBackground(user);
      }
    } catch (e) {
      print('Error loading user: $e');
      // حتى في حالة الخطأ، إنشاء مستخدم مؤقت إذا كان هناك جلسة صالحة
      final user = _supabase.auth.currentUser;
      if (user != null) {
        _currentUser = UserModel.fromSupabaseUser(user);
        _setState(AuthState.authenticated);
        print('Fallback: Created temporary user from auth session');
      } else {
        _setError('Failed to load user: ${e.toString()}');
      }
    }
  }

  // إنشاء ملف المستخدم في الخلفية دون إيقاف التطبيق
  Future<void> _createUserProfileInBackground(User user) async {
    try {
      print('Attempting to create user profile in background...');
      final newUser = await _userService.createUserProfile(
        userId: user.id,
        email: user.email ?? '',
        fullName: user.userMetadata?['full_name'] ??
            user.email?.split('@').first ??
            'المستخدم',
      );

      // تحديث المستخدم الحالي بالبيانات من قاعدة البيانات
      _currentUser = newUser;
      notifyListeners();
      print(
          'User profile created successfully in background: ${newUser.email}');
    } catch (e) {
      print('Failed to create user profile in background: $e');
      // لا نرمي خطأ هنا لأن المستخدم يعمل بالفعل مع البيانات المؤقتة
    }
  }

  // Sign in with email and password
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await _supabase.auth.signInWithPassword(
        email: email.trim(),
        password: password,
      );

      if (response.user != null) {
        await _loadCurrentUser();
        return true;
      }

      _setError('Failed to sign in');
      return false;
    } on AuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      _setError('An unexpected error occurred: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Alternative method name for compatibility
  Future<bool> signInWithEmail(String email, String password) async {
    return await signInWithEmailAndPassword(email, password);
  }

  // Sign up with email and password
  Future<bool> signUpWithEmailAndPassword(
    String email,
    String password,
    String fullName,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      print('Attempting to sign up user: $email');

      final response = await _supabase.auth.signUp(
        email: email.trim(),
        password: password,
        data: {
          'full_name': fullName.trim(),
        },
      );

      print('Supabase auth signup response: ${response.user?.id}');

      if (response.user != null) {
        // Create user profile
        print('Creating user profile...');
        await _userService.createUserProfile(
          userId: response.user!.id,
          email: email.trim(),
          fullName: fullName.trim(),
        );

        print('User registered successfully!');
        return true;
      }

      _setError('Failed to create account');
      return false;
    } on AuthException catch (e) {
      print('Auth exception during signup: ${e.message}');
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      print('Unexpected error during signup: $e');
      _setError('An unexpected error occurred: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Alternative method name for compatibility
  Future<bool> signUpWithEmail(String email, String password, String fullName,
      String phoneNumber) async {
    return await signUpWithEmailAndPassword(email, password, fullName);
  }

  // Sign in with Google
  Future<bool> signInWithGoogle() async {
    try {
      _setLoading(true);
      _clearError();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        _setError('Google sign in was cancelled');
        return false;
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final String? accessToken = googleAuth.accessToken;
      final String? idToken = googleAuth.idToken;

      if (accessToken == null || idToken == null) {
        _setError('Failed to get Google authentication tokens');
        return false;
      }

      final response = await _supabase.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: idToken,
        accessToken: accessToken,
      );

      if (response.user != null) {
        // Check if user profile exists, if not create one
        try {
          await _loadCurrentUser();
        } catch (e) {
          // User profile doesn't exist, create it
          await _userService.createUserProfile(
            userId: response.user!.id,
            email: response.user!.email!,
            fullName: googleUser.displayName ?? '',
            profileImageUrl: googleUser.photoUrl,
          );
          await _loadCurrentUser();
        }
        return true;
      }

      _setError('Failed to sign in with Google');
      return false;
    } catch (e) {
      _setError('Google sign in failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _googleSignIn.signOut();
      await _supabase.auth.signOut();
      _currentUser = null;
      _setState(AuthState.unauthenticated);
    } catch (e) {
      _setError('Failed to sign out: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await _supabase.auth.resetPasswordForEmail(email.trim());
      return true;
    } on AuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to reset password: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateUserProfile(UserModel updatedUser) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _userService.updateUserProfile(updatedUser);
      _currentUser = user;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Upload profile image
  Future<bool> updateProfileImage(Uint8List imageBytes, String fileName) async {
    try {
      _setLoading(true);
      _clearError();

      if (_currentUser == null) {
        _setError('No user logged in');
        return false;
      }

      final imageUrl = await _storageService.uploadUserAvatar(
        userId: _currentUser!.id,
        imageBytes: imageBytes,
        fileName: fileName,
      );

      final updatedUser = _currentUser!.copyWith(profileImageUrl: imageUrl);
      final success = await updateUserProfile(updatedUser);

      return success;
    } catch (e) {
      _setError('Failed to upload profile image: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Upload license image
  Future<bool> updateLicenseImage(Uint8List imageBytes, String fileName) async {
    try {
      _setLoading(true);
      _clearError();

      if (_currentUser == null) {
        _setError('No user logged in');
        return false;
      }

      final imageUrl = await _storageService.uploadDocument(
        userId: _currentUser!.id,
        imageBytes: imageBytes,
        fileName: fileName,
        documentType: 'license',
      );

      final updatedUser = _currentUser!.copyWith(licenseImageUrl: imageUrl);
      final success = await updateUserProfile(updatedUser);

      return success;
    } catch (e) {
      _setError('Failed to upload license image: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Upload any document
  Future<bool> uploadDocument(
    Uint8List documentBytes,
    String fileName,
    String documentType,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      if (_currentUser == null) {
        _setError('No user logged in');
        return false;
      }

      final documentUrl = await _storageService.uploadDocument(
        userId: _currentUser!.id,
        imageBytes: documentBytes,
        fileName: fileName,
        documentType: documentType,
      );

      // Update user model based on document type
      UserModel updatedUser;
      switch (documentType) {
        case 'emirates_id_front':
          updatedUser = _currentUser!.copyWith(emiratesIdFrontUrl: documentUrl);
          break;
        case 'emirates_id_back':
          updatedUser = _currentUser!.copyWith(emiratesIdBackUrl: documentUrl);
          break;
        case 'driving_license':
          updatedUser = _currentUser!.copyWith(drivingLicenseUrl: documentUrl);
          break;
        case 'passport':
          updatedUser = _currentUser!.copyWith(passportUrl: documentUrl);
          break;
        case 'signature':
          updatedUser = _currentUser!.copyWith(signatureUrl: documentUrl);
          break;
        default:
          throw Exception('Unknown document type: $documentType');
      }

      final success = await updateUserProfile(updatedUser);
      return success;
    } catch (e) {
      _setError('Failed to upload document: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<bool> changePassword(String newPassword) async {
    try {
      _setLoading(true);
      _clearError();

      await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      return true;
    } on AuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to change password: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete account
  Future<bool> deleteAccount() async {
    try {
      _setLoading(true);
      _clearError();

      if (_currentUser != null) {
        await _userService.deleteUserAccount(_currentUser!.id);
      }

      await signOut();
      return true;
    } catch (e) {
      _setError('Failed to delete account: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Verify email
  Future<bool> resendVerificationEmail() async {
    try {
      _setLoading(true);
      _clearError();

      if (_currentUser?.email != null) {
        await _supabase.auth.resend(
          type: OtpType.email,
          email: _currentUser!.email,
        );
        return true;
      }

      _setError('No email found for current user');
      return false;
    } catch (e) {
      _setError('Failed to resend verification email: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _setState(AuthState.error);
  }

  void _clearError() {
    _errorMessage = null;
  }

  String _getAuthErrorMessage(AuthException exception) {
    switch (exception.message) {
      case 'Invalid login credentials':
        return 'Invalid email or password';
      case 'User not found':
        return 'No account found with this email';
      case 'Invalid email':
        return 'Please enter a valid email address';
      case 'Weak password':
        return 'Password should be at least 6 characters';
      case 'Email already registered':
        return 'An account with this email already exists';
      case 'Too many requests':
        return 'Too many attempts. Please try again later';
      default:
        return exception.message;
    }
  }
}
