import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../../models/car.dart';
import '../../providers/car_provider.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../../widgets/image_gallery.dart';
import '../../widgets/feature_chip.dart';
import '../../widgets/rating_stars.dart';
import '../../widgets/price_breakdown.dart';
import '../booking/booking_screen.dart';
import '../reviews/reviews_screen.dart';

class CarDetailsScreen extends StatefulWidget {
  final Car car;

  const CarDetailsScreen({
    Key? key,
    required this.car,
  }) : super(key: key);

  @override
  _CarDetailsScreenState createState() => _CarDetailsScreenState();
}

class _CarDetailsScreenState extends State<CarDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _isAppBarExpanded = true;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController();

    _scrollController.addListener(() {
      setState(() {
        _isAppBarExpanded = _scrollController.offset < 200;
      });
    });

    _checkFavoriteStatus();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _checkFavoriteStatus() async {
    // التحقق من حالة المفضلة
    final carProvider = Provider.of<CarProvider>(context, listen: false);
    final isFav = await carProvider.isCarFavorite(widget.car.id);
    setState(() {
      _isFavorite = isFav;
    });
  }

  void _toggleFavorite() async {
    final carProvider = Provider.of<CarProvider>(context, listen: false);
    final success = await carProvider.toggleFavorite(widget.car.id);

    if (success) {
      setState(() {
        _isFavorite = !_isFavorite;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isFavorite
                ? AppLocalizations.of(context)!.addedToFavorites
                : AppLocalizations.of(context)!.removedFromFavorites,
          ),
          backgroundColor: AppTheme.successGreen,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  void _shareCarDetails() {
    // مشاركة تفاصيل السيارة
    // يمكن تنفيذ هذا باستخدام share_plus package
  }

  void _bookCar() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isAuthenticated) {
      _showLoginDialog();
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BookingScreen(car: widget.car),
      ),
    );
  }

  void _showLoginDialog() {
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.loginRequired),
        content: Text(l10n.loginRequiredMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushNamed('/login');
            },
            child: Text(l10n.login),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // شريط التطبيق المرن مع معرض الصور
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.white,
            foregroundColor:
                _isAppBarExpanded ? Colors.white : AppTheme.darkGrey,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Icon(
                    _isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: _isFavorite ? Colors.red : Colors.white,
                  ),
                  onPressed: _toggleFavorite,
                ),
              ),
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: _shareCarDetails,
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: ImageGallery(
                images: widget.car.images,
              ),
            ),
          ),

          // محتوى التفاصيل
          SliverToBoxAdapter(
            child: Column(
              children: [
                // معلومات السيارة الأساسية
                _buildCarBasicInfo(l10n),

                // التبويبات
                _buildTabBar(l10n),

                // محتوى التبويبات
                _buildTabContent(l10n),
              ],
            ),
          ),
        ],
      ),

      // شريط الحجز السفلي
      bottomNavigationBar: _buildBookingBar(l10n),
    );
  }

  Widget _buildCarBasicInfo(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // اسم السيارة والتقييم
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.car.name,
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.car.brand} ${widget.car.model} ${widget.car.year}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: AppTheme.mediumGrey,
                          ),
                    ),
                  ],
                ),
              ),

              // التقييم
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  RatingStars(
                    rating: widget.car.rating,
                    size: 16,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.car.rating} (${widget.car.totalRatings})',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.mediumGrey,
                        ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // المواصفات الأساسية
          Row(
            children: [
              _buildSpecItem(Icons.people, '${widget.car.seats} ${l10n.seats}'),
              const SizedBox(width: 20),
              _buildSpecItem(Icons.local_gas_station, widget.car.fuelType),
              const SizedBox(width: 20),
              _buildSpecItem(Icons.settings, widget.car.transmission),
            ],
          ),

          const SizedBox(height: 16),

          // السعر
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.car.dailyPrice.toStringAsFixed(0)} ${l10n.aedPerDay}',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.royalBlue,
                        ),
                  ),
                  Text(
                    l10n.excludingTaxes,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.mediumGrey,
                        ),
                  ),
                ],
              ),

              // حالة التوفر
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: widget.car.isAvailable
                      ? AppTheme.successGreen.withOpacity(0.1)
                      : AppTheme.errorRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.car.isAvailable
                          ? Icons.check_circle
                          : Icons.cancel,
                      size: 16,
                      color: widget.car.isAvailable
                          ? AppTheme.successGreen
                          : AppTheme.errorRed,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.car.isAvailable
                          ? l10n.available
                          : l10n.unavailable,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: widget.car.isAvailable
                            ? AppTheme.successGreen
                            : AppTheme.errorRed,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSpecItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.mediumGrey,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.mediumGrey,
              ),
        ),
      ],
    );
  }

  Widget _buildTabBar(AppLocalizations l10n) {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.royalBlue,
        unselectedLabelColor: AppTheme.mediumGrey,
        indicatorColor: AppTheme.royalBlue,
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 14,
        ),
        tabs: [
          Tab(text: l10n.overview),
          Tab(text: l10n.features),
          Tab(text: l10n.location),
          Tab(text: l10n.reviews),
        ],
      ),
    );
  }

  Widget _buildTabContent(AppLocalizations l10n) {
    return Container(
      height: 400,
      color: Colors.white,
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(l10n),
          _buildFeaturesTab(l10n),
          _buildLocationTab(l10n),
          _buildReviewsTab(l10n),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الوصف
          Text(
            l10n.description,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.car.description.isNotEmpty
                ? widget.car.description
                : l10n.noDescriptionAvailable,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.5,
                ),
          ),

          const SizedBox(height: 20),

          // المواصفات التفصيلية
          Text(
            l10n.specifications,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),

          _buildSpecificationRow(l10n.brand, widget.car.brand),
          _buildSpecificationRow(l10n.model, widget.car.model),
          _buildSpecificationRow(l10n.year, widget.car.year.toString()),
          _buildSpecificationRow(l10n.type, widget.car.type),
          _buildSpecificationRow(l10n.transmission, widget.car.transmission),
          _buildSpecificationRow(l10n.fuelType, widget.car.fuelType),
          _buildSpecificationRow(l10n.doors, '${widget.car.doors}'),
          _buildSpecificationRow(l10n.seats, '${widget.car.seats}'),
          _buildSpecificationRow(
              l10n.color,
              widget.car.color.isNotEmpty
                  ? widget.car.color
                  : l10n.notSpecified),

          const SizedBox(height: 20),

          // تفاصيل الأسعار
          Text(
            l10n.pricing,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),

          PriceBreakdown(
            basePrice: widget.car.dailyPrice,
            taxes: widget.car.dailyPrice * 0.05, // 5% tax
            serviceFee: 25.0, // Fixed service fee
            rentalDays: 1, // Default to 1 day
          ),
        ],
      ),
    );
  }

  Widget _buildSpecificationRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.mediumGrey,
                ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesTab(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // المميزات العامة
          if (widget.car.features.isNotEmpty) ...[
            Text(
              l10n.generalFeatures,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.car.features.map((feature) {
                return FeatureChip(
                  text: feature,
                  icon: _getFeatureIcon(feature),
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
          ],

          // مميزات الأمان
          if (widget.car.safetyFeatures.isNotEmpty) ...[
            Text(
              l10n.safetyFeatures,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.car.safetyFeatures.map((feature) {
                return FeatureChip(
                  text: feature,
                  icon: Icons.security,
                  backgroundColor: AppTheme.successGreen.withOpacity(0.1),
                  textColor: AppTheme.successGreen,
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
          ],

          // مميزات الراحة
          if (widget.car.comfortFeatures.isNotEmpty) ...[
            Text(
              l10n.comfortFeatures,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.car.comfortFeatures.map((feature) {
                return FeatureChip(
                  text: feature,
                  icon: Icons.airline_seat_recline_extra,
                  backgroundColor: AppTheme.lightBlue.withOpacity(0.1),
                  textColor: AppTheme.royalBlue,
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLocationTab(AppLocalizations l10n) {
    return Column(
      children: [
        // الخريطة
        Expanded(
          flex: 2,
          child: Container(
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: FlutterMap(
                options: MapOptions(
                  initialCenter:
                      LatLng(widget.car.locationLat, widget.car.locationLng),
                  initialZoom: 15,
                  minZoom: 10,
                  maxZoom: 18,
                ),
                children: [
                  TileLayer(
                    urlTemplate:
                        'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                    userAgentPackageName: 'com.dubaicarrental.app',
                  ),
                  MarkerLayer(
                    markers: [
                      Marker(
                        point: LatLng(
                            widget.car.locationLat, widget.car.locationLng),
                        width: 40,
                        height: 40,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.directions_car,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),

        // معلومات الموقع
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.pickupLocation,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: AppTheme.royalBlue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        widget.car.locationAddress,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // مواقع الاستلام المتاحة
                if (widget.car.pickupLocations.isNotEmpty) ...[
                  Text(
                    l10n.availablePickupLocations,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  ...widget.car.pickupLocations.map((location) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: AppTheme.successGreen,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            location,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReviewsTab(AppLocalizations l10n) {
    return Column(
      children: [
        // ملخص التقييمات
        Container(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // التقييم العام
              Column(
                children: [
                  Text(
                    widget.car.rating.toStringAsFixed(1),
                    style: Theme.of(context).textTheme.displaySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.royalBlue,
                        ),
                  ),
                  RatingStars(rating: widget.car.rating),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.car.totalRatings} ${l10n.reviews}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.mediumGrey,
                        ),
                  ),
                ],
              ),

              const SizedBox(width: 30),

              // توزيع التقييمات
              Expanded(
                child: Column(
                  children: [
                    _buildRatingBar(5, 0.7),
                    _buildRatingBar(4, 0.2),
                    _buildRatingBar(3, 0.05),
                    _buildRatingBar(2, 0.03),
                    _buildRatingBar(1, 0.02),
                  ],
                ),
              ),
            ],
          ),
        ),

        const Divider(),

        // قائمة التقييمات
        Expanded(
          child: widget.car.totalRatings > 0
              ? ListView.builder(
                  padding: const EdgeInsets.all(20),
                  itemCount: 3, // عرض 3 تقييمات كمثال
                  itemBuilder: (context, index) {
                    return _buildReviewItem(l10n, index);
                  },
                )
              : Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.rate_review_outlined,
                        size: 64,
                        color: AppTheme.mediumGrey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        l10n.noReviews,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppTheme.mediumGrey,
                                ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        l10n.beFirstToReview,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.mediumGrey,
                            ),
                      ),
                    ],
                  ),
                ),
        ),

        // زر عرض جميع التقييمات
        if (widget.car.totalRatings > 3)
          Padding(
            padding: const EdgeInsets.all(20),
            child: OutlinedButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => ReviewsScreen(carId: widget.car.id),
                  ),
                );
              },
              child: Text(l10n.viewAllReviews),
            ),
          ),
      ],
    );
  }

  Widget _buildRatingBar(int stars, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$stars',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(width: 8),
          Icon(
            Icons.star,
            size: 12,
            color: AppTheme.warningOrange,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: AppTheme.lightGrey,
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.warningOrange),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(percentage * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.mediumGrey,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewItem(AppLocalizations l10n, int index) {
    // بيانات تجريبية للتقييمات
    final reviewData = [
      {
        'name': 'أحمد محمد',
        'rating': 5.0,
        'date': '2024-01-15',
        'comment': 'سيارة ممتازة وخدمة رائعة. أنصح بها بشدة!',
      },
      {
        'name': 'Sarah Johnson',
        'rating': 4.0,
        'date': '2024-01-10',
        'comment':
            'Great car, very clean and comfortable. Good value for money.',
      },
      {
        'name': 'محمد علي',
        'rating': 5.0,
        'date': '2024-01-05',
        'comment': 'تجربة رائعة، السيارة نظيفة والخدمة ممتازة.',
      },
    ];

    final review = reviewData[index];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.lightGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: AppTheme.royalBlue,
                child: Text(
                  review['name'].toString().substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review['name'].toString(),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Row(
                      children: [
                        RatingStars(
                          rating: review['rating'] as double,
                          size: 14,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          review['date'].toString(),
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.mediumGrey,
                                  ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            review['comment'].toString(),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildBookingBar(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // السعر
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.car.dailyPrice.toStringAsFixed(0)} ${l10n.aedPerDay}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.royalBlue,
                        ),
                  ),
                  Text(
                    l10n.excludingTaxes,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.mediumGrey,
                        ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // زر الحجز
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: widget.car.isAvailable ? _bookCar : null,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  widget.car.isAvailable ? l10n.bookNow : l10n.unavailable,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getFeatureIcon(String feature) {
    switch (feature.toLowerCase()) {
      case 'gps':
      case 'navigation':
        return Icons.navigation;
      case 'bluetooth':
        return Icons.bluetooth;
      case 'usb':
        return Icons.usb;
      case 'air conditioning':
      case 'ac':
        return Icons.ac_unit;
      case 'leather seats':
        return Icons.airline_seat_legroom_extra;
      case 'sunroof':
        return Icons.wb_sunny;
      case 'backup camera':
        return Icons.videocam;
      default:
        return Icons.check_circle;
    }
  }
}
