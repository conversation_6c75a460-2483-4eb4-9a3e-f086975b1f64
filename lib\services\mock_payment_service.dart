import 'package:flutter/material.dart';

/// خدمة دفع محاكية للتطوير والاختبار
/// تحاكي عملية الدفع بدون الحاجة لمفاتيح Stripe حقيقية
class MockPaymentService {
  /// محاكاة معالجة الدفع
  static Future<Map<String, dynamic>> processPayment({
    required BuildContext context,
    required double amount,
    required String currency,
    required String description,
  }) async {
    // إظهار حوار الدفع المحاكي
    return await showDialog<Map<String, dynamic>>(
          context: context,
          barrierDismissible: false,
          builder: (context) => _MockPaymentDialog(
            amount: amount,
            currency: currency,
            description: description,
          ),
        ) ??
        {'success': false, 'error': 'تم إلغاء العملية'};
  }
}

class _MockPaymentDialog extends StatefulWidget {
  final double amount;
  final String currency;
  final String description;

  const _MockPaymentDialog({
    required this.amount,
    required this.currency,
    required this.description,
  });

  @override
  State<_MockPaymentDialog> createState() => _MockPaymentDialogState();
}

class _MockPaymentDialogState extends State<_MockPaymentDialog> {
  bool _isProcessing = false;
  final _cardNumberController = TextEditingController(text: '****************');
  final _expiryController = TextEditingController(text: '12/25');
  final _cvvController = TextEditingController(text: '123');

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryController.dispose();
    _cvvController.dispose();
    super.dispose();
  }

  Future<void> _simulatePayment() async {
    setState(() {
      _isProcessing = true;
    });

    // محاكاة وقت المعالجة
    await Future.delayed(Duration(seconds: 2));

    // محاكاة نتيجة الدفع بناءً على رقم البطاقة
    final cardNumber = _cardNumberController.text;
    Map<String, dynamic> result;

    if (cardNumber.contains('****************')) {
      // نجح الدفع
      result = {
        'success': true,
        'paymentId': 'pi_mock_${DateTime.now().millisecondsSinceEpoch}',
        'amount': widget.amount,
        'currency': widget.currency,
        'message': 'تم الدفع بنجاح!',
      };
    } else if (cardNumber.contains('****************')) {
      // فشل الدفع
      result = {
        'success': false,
        'error': 'تم رفض البطاقة',
        'message': 'فشل في عملية الدفع',
      };
    } else {
      // نجح افتراضي
      result = {
        'success': true,
        'paymentId': 'pi_mock_${DateTime.now().millisecondsSinceEpoch}',
        'amount': widget.amount,
        'currency': widget.currency,
        'message': 'تم الدفع بنجاح!',
      };
    }

    if (mounted) {
      Navigator.of(context).pop(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.payment, color: Colors.blue),
          SizedBox(width: 8),
          Text('محاكي الدفع - للتطوير'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات المبلغ
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    widget.description,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '${widget.amount.toStringAsFixed(2)} ${widget.currency.toUpperCase()}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),

            if (!_isProcessing) ...[
              // حقول البطاقة المحاكية
              TextField(
                controller: _cardNumberController,
                decoration: InputDecoration(
                  labelText: 'رقم البطاقة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.credit_card),
                ),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _expiryController,
                      decoration: InputDecoration(
                        labelText: 'MM/YY',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      controller: _cvvController,
                      decoration: InputDecoration(
                        labelText: 'CVV',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),

              // ملاحظة للمطور
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  border: Border.all(color: Colors.orange[200]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'بطاقات الاختبار:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text('**************** - نجح'),
                    Text('**************** - فشل'),
                  ],
                ),
              ),
            ] else ...[
              // مؤشر التحميل
              Column(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري معالجة الدفع...'),
                ],
              ),
            ],
          ],
        ),
      ),
      actions: _isProcessing
          ? []
          : [
              TextButton(
                onPressed: () => Navigator.of(context)
                    .pop({'success': false, 'error': 'تم إلغاء العملية'}),
                child: Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: _simulatePayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                ),
                child: Text(
                  'ادفع الآن',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
    );
  }
}
