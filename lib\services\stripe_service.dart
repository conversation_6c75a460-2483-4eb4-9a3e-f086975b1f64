import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;
import '../config/stripe_config.dart';

class StripeService {
  static const String _baseUrl = 'https://api.stripe.com/v1';

  static Future<void> init() async {
    Stripe.publishableKey = StripeConfig.publishableKey;
    await Stripe.instance.applySettings();
  }

  // إنشاء Payment Intent
  static Future<Map<String, dynamic>> createPaymentIntent({
    required String amount, // المبلغ بالفلس (درهم * 100)
    required String currency,
    String description = 'حجز سيارة في دبي',
  }) async {
    try {
      final url = Uri.parse('$_baseUrl/payment_intents');

      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer ${StripeConfig.secretKey}',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'amount': amount,
          'currency': currency,
          'description': description,
          'payment_method_types[]': 'card',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('فشل في إنشاء عملية الدفع: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // معالجة الدفع
  static Future<bool> processPayment({
    required BuildContext context,
    required double amount, // المبلغ بالدرهم
    required String carName,
    required String bookingId,
  }) async {
    try {
      // تحويل المبلغ إلى فلس (ضرب في 100)
      final amountInFils = (amount * 100).toInt().toString();

      // إنشاء Payment Intent
      final paymentIntent = await createPaymentIntent(
        amount: amountInFils,
        currency: 'aed', // درهم إماراتي
        description: 'حجز $carName - رقم الحجز: $bookingId',
      );

      // تهيئة Payment Sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentIntent['client_secret'],
          merchantDisplayName: 'دبي لتأجير السيارات',
          customerId: paymentIntent['customer'],
          customerEphemeralKeySecret: paymentIntent['ephemeral_key'],
          style: ThemeMode.light,
          billingDetails: const BillingDetails(
            address: Address(
              country: 'AE',
              city: 'Dubai',
              line1: 'Dubai',
              line2: '',
              postalCode: '00000',
              state: 'Dubai',
            ),
          ),
        ),
      );

      // عرض Payment Sheet
      await Stripe.instance.presentPaymentSheet();

      // إذا وصل هنا، فقد تم الدفع بنجاح
      return true;
    } catch (e) {
      debugPrint('خطأ في معالجة الدفع: $e');

      // عرض رسالة خطأ مناسبة
      if (!context.mounted) return false;

      String errorMessage = 'حدث خطأ في عملية الدفع';

      if (e is StripeException) {
        switch (e.error.code) {
          case FailureCode.Canceled:
            errorMessage = 'تم إلغاء عملية الدفع';
            break;
          case FailureCode.Failed:
            errorMessage = 'فشلت عملية الدفع، حاول مرة أخرى';
            break;
          default:
            errorMessage = 'خطأ في الدفع: ${e.error.localizedMessage}';
        }
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );

      return false;
    }
  }

  // التحقق من حالة الدفع
  static Future<bool> verifyPayment(String paymentIntentId) async {
    try {
      final url = Uri.parse('$_baseUrl/payment_intents/$paymentIntentId');

      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer ${StripeConfig.secretKey}',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final paymentIntent = json.decode(response.body);
        return paymentIntent['status'] == 'succeeded';
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من الدفع: $e');
      return false;
    }
  }

  // إنشاء رمز مميز للدفع (للاختبار)
  static Future<PaymentMethod> createTestPaymentMethod() async {
    return await Stripe.instance.createPaymentMethod(
      params: const PaymentMethodParams.card(
        paymentMethodData: PaymentMethodData(),
      ),
    );
  }
}
