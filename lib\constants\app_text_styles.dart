import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Font Families
  static String get arabicFontFamily => 'Cairo';
  static String get englishFontFamily => 'Roboto';

  // Base Text Styles for Arabic
  static TextStyle get arabicHeadline1 => GoogleFonts.cairo(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.onSurface,
    height: 1.2,
  );

  static TextStyle get arabicHeadline2 => GoogleFonts.cairo(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.onSurface,
    height: 1.3,
  );

  static TextStyle get arabicHeadline3 => GoogleFonts.cairo(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.onSurface,
    height: 1.3,
  );

  static TextStyle get arabicHeadline4 => GoogleFonts.cairo(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get arabicHeadline5 => GoogleFonts.cairo(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get arabicHeadline6 => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get arabicBodyLarge => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurface,
    height: 1.5,
  );

  static TextStyle get arabicBodyMedium => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurface,
    height: 1.5,
  );

  static TextStyle get arabicBodySmall => GoogleFonts.cairo(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurfaceVariant,
    height: 1.5,
  );

  static TextStyle get arabicLabelLarge => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get arabicLabelMedium => GoogleFonts.cairo(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get arabicLabelSmall => GoogleFonts.cairo(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurfaceVariant,
    height: 1.4,
  );

  // Base Text Styles for English
  static TextStyle get englishHeadline1 => GoogleFonts.roboto(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.onSurface,
    height: 1.2,
  );

  static TextStyle get englishHeadline2 => GoogleFonts.roboto(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.onSurface,
    height: 1.3,
  );

  static TextStyle get englishHeadline3 => GoogleFonts.roboto(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.onSurface,
    height: 1.3,
  );

  static TextStyle get englishHeadline4 => GoogleFonts.roboto(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get englishHeadline5 => GoogleFonts.roboto(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get englishHeadline6 => GoogleFonts.roboto(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get englishBodyLarge => GoogleFonts.roboto(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurface,
    height: 1.5,
  );

  static TextStyle get englishBodyMedium => GoogleFonts.roboto(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurface,
    height: 1.5,
  );

  static TextStyle get englishBodySmall => GoogleFonts.roboto(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurfaceVariant,
    height: 1.5,
  );

  static TextStyle get englishLabelLarge => GoogleFonts.roboto(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get englishLabelMedium => GoogleFonts.roboto(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    height: 1.4,
  );

  static TextStyle get englishLabelSmall => GoogleFonts.roboto(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurfaceVariant,
    height: 1.4,
  );

  // Special Text Styles
  static TextStyle get priceStyle => GoogleFonts.roboto(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );

  static TextStyle get priceCurrency => GoogleFonts.roboto(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.primary,
  );

  static TextStyle get discountPrice => GoogleFonts.roboto(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurfaceVariant,
    decoration: TextDecoration.lineThrough,
  );

  static TextStyle get buttonText => GoogleFonts.roboto(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );

  static TextStyle get buttonTextSmall => GoogleFonts.roboto(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.white,
  );

  static TextStyle get linkText => GoogleFonts.roboto(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.primary,
    decoration: TextDecoration.underline,
  );

  static TextStyle get captionText => GoogleFonts.roboto(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurfaceVariant,
    fontStyle: FontStyle.italic,
  );

  static TextStyle get errorText => GoogleFonts.roboto(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.error,
  );

  static TextStyle get successText => GoogleFonts.roboto(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.success,
  );

  static TextStyle get warningText => GoogleFonts.roboto(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.warning,
  );

  // Helper method to get style based on locale
  static TextStyle getLocalizedHeadline1(String locale) {
    return locale.startsWith('ar') ? arabicHeadline1 : englishHeadline1;
  }

  static TextStyle getLocalizedHeadline2(String locale) {
    return locale.startsWith('ar') ? arabicHeadline2 : englishHeadline2;
  }

  static TextStyle getLocalizedHeadline3(String locale) {
    return locale.startsWith('ar') ? arabicHeadline3 : englishHeadline3;
  }

  static TextStyle getLocalizedHeadline4(String locale) {
    return locale.startsWith('ar') ? arabicHeadline4 : englishHeadline4;
  }

  static TextStyle getLocalizedHeadline5(String locale) {
    return locale.startsWith('ar') ? arabicHeadline5 : englishHeadline5;
  }

  static TextStyle getLocalizedHeadline6(String locale) {
    return locale.startsWith('ar') ? arabicHeadline6 : englishHeadline6;
  }

  static TextStyle getLocalizedBodyLarge(String locale) {
    return locale.startsWith('ar') ? arabicBodyLarge : englishBodyLarge;
  }

  static TextStyle getLocalizedBodyMedium(String locale) {
    return locale.startsWith('ar') ? arabicBodyMedium : englishBodyMedium;
  }

  static TextStyle getLocalizedBodySmall(String locale) {
    return locale.startsWith('ar') ? arabicBodySmall : englishBodySmall;
  }

  static TextStyle getLocalizedLabelLarge(String locale) {
    return locale.startsWith('ar') ? arabicLabelLarge : englishLabelLarge;
  }

  static TextStyle getLocalizedLabelMedium(String locale) {
    return locale.startsWith('ar') ? arabicLabelMedium : englishLabelMedium;
  }

  static TextStyle getLocalizedLabelSmall(String locale) {
    return locale.startsWith('ar') ? arabicLabelSmall : englishLabelSmall;
  }
}
