-- إضافة جدول تمديدات الإيجار
-- يتم تشغيل هذا السكريبت بعد إنشاء قاعدة البيانات الأساسية

-- جدول تمديدات الإيجار
CREATE TABLE rental_extensions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  extension_number TEXT UNIQUE NOT NULL DEFAULT 'EXT-' || EXTRACT(YEAR FROM NOW()) || '-' || LPAD(EXTRACT(DOY FROM NOW())::TEXT, 3, '0') || '-' || LPAD(EXTRACT(HOUR FROM NOW())::TEXT, 2, '0') || LPAD(EXTRACT(MINUTE FROM NOW())::TEXT, 2, '0'),
  
  -- معلومات الحجز الأساسية
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  car_id UUID REFERENCES cars(id) ON DELETE CASCADE NOT NULL,
  
  -- التواريخ
  original_return_date TIMESTAMP WITH TIME ZONE NOT NULL,
  new_return_date TIMESTAMP WITH TIME ZONE NOT NULL,
  extension_days INTEGER NOT NULL CHECK (extension_days > 0),
  extension_hours INTEGER DEFAULT 0,
  
  -- التكاليف
  daily_rate DECIMAL(10,2) NOT NULL,
  hourly_rate DECIMAL(10,2) DEFAULT 0,
  subtotal DECIMAL(10,2) NOT NULL, -- المبلغ قبل الضريبة
  vat_rate DECIMAL(5,2) NOT NULL DEFAULT 5.00, -- نسبة ضريبة القيمة المضافة
  vat_amount DECIMAL(10,2) NOT NULL DEFAULT 0, -- مبلغ الضريبة
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount > 0), -- المبلغ الإجمالي
  
  -- معلومات الدفع
  payment_method_id TEXT, -- معرف طريقة الدفع
  payment_method_type TEXT DEFAULT 'card' CHECK (payment_method_type IN ('card', 'cash', 'transfer')),
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'paid', 'failed', 'refunded')),
  payment_intent_id TEXT, -- معرف نية الدفع من Stripe
  transaction_id TEXT, -- معرف المعاملة
  
  -- الحالة والموافقة
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'paid', 'active', 'completed', 'cancelled')),
  approval_required BOOLEAN DEFAULT true,
  approved_by UUID REFERENCES profiles(id), -- من وافق على التمديد
  approved_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  
  -- الطلب والمعالجة
  request_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_date TIMESTAMP WITH TIME ZONE,
  completed_date TIMESTAMP WITH TIME ZONE,
  
  -- الملاحظات والأسباب
  request_reason TEXT, -- سبب طلب التمديد
  notes TEXT, -- ملاحظات إضافية
  admin_notes TEXT, -- ملاحظات إدارية
  
  -- المعلومات الإضافية
  ip_address INET, -- عنوان IP لطلب التمديد
  user_agent TEXT, -- معلومات المتصفح
  location_data JSONB, -- موقع المستخدم عند الطلب
  
  -- التوقيعات والوثائق
  contract_amendment_url TEXT, -- رابط تعديل العقد
  signature_url TEXT, -- توقيع على التمديد
  
  -- الطوارئ والحالات الخاصة
  is_emergency_extension BOOLEAN DEFAULT false,
  emergency_contact_notified BOOLEAN DEFAULT false,
  
  -- تواريخ النظام
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- القيود
  CONSTRAINT valid_dates CHECK (new_return_date > original_return_date),
  CONSTRAINT valid_amounts CHECK (subtotal >= 0 AND vat_amount >= 0 AND total_amount >= subtotal),
  CONSTRAINT valid_extension_period CHECK (extension_days > 0 OR extension_hours > 0)
);

-- فهارس للأداء
CREATE INDEX idx_rental_extensions_booking_id ON rental_extensions (booking_id);
CREATE INDEX idx_rental_extensions_user_id ON rental_extensions (user_id);
CREATE INDEX idx_rental_extensions_status ON rental_extensions (status);
CREATE INDEX idx_rental_extensions_payment_status ON rental_extensions (payment_status);
CREATE INDEX idx_rental_extensions_dates ON rental_extensions (original_return_date, new_return_date);
CREATE INDEX idx_rental_extensions_request_date ON rental_extensions (request_date);

-- جدول تاريخ حالات التمديد
CREATE TABLE rental_extension_status_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  extension_id UUID REFERENCES rental_extensions(id) ON DELETE CASCADE NOT NULL,
  
  old_status TEXT,
  new_status TEXT NOT NULL,
  changed_by UUID REFERENCES profiles(id),
  change_reason TEXT,
  notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_extension_status_history_extension_id ON rental_extension_status_history (extension_id);
CREATE INDEX idx_extension_status_history_created_at ON rental_extension_status_history (created_at);

-- جدول مدفوعات التمديد
CREATE TABLE rental_extension_payments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  extension_id UUID REFERENCES rental_extensions(id) ON DELETE CASCADE NOT NULL,
  
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  payment_method TEXT NOT NULL,
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'completed', 'failed', 'refunded')),
  
  -- معلومات الدفع الخارجي
  external_payment_id TEXT, -- معرف الدفعة الخارجية
  gateway_response JSONB, -- رد البوابة
  
  -- معلومات الاسترداد
  refund_amount DECIMAL(10,2) DEFAULT 0,
  refund_reason TEXT,
  refunded_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_extension_payments_extension_id ON rental_extension_payments (extension_id);
CREATE INDEX idx_extension_payments_status ON rental_extension_payments (payment_status);

-- دالة لحساب تكلفة التمديد
CREATE OR REPLACE FUNCTION calculate_extension_cost(
  p_booking_id UUID,
  p_extension_days INTEGER,
  p_extension_hours INTEGER DEFAULT 0
) RETURNS JSONB AS $$
DECLARE
  v_booking RECORD;
  v_daily_cost DECIMAL(10,2);
  v_hourly_cost DECIMAL(10,2);
  v_subtotal DECIMAL(10,2);
  v_vat_rate DECIMAL(5,2) := 5.00;
  v_vat_amount DECIMAL(10,2);
  v_total DECIMAL(10,2);
BEGIN
  -- جلب معلومات الحجز
  SELECT daily_rate, hourly_rate 
  INTO v_booking
  FROM bookings 
  WHERE id = p_booking_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Booking not found: %', p_booking_id;
  END IF;
  
  -- حساب التكلفة
  v_daily_cost := v_booking.daily_rate * p_extension_days;
  v_hourly_cost := COALESCE(v_booking.hourly_rate, 0) * p_extension_hours;
  v_subtotal := v_daily_cost + v_hourly_cost;
  v_vat_amount := v_subtotal * (v_vat_rate / 100);
  v_total := v_subtotal + v_vat_amount;
  
  RETURN jsonb_build_object(
    'daily_rate', v_booking.daily_rate,
    'hourly_rate', v_booking.hourly_rate,
    'extension_days', p_extension_days,
    'extension_hours', p_extension_hours,
    'daily_cost', v_daily_cost,
    'hourly_cost', v_hourly_cost,
    'subtotal', v_subtotal,
    'vat_rate', v_vat_rate,
    'vat_amount', v_vat_amount,
    'total_amount', v_total
  );
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث حالة التمديد
CREATE OR REPLACE FUNCTION update_extension_status(
  p_extension_id UUID,
  p_new_status TEXT,
  p_changed_by UUID DEFAULT NULL,
  p_notes TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_old_status TEXT;
BEGIN
  -- جلب الحالة الحالية
  SELECT status INTO v_old_status
  FROM rental_extensions 
  WHERE id = p_extension_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Extension not found: %', p_extension_id;
  END IF;
  
  -- تحديث الحالة
  UPDATE rental_extensions 
  SET 
    status = p_new_status,
    updated_at = NOW(),
    processed_date = CASE WHEN p_new_status IN ('approved', 'rejected') THEN NOW() ELSE processed_date END,
    completed_date = CASE WHEN p_new_status = 'completed' THEN NOW() ELSE completed_date END,
    approved_by = CASE WHEN p_new_status = 'approved' THEN p_changed_by ELSE approved_by END,
    approved_at = CASE WHEN p_new_status = 'approved' THEN NOW() ELSE approved_at END
  WHERE id = p_extension_id;
  
  -- إضافة سجل في تاريخ الحالات
  INSERT INTO rental_extension_status_history (
    extension_id, old_status, new_status, changed_by, notes
  ) VALUES (
    p_extension_id, v_old_status, p_new_status, p_changed_by, p_notes
  );
  
  -- تحديث تاريخ إرجاع الحجز الأصلية إذا تم الموافقة
  IF p_new_status = 'approved' THEN
    UPDATE bookings 
    SET return_date = (
      SELECT new_return_date 
      FROM rental_extensions 
      WHERE id = p_extension_id
    ),
    updated_at = NOW()
    WHERE id = (
      SELECT booking_id 
      FROM rental_extensions 
      WHERE id = p_extension_id
    );
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- دالة محفز لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفزات
CREATE TRIGGER update_rental_extensions_updated_at 
  BEFORE UPDATE ON rental_extensions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_extension_payments_updated_at 
  BEFORE UPDATE ON rental_extension_payments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إعدادات الأمان
ALTER TABLE rental_extensions ENABLE ROW LEVEL SECURITY;
ALTER TABLE rental_extension_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE rental_extension_payments ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للمستخدمين
CREATE POLICY "Users can view own extensions" ON rental_extensions 
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own extensions" ON rental_extensions 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own extensions" ON rental_extensions 
  FOR UPDATE USING (auth.uid() = user_id);

-- سياسات تاريخ الحالات
CREATE POLICY "Users can view own extension history" ON rental_extension_status_history 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM rental_extensions 
      WHERE rental_extensions.id = rental_extension_status_history.extension_id 
      AND rental_extensions.user_id = auth.uid()
    )
  );

-- سياسات المدفوعات
CREATE POLICY "Users can view own extension payments" ON rental_extension_payments 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM rental_extensions 
      WHERE rental_extensions.id = rental_extension_payments.extension_id 
      AND rental_extensions.user_id = auth.uid()
    )
  );

-- إضافة بعض الإعدادات الافتراضية لنظام التمديد
INSERT INTO system_settings (key, value_ar, value_en, category, is_active) VALUES
('max_extension_days', '7', '7', 'rental_extensions', true),
('max_extensions_per_booking', '3', '3', 'rental_extensions', true),
('extension_approval_required', 'true', 'true', 'rental_extensions', true),
('extension_fee_percentage', '0', '0', 'rental_extensions', true),
('emergency_extension_allowed', 'true', 'true', 'rental_extensions', true),
('extension_notification_enabled', 'true', 'true', 'rental_extensions', true);

-- تعليق على الجداول والأعمدة
COMMENT ON TABLE rental_extensions IS 'جدول تمديدات الإيجار - يحتوي على طلبات تمديد فترات الإيجار';
COMMENT ON COLUMN rental_extensions.extension_number IS 'رقم التمديد الفريد';
COMMENT ON COLUMN rental_extensions.booking_id IS 'معرف الحجز الأصلي';
COMMENT ON COLUMN rental_extensions.original_return_date IS 'تاريخ الإرجاع الأصلي';
COMMENT ON COLUMN rental_extensions.new_return_date IS 'تاريخ الإرجاع الجديد بعد التمديد';
COMMENT ON COLUMN rental_extensions.payment_intent_id IS 'معرف نية الدفع من Stripe';
COMMENT ON COLUMN rental_extensions.approval_required IS 'هل يتطلب موافقة إدارية';
