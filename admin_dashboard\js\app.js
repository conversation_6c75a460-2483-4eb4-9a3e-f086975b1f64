// ملف JavaScript الرئيسي لإدارة لوحة التحكم

// متغيرات عامة
let currentSection = 'dashboard';
let appCurrentPage = 1;
let itemsPerPage = 10;
let charts = {};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadDashboardData();
    setupEventListeners();
    startAutoRefresh();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('تم تهيئة لوحة التحكم');
    
    // تعيين التواريخ الافتراضية
    const today = new Date();
    const lastMonth = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('startDate').value = formatDate(lastMonth);
    document.getElementById('endDate').value = formatDate(today);
    
    // إخفاء loading overlay
    hideLoading();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // Navigation links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('href').substring(1);
            showSection(section);
        });
    });

    // Modal close buttons
    document.querySelectorAll('.close-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal.id);
        });
    });

    // Form submissions
    setupFormHandlers();
    
    // Click outside to close dropdowns
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.notifications')) {
            document.getElementById('notificationsDropdown').classList.remove('show');
        }
        if (!e.target.closest('.user-profile')) {
            document.getElementById('profileDropdown').classList.remove('show');
        }
    });
}

// إعداد معالجات النماذج
function setupFormHandlers() {
    // Add Car Form
    document.getElementById('addCarForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleAddCar();
    });

    // Add Location Form
    document.getElementById('addLocationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleAddLocation();
    });

    // Add Coupon Form
    document.getElementById('addCouponForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleAddCoupon();
    });
}

// إظهار/إخفاء الأقسام
function showSection(sectionId) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });

    // إزالة active class من جميع nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // إظهار القسم المطلوب
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionId;
        
        // إضافة active class للـ nav item المناسب
        const navLink = document.querySelector(`[href="#${sectionId}"]`);
        if (navLink) {
            navLink.closest('.nav-item').classList.add('active');
        }
        
        // تحميل بيانات القسم
        loadSectionData(sectionId);
    }
}

// تحميل بيانات القسم
function loadSectionData(sectionId) {
    showLoading();
    
    switch(sectionId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'cars':
            loadCarsData();
            break;
        case 'bookings':
            loadBookingsData();
            break;
        case 'users':
            loadUsersData();
            break;
        case 'payments':
            loadPaymentsData();
            break;
        case 'reviews':
            loadReviewsData();
            break;
        case 'offices':
            if (typeof initializeOffices === 'function') {
                initializeOffices();
            } else {
                loadOfficesData();
            }
            break;
        case 'locations':
            loadLocationsData();
            break;
        case 'coupons':
            loadCouponsData();
            break;
        case 'reports':
            loadReportsData();
            break;
        case 'settings':
            loadSettingsData();
            break;
    }
}

// إظهار/إخفاء Loading
function showLoading() {
    document.getElementById('loadingOverlay').classList.add('show');
}

function hideLoading() {
    document.getElementById('loadingOverlay').classList.remove('show');
}

// إدارة الإشعارات
function toggleNotifications() {
    const dropdown = document.getElementById('notificationsDropdown');
    dropdown.classList.toggle('show');
    
    if (dropdown.classList.contains('show')) {
        loadNotifications();
    }
}

function loadNotifications() {
    const notificationsList = document.getElementById('notificationsList');
    
    // محاكاة بيانات الإشعارات
    const notifications = [
        {
            id: 1,
            title: 'حجز جديد',
            message: 'تم استلام حجز جديد من أحمد محمد',
            time: 'منذ 5 دقائق',
            type: 'booking',
            read: false
        },
        {
            id: 2,
            title: 'دفعة مكتملة',
            message: 'تم استلام دفعة بقيمة 450 د.إ',
            time: 'منذ 15 دقيقة',
            type: 'payment',
            read: false
        },
        {
            id: 3,
            title: 'تقييم جديد',
            message: 'تقييم 5 نجوم لسيارة تويوتا كامري',
            time: 'منذ ساعة',
            type: 'review',
            read: true
        }
    ];
    
    notificationsList.innerHTML = notifications.map(notif => `
        <div class="notification-item ${notif.read ? 'read' : 'unread'}">
            <div class="notification-icon ${notif.type}">
                <i class="fas ${getNotificationIcon(notif.type)}"></i>
            </div>
            <div class="notification-content">
                <h4>${notif.title}</h4>
                <p>${notif.message}</p>
                <span class="notification-time">${notif.time}</span>
            </div>
        </div>
    `).join('');
}

function getNotificationIcon(type) {
    const icons = {
        booking: 'fa-calendar-check',
        payment: 'fa-credit-card',
        review: 'fa-star',
        user: 'fa-user',
        system: 'fa-cog'
    };
    return icons[type] || 'fa-bell';
}

function markAllAsRead() {
    document.querySelectorAll('.notification-item').forEach(item => {
        item.classList.remove('unread');
        item.classList.add('read');
    });
    
    document.getElementById('notificationCount').textContent = '0';
    document.getElementById('notificationCount').style.display = 'none';
}

// إدارة القائمة الشخصية
function toggleProfileMenu() {
    document.getElementById('profileDropdown').classList.toggle('show');
}

function showProfile() {
    console.log('عرض الملف الشخصي');
    // يمكن إضافة منطق عرض الملف الشخصي هنا
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        console.log('تسجيل الخروج');
        // يمكن إضافة منطق تسجيل الخروج هنا
        window.location.href = '/login.html';
    }
}

// إدارة المودلز
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';
        
        // إعادة تعيين النموذج
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }
}

// إدارة المودلز الخاصة
function showAddCarModal() {
    showModal('addCarModal');
}

function showAddLocationModal() {
    showModal('addLocationModal');
}

function showAddCouponModal() {
    showModal('addCouponModal');
}

function showAddBookingModal() {
    console.log('عرض نافذة إضافة حجز جديد');
    // يمكن إضافة منطق إضافة حجز هنا
}

// معالجات النماذج
function handleAddCar() {
    const formData = new FormData();
    formData.append('name', document.getElementById('carName').value);
    formData.append('make', document.getElementById('carMake').value);
    formData.append('model', document.getElementById('carModel').value);
    formData.append('year', document.getElementById('carYear').value);
    formData.append('category', document.getElementById('carCategory').value);
    formData.append('type', document.getElementById('carType').value);
    formData.append('daily_price', document.getElementById('carDailyPrice').value);
    formData.append('plate_number', document.getElementById('carPlateNumber').value);
    formData.append('description', document.getElementById('carDescription').value);
    
    // إضافة الصور
    const images = document.getElementById('carImages').files;
    for (let i = 0; i < images.length; i++) {
        formData.append('images[]', images[i]);
    }
    
    showLoading();
    
    // محاكاة إرسال البيانات
    setTimeout(() => {
        hideLoading();
        closeModal('addCarModal');
        showNotification('تم إضافة السيارة بنجاح', 'success');
        loadCarsData(); // إعادة تحميل بيانات السيارات
    }, 2000);
}

function handleAddLocation() {
    const locationData = {
        name: document.getElementById('locationName').value,
        address: document.getElementById('locationAddress').value,
        latitude: parseFloat(document.getElementById('locationLat').value),
        longitude: parseFloat(document.getElementById('locationLng').value),
        type: document.getElementById('locationType').value
    };
    
    showLoading();
    
    // محاكاة إرسال البيانات
    setTimeout(() => {
        hideLoading();
        closeModal('addLocationModal');
        showNotification('تم إضافة الموقع بنجاح', 'success');
        loadLocationsData();
    }, 1500);
}

function handleAddCoupon() {
    const couponData = {
        code: document.getElementById('couponCode').value,
        type: document.getElementById('couponType').value,
        value: parseFloat(document.getElementById('couponValue').value),
        min_order: parseFloat(document.getElementById('couponMinOrder').value) || 0,
        valid_from: document.getElementById('couponValidFrom').value,
        valid_to: document.getElementById('couponValidTo').value
    };
    
    showLoading();
    
    // محاكاة إرسال البيانات
    setTimeout(() => {
        hideLoading();
        closeModal('addCouponModal');
        showNotification('تم إضافة الكوبون بنجاح', 'success');
        loadCouponsData();
    }, 1500);
}

// إظهار الإشعارات
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationTypeIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // إضافة styles للإشعار
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        left: 20px;
        background: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        z-index: 2001;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: 300px;
        border-right: 4px solid ${getNotificationColor(type)};
        animation: slideInLeft 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutLeft 0.3s ease';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, duration);
}

function getNotificationTypeIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

function getNotificationColor(type) {
    const colors = {
        success: '#38a169',
        error: '#e53e3e',
        warning: '#ed8936',
        info: '#3182ce'
    };
    return colors[type] || '#3182ce';
}

// دوال التصدير
function exportCars() {
    showLoading();
    setTimeout(() => {
        hideLoading();
        showNotification('تم تصدير بيانات السيارات بنجاح', 'success');
    }, 1000);
}

function exportBookings() {
    showLoading();
    setTimeout(() => {
        hideLoading();
        showNotification('تم تصدير بيانات الحجوزات بنجاح', 'success');
    }, 1000);
}

function exportUsers() {
    showLoading();
    setTimeout(() => {
        hideLoading();
        showNotification('تم تصدير بيانات المستخدمين بنجاح', 'success');
    }, 1000);
}

function exportPayments() {
    showLoading();
    setTimeout(() => {
        hideLoading();
        showNotification('تم تصدير بيانات المدفوعات بنجاح', 'success');
    }, 1000);
}

function exportReviews() {
    showLoading();
    setTimeout(() => {
        hideLoading();
        showNotification('تم تصدير بيانات التقييمات بنجاح', 'success');
    }, 1000);
}

// إدارة الإعدادات
function showSettingsTab(tabName) {
    // إخفاء جميع المحتويات
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // إزالة active class من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إظهار المحتوى المطلوب
    document.getElementById(tabName + 'Settings').classList.add('active');
    
    // إضافة active class للزر
    event.target.classList.add('active');
}

function saveSettings() {
    showLoading();
    
    const settings = {
        app_name: document.getElementById('appName').value,
        support_phone: document.getElementById('supportPhone').value,
        support_email: document.getElementById('supportEmail').value,
        min_rental_age: document.getElementById('minRentalAge').value,
        max_rental_days: document.getElementById('maxRentalDays').value,
        cancellation_hours: document.getElementById('cancellationHours').value,
        default_currency: document.getElementById('defaultCurrency').value,
        tax_rate: document.getElementById('taxRate').value,
        email_notifications: document.getElementById('emailNotifications').checked,
        sms_notifications: document.getElementById('smsNotifications').checked
    };
    
    // محاكاة حفظ الإعدادات
    setTimeout(() => {
        hideLoading();
        showNotification('تم حفظ الإعدادات بنجاح', 'success');
    }, 1500);
}

// دوال مساعدة
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-AE') + ' ' + date.toLocaleTimeString('ar-AE');
}

function formatCurrency(amount, currency = 'AED') {
    return new Intl.NumberFormat('ar-AE', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
    }).format(amount);
}

function formatNumber(number) {
    return new Intl.NumberFormat('ar-AE').format(number);
}

// تحديث تلقائي للبيانات
function startAutoRefresh() {
    // تحديث الإحصائيات كل 5 دقائق
    setInterval(() => {
        if (currentSection === 'dashboard') {
            updateDashboardStats();
        }
    }, 5 * 60 * 1000);
    
    // تحديث الإشعارات كل دقيقة
    setInterval(() => {
        updateNotificationCount();
    }, 60 * 1000);
}

function updateNotificationCount() {
    // محاكاة تحديث عدد الإشعارات
    const count = Math.floor(Math.random() * 10);
    const countElement = document.getElementById('notificationCount');
    
    if (count > 0) {
        countElement.textContent = count;
        countElement.style.display = 'block';
    } else {
        countElement.style.display = 'none';
    }
}

// معالجة الأخطاء
window.addEventListener('error', function(e) {
    console.error('خطأ في التطبيق:', e.error);
    showNotification('حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى.', 'error');
});

// معالجة الأخطاء غير المتزامنة
window.addEventListener('unhandledrejection', function(e) {
    console.error('خطأ غير متزامن:', e.reason);
    showNotification('حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى.', 'error');
});

// إضافة CSS animations للإشعارات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInLeft {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutLeft {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(-100%);
            opacity: 0;
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s;
    }
    
    .notification-close:hover {
        opacity: 1;
    }
`;
document.head.appendChild(style);

// دوال تحميل البيانات المختلفة
function loadDashboardData() {
    if (typeof initializeDashboard === 'function') {
        initializeDashboard();
    }
    hideLoading();
}

function loadCarsData() {
    if (typeof initializeCars === 'function') {
        initializeCars();
    }
    hideLoading();
}

function loadBookingsData() {
    if (typeof initializeBookings === 'function') {
        initializeBookings();
    }
    hideLoading();
}

function loadUsersData() {
    if (typeof initializeUsers === 'function') {
        initializeUsers();
    }
    hideLoading();
}

function loadPaymentsData() {
    if (typeof initializePayments === 'function') {
        initializePayments();
    }
    hideLoading();
}

function loadReviewsData() {
    if (typeof initializeReviews === 'function') {
        initializeReviews();
    }
    hideLoading();
}

function loadOfficesData() {
    if (typeof initializeOffices === 'function') {
        initializeOffices();
    }
    hideLoading();
}

function loadLocationsData() {
    console.log('تحميل بيانات المواقع...');
    hideLoading();
}

function loadCouponsData() {
    console.log('تحميل بيانات الكوبونات...');
    hideLoading();
}

function loadReportsData() {
    console.log('تحميل بيانات التقارير...');
    hideLoading();
}

function loadSettingsData() {
    console.log('تحميل بيانات الإعدادات...');
    hideLoading();
}
