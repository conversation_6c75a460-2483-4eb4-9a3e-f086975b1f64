import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

import '../providers/auth_provider.dart';
import '../widgets/simple_app_logo.dart';
import 'onboarding/onboarding_screen.dart';
import 'auth/login_screen.dart';
import 'main_navigation_screen.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _textController;
  late Animation<double> _textOpacityAnimation;
  late Animation<Offset> _textSlideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeIn),
    ));

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
    ));
  }

  void _startAnimations() {
    // البدء بتأخير بسيط
    Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        _textController.forward();
      }
    });
  }

  Future<void> _initializeApp() async {
    try {
      // تحميل AuthProvider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // انتظار ثانيتين على الأقل لعرض الشاشة
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      // التحقق من إعدادات المستخدم
      final prefs = await SharedPreferences.getInstance();
      final hasSeenOnboarding = prefs.getBool('has_seen_onboarding') ?? false;

      if (authProvider.isAuthenticated) {
        // المستخدم مُسجل الدخول
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => MainNavigationScreen()),
        );
      } else if (!hasSeenOnboarding) {
        // أول مرة يستخدم التطبيق
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => OnboardingScreen()),
        );
      } else {
        // المستخدم رأى الonboarding لكنه غير مُسجل الدخول
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => LoginScreen()),
        );
      }
    } catch (error) {
      print('خطأ في تحميل البيانات: $error');
      // في حالة حدوث خطأ، الانتقال إلى شاشة تسجيل الدخول
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => LoginScreen()),
        );
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF42A5F5), // أزرق فاتح لجعل الشعار أوضح
              const Color(0xFF64B5F6), // أزرق فاتح أكثر
              const Color(0xFF90CAF9), // أزرق فاتح جداً
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار الشركة
              SimpleSplashLogo(
                onComplete: () {
                  // يمكن إضافة أي منطق إضافي هنا
                },
              ),

              const SizedBox(height: 50),

              // نص الشركة مع الرسوم المتحركة
              SlideTransition(
                position: _textSlideAnimation,
                child: FadeTransition(
                  opacity: _textOpacityAnimation,
                  child: Column(
                    children: [
                      const Text(
                        'Dubai Car Rental',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0D47A1), // أزرق داكن للنص
                          letterSpacing: 1.5,
                          shadows: [
                            Shadow(
                              color: Colors.white54,
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'تأجير السيارات الفاخرة في دبي',
                        style: TextStyle(
                          fontSize: 18,
                          color:
                              const Color(0xFF1565C0), // أزرق متوسط للنص العربي
                          fontWeight: FontWeight.w400,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Premium Car Rental Experience',
                        style: TextStyle(
                          fontSize: 16,
                          color: const Color(0xFF1976D2), // أزرق للنص الإنجليزي
                          fontWeight: FontWeight.w300,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 80),

              // مؤشر التحميل
              Container(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  color: const Color(0xFF0D47A1), // أزرق داكن للمؤشر
                  backgroundColor: Colors.white.withOpacity(0.5),
                  strokeWidth: 3,
                ),
              ),

              const SizedBox(height: 20),

              Text(
                'جاري التحميل...',
                style: TextStyle(
                  color: const Color(0xFF1565C0), // أزرق متوسط للنص
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
