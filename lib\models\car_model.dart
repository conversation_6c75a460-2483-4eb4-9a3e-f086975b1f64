import 'package:equatable/equatable.dart';

class CarModel extends Equatable {
  final String id;
  final String name;
  final String make;  // العلامة التجارية (جديد في قاعدة البيانات)
  final String brand; // للتوافق مع النظام الحالي
  final String model;
  final int year;
  final String category; // economy, standard, premium, luxury, exotic
  final String type;     // sedan, suv, hatchback, coupe, convertible, pickup, luxury
  final String transmission; // automatic, manual
  final String fuelType; // gasoline, petrol, diesel, hybrid, electric
  final int doors;
  final int seats;
  final String? color;
  
  // معلومات السيارة
  final String plateNumber;    // plate_number (الأساسي)
  final String licensePlate;   // license_plate (للتوافق)
  final String? vinNumber;
  final int mileage;
  final double? fuelCapacity;
  final String? engineSize;
  
  // الأسعار
  final double hourlyPrice;
  final double dailyRate;   // daily_rate (الأساسي)
  final double dailyPrice;  // daily_price (للتوافق)
  final double weeklyRate;  // weekly_rate (الأساسي)
  final double weeklyPrice; // weekly_price (للتوافق)
  final double monthlyRate; // monthly_rate (الأساسي)
  final double monthlyPrice;// monthly_price (للتوافق)
  final double securityDeposit;
  
  // التقييمات
  final double rating;
  final int totalRatings;
  final int totalReviews;
  
  // الوسائط
  final List<String> images;
  final List<String> imageUrls; // للتوافق مع النظام الحالي
  final String? thumbnailUrl;
  final String? videoUrl;
  
  // المميزات
  final List<String> features;
  final List<String> safetyFeatures;
  final List<String> comfortFeatures;
  
  // الموقع
  final double locationLat;
  final double locationLng;
  final String locationAddress;
  final Map<String, dynamic>? currentLocation; // JSONB
  final List<String> pickupLocations;
  final String? pickupLocation;
  final String? returnLocation;
  
  // الحالة
  final bool isAvailable;
  final bool isFeatured;
  final String availabilityStatus; // available, rented, maintenance, unavailable
  final CarStatus status; // active, inactive, maintenance, retired
  
  // التأمين والصيانة
  final Map<String, dynamic>? insurance; // JSONB
  final DateTime insuranceExpiry;
  final DateTime registrationExpiry;
  final DateTime? lastServiceDate;
  final DateTime? lastMaintenance;
  final DateTime? nextServiceDate;
  final DateTime? nextMaintenance;
  
  // الوصف
  final String? description;
  final String? descriptionAr;
  final String? descriptionEn;
  final String? termsConditionsAr;
  final String? termsConditionsEn;
  
  final DateTime createdAt;
  final DateTime updatedAt;

  const CarModel({
    required this.id,
    required this.name,
    required this.make,
    required this.brand,
    required this.model,
    required this.year,
    required this.category,
    required this.type,
    required this.transmission,
    required this.fuelType,
    required this.doors,
    required this.seats,
    this.color,
    required this.plateNumber,
    required this.licensePlate,
    this.vinNumber,
    required this.mileage,
    this.fuelCapacity,
    this.engineSize,
    required this.hourlyPrice,
    required this.dailyRate,
    required this.dailyPrice,
    required this.weeklyRate,
    required this.weeklyPrice,
    required this.monthlyRate,
    required this.monthlyPrice,
    required this.securityDeposit,
    required this.rating,
    required this.totalRatings,
    required this.totalReviews,
    required this.images,
    required this.imageUrls,
    this.thumbnailUrl,
    this.videoUrl,
    required this.features,
    required this.safetyFeatures,
    required this.comfortFeatures,
    required this.locationLat,
    required this.locationLng,
    required this.locationAddress,
    this.currentLocation,
    required this.pickupLocations,
    this.pickupLocation,
    this.returnLocation,
    required this.isAvailable,
    required this.isFeatured,
    required this.availabilityStatus,
    required this.status,
    this.insurance,
    required this.insuranceExpiry,
    required this.registrationExpiry,
    this.lastServiceDate,
    this.lastMaintenance,
    this.nextServiceDate,
    this.nextMaintenance,
    this.description,
    this.descriptionAr,
    this.descriptionEn,
    this.termsConditionsAr,
    this.termsConditionsEn,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CarModel.fromJson(Map<String, dynamic> json) {
    return CarModel(
      id: json['id'] as String,
      name: json['name'] as String,
      make: json['make'] as String,
      brand: json['brand'] as String,
      model: json['model'] as String,
      year: json['year'] as int,
      category: json['category'] as String,
      type: json['type'] as String,
      transmission: json['transmission'] as String,
      fuelType: json['fuel_type'] as String,
      doors: json['doors'] as int,
      seats: json['seats'] as int,
      color: json['color'] as String?,
      plateNumber: json['plate_number'] as String,
      licensePlate: json['license_plate'] as String,
      vinNumber: json['vin_number'] as String?,
      mileage: json['mileage'] as int? ?? 0,
      fuelCapacity: json['fuel_capacity'] != null 
          ? (json['fuel_capacity'] as num).toDouble() 
          : null,
      engineSize: json['engine_size'] as String?,
      hourlyPrice: (json['hourly_price'] as num? ?? 0).toDouble(),
      dailyRate: (json['daily_rate'] as num).toDouble(),
      dailyPrice: (json['daily_price'] as num).toDouble(),
      weeklyRate: (json['weekly_rate'] as num).toDouble(),
      weeklyPrice: (json['weekly_price'] as num).toDouble(),
      monthlyRate: (json['monthly_rate'] as num).toDouble(),
      monthlyPrice: (json['monthly_price'] as num).toDouble(),
      securityDeposit: (json['security_deposit'] as num? ?? 1000).toDouble(),
      rating: (json['rating'] as num? ?? 0).toDouble(),
      totalRatings: json['total_ratings'] as int? ?? 0,
      totalReviews: json['total_reviews'] as int? ?? 0,
      images: List<String>.from(json['images'] as List? ?? []),
      imageUrls: List<String>.from(json['image_urls'] as List? ?? []),
      thumbnailUrl: json['thumbnail_url'] as String?,
      videoUrl: json['video_url'] as String?,
      features: List<String>.from(json['features'] as List? ?? []),
      safetyFeatures: List<String>.from(json['safety_features'] as List? ?? []),
      comfortFeatures: List<String>.from(json['comfort_features'] as List? ?? []),
      locationLat: (json['location_lat'] as num).toDouble(),
      locationLng: (json['location_lng'] as num).toDouble(),
      locationAddress: json['location_address'] as String,
      currentLocation: json['current_location'] != null 
          ? Map<String, dynamic>.from(json['current_location'] as Map) 
          : null,
      pickupLocations: List<String>.from(json['pickup_locations'] as List? ?? []),
      pickupLocation: json['pickup_location'] as String?,
      returnLocation: json['return_location'] as String?,
      isAvailable: json['is_available'] as bool? ?? true,
      isFeatured: json['is_featured'] as bool? ?? false,
      availabilityStatus: json['availability_status'] as String? ?? 'available',
      status: CarStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String? ?? 'active'),
        orElse: () => CarStatus.active,
      ),
      insurance: json['insurance'] != null 
          ? Map<String, dynamic>.from(json['insurance'] as Map) 
          : null,
      insuranceExpiry: DateTime.parse(json['insurance_expiry'] as String),
      registrationExpiry: DateTime.parse(json['registration_expiry'] as String),
      lastServiceDate: json['last_service_date'] != null
          ? DateTime.parse(json['last_service_date'] as String)
          : null,
      lastMaintenance: json['last_maintenance'] != null
          ? DateTime.parse(json['last_maintenance'] as String)
          : null,
      nextServiceDate: json['next_service_date'] != null
          ? DateTime.parse(json['next_service_date'] as String)
          : null,
      nextMaintenance: json['next_maintenance'] != null
          ? DateTime.parse(json['next_maintenance'] as String)
          : null,
      description: json['description'] as String?,
      descriptionAr: json['description_ar'] as String?,
      descriptionEn: json['description_en'] as String?,
      termsConditionsAr: json['terms_conditions_ar'] as String?,
      termsConditionsEn: json['terms_conditions_en'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'make': make,
      'brand': brand,
      'model': model,
      'year': year,
      'category': category,
      'type': type,
      'transmission': transmission,
      'fuel_type': fuelType,
      'doors': doors,
      'seats': seats,
      'color': color,
      'plate_number': plateNumber,
      'license_plate': licensePlate,
      'vin_number': vinNumber,
      'mileage': mileage,
      'fuel_capacity': fuelCapacity,
      'engine_size': engineSize,
      'hourly_price': hourlyPrice,
      'daily_rate': dailyRate,
      'daily_price': dailyPrice,
      'weekly_rate': weeklyRate,
      'weekly_price': weeklyPrice,
      'monthly_rate': monthlyRate,
      'monthly_price': monthlyPrice,
      'security_deposit': securityDeposit,
      'rating': rating,
      'total_ratings': totalRatings,
      'total_reviews': totalReviews,
      'images': images,
      'image_urls': imageUrls,
      'thumbnail_url': thumbnailUrl,
      'video_url': videoUrl,
      'features': features,
      'safety_features': safetyFeatures,
      'comfort_features': comfortFeatures,
      'location_lat': locationLat,
      'location_lng': locationLng,
      'location_address': locationAddress,
      'current_location': currentLocation,
      'pickup_locations': pickupLocations,
      'pickup_location': pickupLocation,
      'return_location': returnLocation,
      'is_available': isAvailable,
      'is_featured': isFeatured,
      'availability_status': availabilityStatus,
      'status': status.name,
      'insurance': insurance,
      'insurance_expiry': insuranceExpiry.toIso8601String().split('T')[0],
      'registration_expiry': registrationExpiry.toIso8601String().split('T')[0],
      'last_service_date': lastServiceDate?.toIso8601String().split('T')[0],
      'last_maintenance': lastMaintenance?.toIso8601String().split('T')[0],
      'next_service_date': nextServiceDate?.toIso8601String().split('T')[0],
      'next_maintenance': nextMaintenance?.toIso8601String().split('T')[0],
      'description': description,
      'description_ar': descriptionAr,
      'description_en': descriptionEn,
      'terms_conditions_ar': termsConditionsAr,
      'terms_conditions_en': termsConditionsEn,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        make,
        brand,
        model,
        year,
        category,
        type,
        transmission,
        fuelType,
        doors,
        seats,
        color,
        plateNumber,
        licensePlate,
        vinNumber,
        mileage,
        fuelCapacity,
        engineSize,
        hourlyPrice,
        dailyRate,
        dailyPrice,
        weeklyRate,
        weeklyPrice,
        monthlyRate,
        monthlyPrice,
        securityDeposit,
        rating,
        totalRatings,
        totalReviews,
        images,
        imageUrls,
        thumbnailUrl,
        videoUrl,
        features,
        safetyFeatures,
        comfortFeatures,
        locationLat,
        locationLng,
        locationAddress,
        currentLocation,
        pickupLocations,
        pickupLocation,
        returnLocation,
        isAvailable,
        isFeatured,
        availabilityStatus,
        status,
        insurance,
        insuranceExpiry,
        registrationExpiry,
        lastServiceDate,
        lastMaintenance,
        nextServiceDate,
        nextMaintenance,
        description,
        descriptionAr,
        descriptionEn,
        termsConditionsAr,
        termsConditionsEn,
        createdAt,
        updatedAt,
      ];
}

enum CarStatus {
  active,      // نشط
  inactive,    // غير نشط
  maintenance, // في الصيانة
  retired,     // معتزل/خارج الخدمة
}

// إبقاء الكلاسات الأخرى للتوافق
class LocationInfo {
  final String address;
  final double? latitude;
  final double? longitude;
  final String? details;

  const LocationInfo({
    required this.address,
    this.latitude,
    this.longitude,
    this.details,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      address: json['address'] as String? ?? '',
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      details: json['details'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'details': details,
    };
  }
}

class CarInsurance {
  final String provider;
  final String policyNumber;
  final DateTime expiryDate;
  final String coverage;

  const CarInsurance({
    required this.provider,
    required this.policyNumber,
    required this.expiryDate,
    required this.coverage,
  });

  factory CarInsurance.fromJson(Map<String, dynamic> json) {
    return CarInsurance(
      provider: json['provider'] as String,
      policyNumber: json['policy_number'] as String,
      expiryDate: DateTime.parse(json['expiry_date'] as String),
      coverage: json['coverage'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'policy_number': policyNumber,
      'expiry_date': expiryDate.toIso8601String(),
      'coverage': coverage,
    };
  }
}
