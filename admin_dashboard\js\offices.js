// ملف JavaScript لإدارة المكاتب في لوحة التحكم

// متغيرات عامة للمكاتب
let officesData = [];
let currentOfficePage = 1;
const officesPerPage = 10;
let totalOfficePages = 1;

// بيانات وهمية للمكاتب (للاختبار)
const mockOfficesData = [
    {
        id: '550e8400-e29b-41d4-a716-446655440001',
        office_code: 'DXB001',
        name: 'Dubai Main Office',
        name_ar: 'المكتب الرئيسي - دبي',
        name_en: 'Dubai Main Office',
        phone: '+971-4-555-1001',
        email: '<EMAIL>',
        whatsapp: '+971-50-123-1001',
        address: 'Downtown Dubai, Burj Khalifa Boulevard',
        address_ar: 'وسط دبي، شارع برج خليفة',
        address_en: 'Downtown Dubai, Burj Khalifa Boulevard',
        emirate: 'Dubai',
        city: 'Dubai',
        area: 'Downtown Dubai',
        latitude: 25.1972,
        longitude: 55.2744,
        office_type: 'main',
        capacity: 50,
        current_cars_count: 42,
        is_pickup_location: true,
        is_return_location: true,
        is_24_hours: false,
        has_valet_service: true,
        has_delivery_service: true,
        services: ['cleaning', 'maintenance', 'inspection', 'fuel'],
        facilities: ['parking', 'wifi', 'waiting_area', 'restroom'],
        manager_name: 'أحمد محمد',
        manager_phone: '+971-50-123-4567',
        staff_count: 8,
        rating: 4.5,
        total_ratings: 125,
        is_active: true,
        is_featured: true,
        status: 'active',
        description: 'المكتب الرئيسي في قلب دبي مع خدمات شاملة على مدار الساعة',
        working_hours: {
            "saturday": {"open": "08:00", "close": "22:00", "is_open": true},
            "sunday": {"open": "08:00", "close": "22:00", "is_open": true},
            "monday": {"open": "08:00", "close": "22:00", "is_open": true},
            "tuesday": {"open": "08:00", "close": "22:00", "is_open": true},
            "wednesday": {"open": "08:00", "close": "22:00", "is_open": true},
            "thursday": {"open": "08:00", "close": "22:00", "is_open": true},
            "friday": {"open": "14:00", "close": "22:00", "is_open": true}
        },
        images: ['/images/offices/dxb001_1.jpg', '/images/offices/dxb001_2.jpg'],
        created_at: new Date('2024-01-01T08:00:00'),
        updated_at: new Date('2024-01-15T14:30:00')
    },
    {
        id: '550e8400-e29b-41d4-a716-446655440002',
        office_code: 'DXB002',
        name: 'Dubai Airport Office',
        name_ar: 'مكتب مطار دبي',
        name_en: 'Dubai Airport Office',
        phone: '+971-4-555-1002',
        email: '<EMAIL>',
        whatsapp: '+971-50-123-1002',
        address: 'Dubai International Airport, Terminal 3',
        address_ar: 'مطار دبي الدولي، المبنى 3',
        address_en: 'Dubai International Airport, Terminal 3',
        emirate: 'Dubai',
        city: 'Dubai',
        area: 'Al Garhoud',
        latitude: 25.2532,
        longitude: 55.3657,
        office_type: 'branch',
        capacity: 30,
        current_cars_count: 28,
        is_pickup_location: true,
        is_return_location: true,
        is_24_hours: true,
        has_valet_service: false,
        has_delivery_service: false,
        services: ['cleaning', 'inspection'],
        facilities: ['parking', 'waiting_area'],
        manager_name: 'سارة أحمد',
        manager_phone: '+971-50-234-5678',
        staff_count: 6,
        rating: 4.2,
        total_ratings: 89,
        is_active: true,
        is_featured: false,
        status: 'active',
        description: 'مكتب مطار دبي يعمل على مدار الساعة لخدمة المسافرين',
        working_hours: {
            "saturday": {"open": "00:00", "close": "23:59", "is_open": true},
            "sunday": {"open": "00:00", "close": "23:59", "is_open": true},
            "monday": {"open": "00:00", "close": "23:59", "is_open": true},
            "tuesday": {"open": "00:00", "close": "23:59", "is_open": true},
            "wednesday": {"open": "00:00", "close": "23:59", "is_open": true},
            "thursday": {"open": "00:00", "close": "23:59", "is_open": true},
            "friday": {"open": "00:00", "close": "23:59", "is_open": true}
        },
        images: ['/images/offices/dxb002_1.jpg'],
        created_at: new Date('2024-01-05T10:00:00'),
        updated_at: new Date('2024-01-10T16:20:00')
    },
    {
        id: '550e8400-e29b-41d4-a716-446655440003',
        office_code: 'SHJ001',
        name: 'Sharjah Main Office',
        name_ar: 'المكتب الرئيسي - الشارقة',
        name_en: 'Sharjah Main Office',
        phone: '+971-6-555-2001',
        email: '<EMAIL>',
        whatsapp: '+971-50-345-6789',
        address: 'Al Majaz, Corniche Street',
        address_ar: 'المجاز، شارع الكورنيش',
        address_en: 'Al Majaz, Corniche Street',
        emirate: 'Sharjah',
        city: 'Sharjah',
        area: 'Al Majaz',
        latitude: 25.3216,
        longitude: 55.3809,
        office_type: 'branch',
        capacity: 25,
        current_cars_count: 20,
        is_pickup_location: true,
        is_return_location: true,
        is_24_hours: false,
        has_valet_service: true,
        has_delivery_service: true,
        services: ['cleaning', 'maintenance', 'inspection'],
        facilities: ['parking', 'wifi', 'waiting_area', 'restroom', 'cafe'],
        manager_name: 'محمد خالد',
        manager_phone: '+971-50-456-7890',
        staff_count: 5,
        rating: 4.3,
        total_ratings: 67,
        is_active: true,
        is_featured: false,
        status: 'active',
        description: 'فرع الشارقة الرئيسي مع إطلالة على كورنيش المجاز',
        working_hours: {
            "saturday": {"open": "08:00", "close": "22:00", "is_open": true},
            "sunday": {"open": "08:00", "close": "22:00", "is_open": true},
            "monday": {"open": "08:00", "close": "22:00", "is_open": true},
            "tuesday": {"open": "08:00", "close": "22:00", "is_open": true},
            "wednesday": {"open": "08:00", "close": "22:00", "is_open": true},
            "thursday": {"open": "08:00", "close": "22:00", "is_open": true},
            "friday": {"open": "14:00", "close": "22:00", "is_open": true}
        },
        images: ['/images/offices/shj001_1.jpg', '/images/offices/shj001_2.jpg'],
        created_at: new Date('2024-01-08T12:00:00'),
        updated_at: new Date('2024-01-12T10:45:00')
    },
    {
        id: '550e8400-e29b-41d4-a716-446655440004',
        office_code: 'AUH001',
        name: 'Abu Dhabi Main Office',
        name_ar: 'المكتب الرئيسي - أبوظبي',
        name_en: 'Abu Dhabi Main Office',
        phone: '+971-2-555-3001',
        email: '<EMAIL>',
        whatsapp: '+971-50-567-8901',
        address: 'Corniche Road, Abu Dhabi',
        address_ar: 'شارع الكورنيش، أبوظبي',
        address_en: 'Corniche Road, Abu Dhabi',
        emirate: 'Abu Dhabi',
        city: 'Abu Dhabi',
        area: 'Corniche',
        latitude: 24.4539,
        longitude: 54.3773,
        office_type: 'branch',
        capacity: 35,
        current_cars_count: 32,
        is_pickup_location: true,
        is_return_location: true,
        is_24_hours: false,
        has_valet_service: true,
        has_delivery_service: true,
        services: ['cleaning', 'maintenance', 'inspection', 'fuel'],
        facilities: ['parking', 'wifi', 'waiting_area', 'restroom'],
        manager_name: 'فاطمة علي',
        manager_phone: '+971-50-678-9012',
        staff_count: 7,
        rating: 4.4,
        total_ratings: 98,
        is_active: true,
        is_featured: true,
        status: 'active',
        description: 'فرع أبوظبي الرئيسي على كورنيش العاصمة',
        working_hours: {
            "saturday": {"open": "08:00", "close": "22:00", "is_open": true},
            "sunday": {"open": "08:00", "close": "22:00", "is_open": true},
            "monday": {"open": "08:00", "close": "22:00", "is_open": true},
            "tuesday": {"open": "08:00", "close": "22:00", "is_open": true},
            "wednesday": {"open": "08:00", "close": "22:00", "is_open": true},
            "thursday": {"open": "08:00", "close": "22:00", "is_open": true},
            "friday": {"open": "14:00", "close": "22:00", "is_open": true}
        },
        images: ['/images/offices/auh001_1.jpg'],
        created_at: new Date('2024-01-10T09:30:00'),
        updated_at: new Date('2024-01-14T15:15:00')
    },
    {
        id: '550e8400-e29b-41d4-a716-446655440005',
        office_code: 'DXB003',
        name: 'Dubai Mall Pickup Point',
        name_ar: 'نقطة استلام دبي مول',
        name_en: 'Dubai Mall Pickup Point',
        phone: '+971-4-555-1003',
        email: '<EMAIL>',
        whatsapp: '+971-50-789-0123',
        address: 'The Dubai Mall, Lower Ground Floor',
        address_ar: 'دبي مول، الطابق السفلي',
        address_en: 'The Dubai Mall, Lower Ground Floor',
        emirate: 'Dubai',
        city: 'Dubai',
        area: 'Downtown Dubai',
        latitude: 25.1975,
        longitude: 55.2796,
        office_type: 'pickup_point',
        capacity: 15,
        current_cars_count: 12,
        is_pickup_location: true,
        is_return_location: true,
        is_24_hours: false,
        has_valet_service: false,
        has_delivery_service: false,
        services: ['inspection'],
        facilities: ['parking'],
        manager_name: 'عبدالله حسن',
        manager_phone: '+971-50-890-1234',
        staff_count: 3,
        rating: 4.1,
        total_ratings: 45,
        is_active: true,
        is_featured: false,
        status: 'active',
        description: 'نقطة استلام مريحة في دبي مول',
        working_hours: {
            "saturday": {"open": "10:00", "close": "22:00", "is_open": true},
            "sunday": {"open": "10:00", "close": "22:00", "is_open": true},
            "monday": {"open": "10:00", "close": "22:00", "is_open": true},
            "tuesday": {"open": "10:00", "close": "22:00", "is_open": true},
            "wednesday": {"open": "10:00", "close": "22:00", "is_open": true},
            "thursday": {"open": "10:00", "close": "24:00", "is_open": true},
            "friday": {"open": "10:00", "close": "24:00", "is_open": true}
        },
        images: ['/images/offices/dxb003_1.jpg'],
        created_at: new Date('2024-01-12T14:00:00'),
        updated_at: new Date('2024-01-12T14:00:00')
    }
];

// تهيئة صفحة المكاتب
function initializeOffices() {
    console.log('تهيئة صفحة المكاتب...');
    
    // تحميل البيانات
    loadOfficesData();
    
    // ربط الأحداث
    bindOfficeEvents();
    
    // تحديث الإحصائيات
    updateOfficeStats();
}

// تحميل بيانات المكاتب
async function loadOfficesData() {
    try {
        showLoading(true);
        
        // في الواقع، هذا سيكون استدعاء API
        // const response = await fetchOffices(currentOfficePage, officesPerPage, getOfficeFilters());
        // officesData = response.data;
        // totalOfficePages = response.totalPages;
        
        // استخدام البيانات الوهمية للآن
        await new Promise(resolve => setTimeout(resolve, 800));
        officesData = mockOfficesData;
        totalOfficePages = Math.ceil(officesData.length / officesPerPage);
        
        displayOffices();
        updateOfficePagination();
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات المكاتب:', error);
        showNotification('خطأ في تحميل بيانات المكاتب', 'error');
    } finally {
        showLoading(false);
    }
}

// عرض المكاتب في الجدول
function displayOffices() {
    const tbody = document.querySelector('#officesTable tbody');
    if (!tbody) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentOfficePage - 1) * officesPerPage;
    const endIndex = startIndex + officesPerPage;
    const currentOffices = officesData.slice(startIndex, endIndex);
    
    tbody.innerHTML = '';
    
    currentOffices.forEach(office => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="office-code">
                    <strong>${office.office_code}</strong>
                    ${office.is_featured ? '<span class="featured-badge">مميز</span>' : ''}
                </div>
            </td>
            <td>
                <div class="office-info">
                    <strong>${office.name_ar}</strong>
                    <small>${office.name_en}</small>
                </div>
            </td>
            <td>
                <span class="emirate-badge emirate-${office.emirate.toLowerCase().replace(' ', '-')}">
                    ${office.emirate}
                </span>
            </td>
            <td>${office.area || office.city}</td>
            <td>
                <span class="office-type-badge type-${office.office_type.replace('_', '-')}">
                    ${getOfficeTypeText(office.office_type)}
                </span>
            </td>
            <td>
                <div class="cars-count">
                    <strong>${office.current_cars_count}/${office.capacity}</strong>
                    <div class="capacity-bar">
                        <div class="capacity-fill" style="width: ${(office.current_cars_count / office.capacity) * 100}%"></div>
                    </div>
                </div>
            </td>
            <td>
                <div class="rating-display">
                    ${generateStarRating(Math.round(office.rating))}
                    <span class="rating-number">${office.rating.toFixed(1)}</span>
                </div>
            </td>
            <td>
                <span class="status-badge status-${office.status}">
                    ${getOfficeStatusText(office.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon" onclick="viewOfficeDetails('${office.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon btn-primary" onclick="editOffice('${office.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-success" onclick="viewOfficeCars('${office.id}')" title="السيارات">
                        <i class="fas fa-car"></i>
                    </button>
                    <button class="btn-icon ${office.status === 'active' ? 'btn-warning' : 'btn-success'}" 
                            onclick="toggleOfficeStatus('${office.id}')" 
                            title="${office.status === 'active' ? 'تعطيل' : 'تفعيل'}">
                        <i class="fas ${office.status === 'active' ? 'fa-pause' : 'fa-play'}"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteOffice('${office.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // تحديث معلومات العدد
    updateOfficeCount();
}

// ربط أحداث المكاتب
function bindOfficeEvents() {
    // فلترة المكاتب
    const emirateFilter = document.getElementById('emirateFilter');
    const officeTypeFilter = document.getElementById('officeTypeFilter');
    const statusFilter = document.getElementById('statusFilter');
    const searchInput = document.getElementById('searchOffices');
    
    if (emirateFilter) {
        emirateFilter.addEventListener('change', filterOffices);
    }
    
    if (officeTypeFilter) {
        officeTypeFilter.addEventListener('change', filterOffices);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', filterOffices);
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterOffices, 300));
    }
    
    // تصدير البيانات
    const exportBtn = document.getElementById('exportOffices');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportOffices);
    }
    
    // تحديث البيانات
    const refreshBtn = document.getElementById('refreshOffices');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            currentOfficePage = 1;
            loadOfficesData();
        });
    }
}

// فلترة المكاتب
function filterOffices() {
    const emirateFilter = document.getElementById('emirateFilter')?.value || '';
    const typeFilter = document.getElementById('officeTypeFilter')?.value || '';
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const searchText = document.getElementById('searchOffices')?.value.toLowerCase() || '';
    
    let filteredData = mockOfficesData.filter(office => {
        // فلترة بالإمارة
        if (emirateFilter && office.emirate !== emirateFilter) {
            return false;
        }
        
        // فلترة بنوع المكتب
        if (typeFilter && office.office_type !== typeFilter) {
            return false;
        }
        
        // فلترة بالحالة
        if (statusFilter && office.status !== statusFilter) {
            return false;
        }
        
        // فلترة بالبحث النصي
        if (searchText) {
            const searchableText = `${office.office_code} ${office.name} ${office.name_ar} ${office.name_en} ${office.area} ${office.city}`.toLowerCase();
            if (!searchableText.includes(searchText)) {
                return false;
            }
        }
        
        return true;
    });
    
    officesData = filteredData;
    totalOfficePages = Math.ceil(officesData.length / officesPerPage);
    currentOfficePage = 1;
    
    displayOffices();
    updateOfficePagination();
    updateOfficeStats();
}

// عرض موديل إضافة مكتب جديد
function showAddOfficeModal() {
    // إعادة تعيين النموذج
    document.getElementById('addOfficeForm').reset();
    
    showModal('addOfficeModal');
}

// إرسال بيانات المكتب الجديد
function submitOffice(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const officeData = Object.fromEntries(formData.entries());
    
    // التحقق من البيانات
    if (!validateOfficeData(officeData)) {
        return;
    }
    
    // محاكاة حفظ البيانات
    showLoading(true);
    
    setTimeout(() => {
        const newOffice = {
            id: generateUUID(),
            ...officeData,
            current_cars_count: 0,
            rating: 0,
            total_ratings: 0,
            is_active: true,
            status: 'active',
            created_at: new Date(),
            updated_at: new Date()
        };
        
        mockOfficesData.unshift(newOffice);
        
        showLoading(false);
        hideModal('addOfficeModal');
        
        showNotification('تم إضافة المكتب بنجاح', 'success');
        
        // تحديث العرض
        loadOfficesData();
        
    }, 2000);
}

// التحقق من صحة بيانات المكتب
function validateOfficeData(data) {
    if (!data.office_code || data.office_code.length < 3) {
        showNotification('رمز المكتب يجب أن يكون 3 أحرف على الأقل', 'error');
        return false;
    }
    
    if (!data.name || !data.name_ar || !data.name_en) {
        showNotification('جميع أسماء المكتب مطلوبة', 'error');
        return false;
    }
    
    if (!data.phone || !data.email) {
        showNotification('رقم الهاتف والبريد الإلكتروني مطلوبان', 'error');
        return false;
    }
    
    if (!data.emirate || !data.city || !data.address) {
        showNotification('معلومات الموقع مطلوبة', 'error');
        return false;
    }
    
    if (!data.latitude || !data.longitude) {
        showNotification('الإحداثيات الجغرافية مطلوبة', 'error');
        return false;
    }
    
    // التحقق من تفرد رمز المكتب
    if (mockOfficesData.some(office => office.office_code === data.office_code)) {
        showNotification('رمز المكتب موجود بالفعل', 'error');
        return false;
    }
    
    return true;
}

// عرض تفاصيل المكتب
function viewOfficeDetails(officeId) {
    const office = mockOfficesData.find(o => o.id === officeId);
    if (!office) {
        showNotification('لم يتم العثور على بيانات المكتب', 'error');
        return;
    }
    
    const detailsContainer = document.getElementById('officeDetailsContent');
    detailsContainer.innerHTML = `
        <div class="office-details">
            <div class="details-section">
                <h3>المعلومات الأساسية</h3>
                <div class="details-grid">
                    <div class="detail-item">
                        <label>رمز المكتب:</label>
                        <span>${office.office_code}</span>
                    </div>
                    <div class="detail-item">
                        <label>اسم المكتب (عربي):</label>
                        <span>${office.name_ar}</span>
                    </div>
                    <div class="detail-item">
                        <label>اسم المكتب (انجليزي):</label>
                        <span>${office.name_en}</span>
                    </div>
                    <div class="detail-item">
                        <label>نوع المكتب:</label>
                        <span class="office-type-badge type-${office.office_type.replace('_', '-')}">
                            ${getOfficeTypeText(office.office_type)}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="details-section">
                <h3>معلومات الاتصال</h3>
                <div class="details-grid">
                    <div class="detail-item">
                        <label>الهاتف:</label>
                        <span>${office.phone}</span>
                    </div>
                    <div class="detail-item">
                        <label>البريد الإلكتروني:</label>
                        <span>${office.email}</span>
                    </div>
                    <div class="detail-item">
                        <label>واتساب:</label>
                        <span>${office.whatsapp || 'غير متوفر'}</span>
                    </div>
                    <div class="detail-item">
                        <label>المدير:</label>
                        <span>${office.manager_name} - ${office.manager_phone}</span>
                    </div>
                </div>
            </div>
            
            <div class="details-section">
                <h3>الموقع</h3>
                <div class="details-grid">
                    <div class="detail-item full-width">
                        <label>العنوان:</label>
                        <span>${office.address_ar}</span>
                    </div>
                    <div class="detail-item">
                        <label>الإمارة:</label>
                        <span>${office.emirate}</span>
                    </div>
                    <div class="detail-item">
                        <label>المدينة:</label>
                        <span>${office.city}</span>
                    </div>
                    <div class="detail-item">
                        <label>المنطقة:</label>
                        <span>${office.area || 'غير محدد'}</span>
                    </div>
                    <div class="detail-item">
                        <label>الإحداثيات:</label>
                        <span>${office.latitude}, ${office.longitude}</span>
                    </div>
                </div>
            </div>
            
            <div class="details-section">
                <h3>معلومات التشغيل</h3>
                <div class="details-grid">
                    <div class="detail-item">
                        <label>السعة:</label>
                        <span>${office.capacity} سيارة</span>
                    </div>
                    <div class="detail-item">
                        <label>السيارات الحالية:</label>
                        <span>${office.current_cars_count} سيارة</span>
                    </div>
                    <div class="detail-item">
                        <label>عدد الموظفين:</label>
                        <span>${office.staff_count} موظف</span>
                    </div>
                    <div class="detail-item">
                        <label>التقييم:</label>
                        <div class="rating-display">
                            ${generateStarRating(Math.round(office.rating))}
                            <span class="rating-number">${office.rating.toFixed(1)} (${office.total_ratings} تقييم)</span>
                        </div>
                    </div>
                </div>
                
                <div class="services-badges">
                    <h4>الخدمات المتاحة:</h4>
                    ${office.services.map(service => 
                        `<span class="service-badge">${getServiceName(service)}</span>`
                    ).join('')}
                </div>
                
                <div class="facilities-badges">
                    <h4>المرافق:</h4>
                    ${office.facilities.map(facility => 
                        `<span class="facility-badge">${getFacilityName(facility)}</span>`
                    ).join('')}
                </div>
            </div>
            
            <div class="details-section">
                <h3>ساعات العمل</h3>
                <div class="working-hours">
                    ${Object.entries(office.working_hours).map(([day, hours]) => `
                        <div class="day-hours">
                            <span class="day-name">${getDayName(day)}:</span>
                            <span class="hours ${hours.is_open ? 'open' : 'closed'}">
                                ${hours.is_open ? `${hours.open} - ${hours.close}` : 'مغلق'}
                            </span>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
    
    showModal('officeDetailsModal');
}

// تعديل المكتب
function editOffice(officeId) {
    const office = mockOfficesData.find(o => o.id === officeId);
    if (!office) {
        showNotification('لم يتم العثور على بيانات المكتب', 'error');
        return;
    }
    
    // ملء النموذج ببيانات المكتب
    // سيتم تنفيذ هذا لاحقاً
    showNotification('ميزة التعديل قيد التطوير', 'info');
}

// عرض سيارات المكتب
function viewOfficeCars(officeId) {
    const office = mockOfficesData.find(o => o.id === officeId);
    if (!office) {
        showNotification('لم يتم العثور على بيانات المكتب', 'error');
        return;
    }
    
    showNotification(`عرض سيارات مكتب ${office.name_ar} (${office.current_cars_count} سيارة)`, 'info');
    // يمكن توجيه المستخدم إلى صفحة السيارات مع فلترة بالمكتب
}

// تبديل حالة المكتب
function toggleOfficeStatus(officeId) {
    const office = mockOfficesData.find(o => o.id === officeId);
    if (!office) return;
    
    const newStatus = office.status === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'تفعيل' : 'تعطيل';
    
    if (confirm(`هل أنت متأكد من ${action} هذا المكتب؟`)) {
        office.status = newStatus;
        office.is_active = newStatus === 'active';
        office.updated_at = new Date();
        
        showNotification(`تم ${action} المكتب بنجاح`, 'success');
        displayOffices();
        updateOfficeStats();
    }
}

// حذف المكتب
function deleteOffice(officeId) {
    const office = mockOfficesData.find(o => o.id === officeId);
    if (!office) return;
    
    if (!confirm(`هل أنت متأكد من حذف مكتب "${office.name_ar}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
        return;
    }
    
    const index = mockOfficesData.findIndex(o => o.id === officeId);
    if (index !== -1) {
        mockOfficesData.splice(index, 1);
        showNotification('تم حذف المكتب بنجاح', 'success');
        
        // إعادة تحديث البيانات
        filterOffices();
    }
}

// تصدير المكاتب
function exportOffices() {
    showLoading(true);
    
    setTimeout(() => {
        const csvContent = generateOfficesCSV(officesData);
        downloadCSV(csvContent, 'offices_export.csv');
        showLoading(false);
        showNotification('تم تصدير البيانات بنجاح', 'success');
    }, 1500);
}

// إنشاء محتوى CSV للمكاتب
function generateOfficesCSV(offices) {
    const headers = [
        'رمز المكتب',
        'الاسم بالعربية',
        'الاسم بالإنجليزية',
        'النوع',
        'الإمارة',
        'المدينة',
        'المنطقة',
        'الهاتف',
        'البريد الإلكتروني',
        'العنوان',
        'السعة',
        'السيارات الحالية',
        'التقييم',
        'الحالة',
        'تاريخ الإنشاء'
    ];
    
    let csvContent = headers.join(',') + '\n';
    
    offices.forEach(office => {
        const row = [
            office.office_code,
            `"${office.name_ar}"`,
            `"${office.name_en}"`,
            getOfficeTypeText(office.office_type),
            office.emirate,
            office.city,
            office.area || '',
            office.phone,
            office.email,
            `"${office.address}"`,
            office.capacity,
            office.current_cars_count,
            office.rating.toFixed(1),
            getOfficeStatusText(office.status),
            formatDate(office.created_at)
        ];
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

// تحديث إحصائيات المكاتب
function updateOfficeStats() {
    const stats = calculateOfficeStats(officesData);
    
    const totalOfficesEl = document.getElementById('totalOffices');
    const activeOfficesEl = document.getElementById('activeOffices');
    const totalOfficeCarsEl = document.getElementById('totalOfficeCars');
    const coverageAreasEl = document.getElementById('coverageAreas');
    
    if (totalOfficesEl) {
        totalOfficesEl.textContent = stats.total;
    }
    
    if (activeOfficesEl) {
        activeOfficesEl.textContent = stats.active;
    }
    
    if (totalOfficeCarsEl) {
        totalOfficeCarsEl.textContent = stats.totalCars;
    }
    
    if (coverageAreasEl) {
        coverageAreasEl.textContent = stats.coverageAreas;
    }
}

// حساب إحصائيات المكاتب
function calculateOfficeStats(offices) {
    const stats = {
        total: offices.length,
        active: 0,
        inactive: 0,
        totalCars: 0,
        totalCapacity: 0,
        coverageAreas: 0,
        emirates: new Set(),
        averageRating: 0
    };
    
    let totalRating = 0;
    let ratedOffices = 0;
    const areas = new Set();
    
    offices.forEach(office => {
        if (office.status === 'active') {
            stats.active++;
        } else {
            stats.inactive++;
        }
        
        stats.totalCars += office.current_cars_count;
        stats.totalCapacity += office.capacity;
        stats.emirates.add(office.emirate);
        areas.add(`${office.emirate}-${office.city}-${office.area}`);
        
        if (office.rating > 0) {
            totalRating += office.rating;
            ratedOffices++;
        }
    });
    
    stats.coverageAreas = areas.size;
    stats.averageRating = ratedOffices > 0 ? totalRating / ratedOffices : 0;
    
    return stats;
}

// تحديث عدد المكاتب
function updateOfficeCount() {
    const countElement = document.getElementById('officesCount');
    if (countElement) {
        const startIndex = (currentOfficePage - 1) * officesPerPage + 1;
        const endIndex = Math.min(currentOfficePage * officesPerPage, officesData.length);
        countElement.textContent = `عرض ${startIndex}-${endIndex} من ${officesData.length} مكتب`;
    }
}

// دوال مساعدة للمكاتب
function getOfficeTypeText(type) {
    const types = {
        'main': 'مكتب رئيسي',
        'branch': 'فرع',
        'pickup_point': 'نقطة استلام',
        'service_center': 'مركز خدمة'
    };
    
    return types[type] || type;
}

function getOfficeStatusText(status) {
    const statuses = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'maintenance': 'صيانة',
        'closed': 'مغلق'
    };
    
    return statuses[status] || status;
}

function getServiceName(service) {
    const services = {
        'cleaning': 'تنظيف',
        'maintenance': 'صيانة',
        'inspection': 'فحص',
        'fuel': 'تزويد وقود'
    };
    
    return services[service] || service;
}

function getFacilityName(facility) {
    const facilities = {
        'parking': 'موقف سيارات',
        'wifi': 'واي فاي',
        'waiting_area': 'منطقة انتظار',
        'restroom': 'دورات مياه',
        'cafe': 'مقهى'
    };
    
    return facilities[facility] || facility;
}

function getDayName(day) {
    const days = {
        'saturday': 'السبت',
        'sunday': 'الأحد',
        'monday': 'الاثنين',
        'tuesday': 'الثلاثاء',
        'wednesday': 'الأربعاء',
        'thursday': 'الخميس',
        'friday': 'الجمعة'
    };
    
    return days[day] || day;
}

// التنقل بين الصفحات
function goToOfficePage(page) {
    if (page >= 1 && page <= totalOfficePages) {
        currentOfficePage = page;
        displayOffices();
        updateOfficePagination();
    }
}

function updateOfficePagination() {
    const paginationContainer = document.querySelector('#officesPagination .pagination');
    if (!paginationContainer) return;
    
    let paginationHTML = '';
    
    // زر السابق
    paginationHTML += `
        <button class="page-btn ${currentOfficePage === 1 ? 'disabled' : ''}" 
                onclick="goToOfficePage(${currentOfficePage - 1})" 
                ${currentOfficePage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // أرقام الصفحات
    const startPage = Math.max(1, currentOfficePage - 2);
    const endPage = Math.min(totalOfficePages, currentOfficePage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="goToOfficePage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="page-dots">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="page-btn ${i === currentOfficePage ? 'active' : ''}" 
                    onclick="goToOfficePage(${i})">
                ${i}
            </button>
        `;
    }
    
    if (endPage < totalOfficePages) {
        if (endPage < totalOfficePages - 1) {
            paginationHTML += `<span class="page-dots">...</span>`;
        }
        paginationHTML += `<button class="page-btn" onclick="goToOfficePage(${totalOfficePages})">${totalOfficePages}</button>`;
    }
    
    // زر التالي
    paginationHTML += `
        <button class="page-btn ${currentOfficePage === totalOfficePages ? 'disabled' : ''}" 
                onclick="goToOfficePage(${currentOfficePage + 1})" 
                ${currentOfficePage === totalOfficePages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// تصدير الوظائف للاستخدام العام
window.initializeOffices = initializeOffices;
window.showAddOfficeModal = showAddOfficeModal;
window.submitOffice = submitOffice;
window.viewOfficeDetails = viewOfficeDetails;
window.editOffice = editOffice;
window.viewOfficeCars = viewOfficeCars;
window.toggleOfficeStatus = toggleOfficeStatus;
window.deleteOffice = deleteOffice;
window.exportOffices = exportOffices;
window.filterOffices = filterOffices;
window.refreshOffices = () => {
    currentOfficePage = 1;
    loadOfficesData();
};
window.goToOfficePage = goToOfficePage;
