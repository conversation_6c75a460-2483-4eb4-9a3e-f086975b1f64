import 'car.dart';

enum BookingStatus {
  pending,
  confirmed,
  active,
  completed,
  cancelled,
  rejected,
}

class Booking {
  final String id;
  final String userId;
  final String carId;
  final Car? car;
  final DateTime pickupDate;
  final DateTime dropoffDate;
  final String pickupLocation;
  final String dropoffLocation;
  final double totalPrice;
  final double deposit;
  final double taxes;
  final double serviceFee;
  final BookingStatus status;
  final String? paymentIntentId;
  final String? paymentMethodId;
  final Map<String, dynamic>? additionalServices;
  final List<String>? driverLicenseImages;
  final String? signature;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? rentalAgreement;
  final bool needsDelivery;
  final String? deliveryAddress;
  final double? deliveryFee;

  Booking({
    required this.id,
    required this.userId,
    required this.carId,
    this.car,
    required this.pickupDate,
    required this.dropoffDate,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.totalPrice,
    required this.deposit,
    required this.taxes,
    required this.serviceFee,
    required this.status,
    this.paymentIntentId,
    this.paymentMethodId,
    this.additionalServices,
    this.driverLicenseImages,
    this.signature,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.rentalAgreement,
    this.needsDelivery = false,
    this.deliveryAddress,
    this.deliveryFee,
  });

  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      carId: json['car_id'] ?? '',
      car: json['car'] != null ? Car.fromJson(json['car']) : null,
      pickupDate: DateTime.parse(json['pickup_date']),
      dropoffDate: DateTime.parse(json['dropoff_date']),
      pickupLocation: json['pickup_location'] ?? '',
      dropoffLocation: json['dropoff_location'] ?? '',
      totalPrice: (json['total_price'] ?? 0).toDouble(),
      deposit: (json['deposit'] ?? 0).toDouble(),
      taxes: (json['taxes'] ?? 0).toDouble(),
      serviceFee: (json['service_fee'] ?? 0).toDouble(),
      status: BookingStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => BookingStatus.pending,
      ),
      paymentIntentId: json['payment_intent_id'],
      paymentMethodId: json['payment_method_id'],
      additionalServices: json['additional_services'],
      driverLicenseImages: json['driver_license_images'] != null
          ? List<String>.from(json['driver_license_images'])
          : null,
      signature: json['signature'],
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      rentalAgreement: json['rental_agreement'],
      needsDelivery: json['needs_delivery'] ?? false,
      deliveryAddress: json['delivery_address'],
      deliveryFee: json['delivery_fee']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'car_id': carId,
      'car': car?.toJson(),
      'pickup_date': pickupDate.toIso8601String(),
      'dropoff_date': dropoffDate.toIso8601String(),
      'pickup_location': pickupLocation,
      'dropoff_location': dropoffLocation,
      'total_price': totalPrice,
      'deposit': deposit,
      'taxes': taxes,
      'service_fee': serviceFee,
      'status': status.name,
      'payment_intent_id': paymentIntentId,
      'payment_method_id': paymentMethodId,
      'additional_services': additionalServices,
      'driver_license_images': driverLicenseImages,
      'signature': signature,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'rental_agreement': rentalAgreement,
      'needs_delivery': needsDelivery,
      'delivery_address': deliveryAddress,
      'delivery_fee': deliveryFee,
    };
  }

  Duration get rentalDuration {
    return dropoffDate.difference(pickupDate);
  }

  int get rentalDays {
    final duration = rentalDuration;
    return duration.inDays == 0 ? 1 : duration.inDays;
  }

  double get basePrice {
    return totalPrice - taxes - serviceFee - (deliveryFee ?? 0);
  }

  bool get isActive {
    return status == BookingStatus.active || status == BookingStatus.confirmed;
  }

  bool get canBeCancelled {
    return status == BookingStatus.pending || status == BookingStatus.confirmed;
  }

  bool get isCompleted {
    return status == BookingStatus.completed;
  }

  bool get isCancelled {
    return status == BookingStatus.cancelled ||
        status == BookingStatus.rejected;
  }

  Booking copyWith({
    String? id,
    String? userId,
    String? carId,
    Car? car,
    DateTime? pickupDate,
    DateTime? dropoffDate,
    String? pickupLocation,
    String? dropoffLocation,
    double? totalPrice,
    double? deposit,
    double? taxes,
    double? serviceFee,
    BookingStatus? status,
    String? paymentIntentId,
    String? paymentMethodId,
    Map<String, dynamic>? additionalServices,
    List<String>? driverLicenseImages,
    String? signature,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? rentalAgreement,
    bool? needsDelivery,
    String? deliveryAddress,
    double? deliveryFee,
  }) {
    return Booking(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      carId: carId ?? this.carId,
      car: car ?? this.car,
      pickupDate: pickupDate ?? this.pickupDate,
      dropoffDate: dropoffDate ?? this.dropoffDate,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      totalPrice: totalPrice ?? this.totalPrice,
      deposit: deposit ?? this.deposit,
      taxes: taxes ?? this.taxes,
      serviceFee: serviceFee ?? this.serviceFee,
      status: status ?? this.status,
      paymentIntentId: paymentIntentId ?? this.paymentIntentId,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      additionalServices: additionalServices ?? this.additionalServices,
      driverLicenseImages: driverLicenseImages ?? this.driverLicenseImages,
      signature: signature ?? this.signature,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rentalAgreement: rentalAgreement ?? this.rentalAgreement,
      needsDelivery: needsDelivery ?? this.needsDelivery,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      deliveryFee: deliveryFee ?? this.deliveryFee,
    );
  }

  @override
  String toString() {
    return 'Booking(id: $id, carId: $carId, status: $status, totalPrice: $totalPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Booking && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Additional getters for compatibility
  String get bookingNumber => 'BK-${id.substring(0, 8).toUpperCase()}';
  DateTime get returnDate => dropoffDate;
  String get pickupAddress => pickupLocation;
  double get totalAmount => totalPrice + deposit + taxes + (deliveryFee ?? 0);
}
