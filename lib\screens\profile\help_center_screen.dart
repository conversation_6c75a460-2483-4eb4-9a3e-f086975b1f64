import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../constants/app_colors.dart';

class HelpCenterScreen extends StatelessWidget {
  const HelpCenterScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'مركز المساعدة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primaryDark],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.help_center,
                    size: 64,
                    color: Colors.white,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'كيف يمكننا مساعدتك؟',
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'نحن هنا لمساعدتك في أي وقت',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),

            // Contact Options
            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildContactCard(
                    icon: Icons.phone,
                    title: 'اتصل بنا',
                    subtitle: 'متاح 24/7',
                    details: '+971 4 123 4567',
                    color: Colors.green,
                    onTap: () => _launchPhone('+97141234567'),
                  ),

                  SizedBox(height: 12),

                  _buildContactCard(
                    icon: Icons.chat,
                    title: 'واتساب',
                    subtitle: 'رد سريع',
                    details: '+971 50 123 4567',
                    color: Colors.teal,
                    onTap: () => _launchWhatsApp('+971501234567'),
                  ),

                  SizedBox(height: 12),

                  _buildContactCard(
                    icon: Icons.email,
                    title: 'بريد إلكتروني',
                    subtitle: 'استجابة خلال 24 ساعة',
                    details: '<EMAIL>',
                    color: Colors.blue,
                    onTap: () => _launchEmail('<EMAIL>'),
                  ),

                  SizedBox(height: 24),

                  // FAQ Section
                  _buildFAQSection(context), SizedBox(height: 24),

                  // Quick Help Section
                  _buildQuickHelpSection(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required String details,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    details,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.onSurfaceVariant,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.quiz, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'الأسئلة الشائعة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          _buildFAQItem(
            question: 'كيف يمكنني حجز سيارة؟',
            answer:
                'يمكنك حجز سيارة من خلال تطبيقنا بسهولة. اختر السيارة المطلوبة، حدد التواريخ، وأكمل عملية الدفع.',
          ),
          _buildFAQItem(
            question: 'ما هي الوثائق المطلوبة؟',
            answer:
                'تحتاج إلى رخصة قيادة سارية المفعول، هوية الإمارات أو جواز السفر، وبطاقة ائتمانية.',
          ),
          _buildFAQItem(
            question: 'كيف يمكنني إلغاء الحجز؟',
            answer:
                'يمكنك إلغاء الحجز من خلال قسم "حجوزاتي" في التطبيق. شروط الإلغاء تختلف حسب نوع الحجز.',
          ),
          SizedBox(height: 12),
          TextButton(
            onPressed: () {
              // TODO: Navigate to full FAQ page
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('صفحة الأسئلة الشائعة الكاملة ستكون متاحة قريباً'),
                ),
              );
            },
            child: Text(
              'عرض جميع الأسئلة',
              style: GoogleFonts.cairo(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem({required String question, required String answer}) {
    return ExpansionTile(
      title: Text(
        question,
        style: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppColors.onSurface,
        ),
      ),
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 16, left: 16, right: 16),
          child: Text(
            answer,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppColors.onSurfaceVariant,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickHelpSection() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flash_on, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'مساعدة سريعة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.location_on,
                  title: 'مواقع الاستلام',
                  onTap: () {},
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.payment,
                  title: 'طرق الدفع',
                  onTap: () {},
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.schedule,
                  title: 'ساعات العمل',
                  onTap: () {},
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.policy,
                  title: 'السياسات',
                  onTap: () {},
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickHelpItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.primary.withOpacity(0.1)),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: AppColors.primary,
              size: 32,
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppColors.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _launchPhone(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  void _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=استفسار عن تطبيق دبي لتأجير السيارات',
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _launchWhatsApp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse(
        'https://wa.me/$phoneNumber?text=مرحباً، أحتاج مساعدة في تطبيق دبي لتأجير السيارات');
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }
}
