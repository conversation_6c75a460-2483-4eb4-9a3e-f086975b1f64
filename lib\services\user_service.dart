import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';

class UserService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Create user profile
  Future<UserModel> createUserProfile({
    required String userId,
    required String email,
    required String fullName,
    String? profileImageUrl,
  }) async {
    try {
      print('Creating user profile for: $email');

      // استخدام المصنع الجديد
      final newUser = UserModel.create(
        id: userId,
        email: email,
        fullName: fullName,
        profileImageUrl: profileImageUrl,
      );

      print('User data to insert: ${newUser.toJson()}');

      final response = await _supabase
          .from('profiles')
          .insert(newUser.toJson())
          .select()
          .single();

      print('User profile created successfully: ${response['id']}');
      return UserModel.fromJson(response);
    } catch (e) {
      print('Error creating user profile: $e');
      throw Exception('Failed to create user profile: $e');
    }
  }

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final response =
          await _supabase.from('profiles').select().eq('id', userId).single();

      return UserModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Update user profile
  Future<UserModel> updateUserProfile(UserModel user) async {
    try {
      final updateData = user.toJson();
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('profiles')
          .update(updateData)
          .eq('id', user.id)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  // Update specific user fields
  Future<UserModel> updateUserFields(
      String userId, Map<String, dynamic> updates) async {
    try {
      updates['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('profiles')
          .update(updates)
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update user fields: $e');
    }
  }

  // Delete user account
  Future<void> deleteUserAccount(String userId) async {
    try {
      await _supabase.from('profiles').delete().eq('id', userId);
    } catch (e) {
      throw Exception('Failed to delete user account: $e');
    }
  }

  // Verify email
  Future<UserModel> verifyEmail(String userId) async {
    try {
      final response = await _supabase
          .from('profiles')
          .update({
            'is_email_verified': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to verify email: $e');
    }
  }

  // Verify phone
  Future<UserModel> verifyPhone(String userId) async {
    try {
      final response = await _supabase
          .from('profiles')
          .update({
            'is_phone_verified': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to verify phone: $e');
    }
  }

  // Verify license
  Future<UserModel> verifyLicense(String userId) async {
    try {
      final response = await _supabase
          .from('profiles')
          .update({
            'is_license_verified': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to verify license: $e');
    }
  }

  // Update profile completion status
  Future<UserModel> updateProfileCompletionStatus(String userId) async {
    try {
      final user = await getUserById(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      // Check if profile is complete
      final isComplete = _isProfileComplete(user);

      final response = await _supabase
          .from('profiles')
          .update({
            'is_profile_complete': isComplete,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update profile completion status: $e');
    }
  }

  // Check if profile is complete
  bool _isProfileComplete(UserModel user) {
    return user.fullName != null &&
        user.fullName!.isNotEmpty &&
        user.phone != null &&
        user.phone!.isNotEmpty &&
        user.dateOfBirth != null &&
        user.address != null &&
        user.address!.isNotEmpty &&
        user.isEmailVerified &&
        user.isPhoneVerified &&
        user.licenseImageUrl != null;
  }

  // Search users (for admin purposes)
  Future<List<UserModel>> searchUsers({
    String? query,
    UserStatus? status,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      var queryBuilder = _supabase.from('profiles').select();

      if (query != null && query.isNotEmpty) {
        queryBuilder = queryBuilder.ilike('full_name', '%$query%');
      }

      if (status != null) {
        queryBuilder = queryBuilder.eq('status', status.name);
      }

      final response = await queryBuilder.range(offset, offset + limit - 1);

      return (response as List<dynamic>)
          .map((json) => UserModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStatistics(String userId) async {
    try {
      final user = await getUserById(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      // Get booking count from bookings table
      final bookingCountResponse =
          await _supabase.from('bookings').select('*').eq('user_id', userId);

      return {
        'total_bookings': (bookingCountResponse as List).length,
        'member_since': user.createdAt.toIso8601String(),
        'profile_completion': _calculateProfileCompletion(user),
        'verification_status': {
          'email': user.isEmailVerified,
          'phone': user.isPhoneVerified,
          'license': user.isLicenseVerified,
        },
      };
    } catch (e) {
      throw Exception('Failed to get user statistics: $e');
    }
  }

  // Calculate profile completion percentage
  double _calculateProfileCompletion(UserModel user) {
    int completedFields = 0;
    int totalFields = 10;

    if (user.fullName != null && user.fullName!.isNotEmpty) completedFields++;
    if (user.phone != null && user.phone!.isNotEmpty) completedFields++;
    if (user.dateOfBirth != null) completedFields++;
    if (user.address != null && user.address!.isNotEmpty) completedFields++;
    if (user.profileImageUrl != null) completedFields++;
    if (user.licenseImageUrl != null) completedFields++;
    if (user.isEmailVerified) completedFields++;
    if (user.isPhoneVerified) completedFields++;
    if (user.emergencyContactName != null &&
        user.emergencyContactName!.isNotEmpty) completedFields++;
    if (user.emergencyContactPhone != null &&
        user.emergencyContactPhone!.isNotEmpty) completedFields++;

    return (completedFields / totalFields) * 100;
  }
}
