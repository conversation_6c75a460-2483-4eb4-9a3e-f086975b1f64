import 'package:flutter/foundation.dart';
import '../models/car.dart';

class CarProvider with ChangeNotifier {
  List<Car> _cars = [];
  List<Car> _filteredCars = [];
  bool _isLoading = false;
  String? _error;
  Car? _selectedCar;
  String _searchQuery = '';
  String _selectedCategory = '';
  String _sortBy = 'name';
  bool _sortAscending = true;
  double _minPrice = 0;
  double _maxPrice = 1000;

  // Getters
  List<Car> get cars => _cars;
  List<Car> get filteredCars =>
      _filteredCars.isEmpty && _searchQuery.isEmpty && _selectedCategory.isEmpty
          ? _cars
          : _filteredCars;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Car? get selectedCar => _selectedCar;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  String get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;

  List<String> get categories {
    final categories = <String>{};
    for (final car in _cars) {
      categories.add(car.category);
    }
    return categories.toList()..sort();
  }

  List<String> get brands {
    final brands = <String>{};
    for (final car in _cars) {
      brands.add(car.brand);
    }
    return brands.toList()..sort();
  }

  // Methods
  Future<void> loadCars() async {
    _setLoading(true);
    try {
      // Simulate loading cars from API
      await Future.delayed(const Duration(seconds: 1));

      // Mock data - replace with actual API call
      _cars = _generateMockCars();
      _filterAndSortCars();
      _error = null;
    } catch (e) {
      _error = e.toString();
    }
    _setLoading(false);
  }

  Future<void> refreshCars() async {
    await loadCars();
  }

  void setSelectedCar(Car car) {
    _selectedCar = car;
    notifyListeners();
  }

  void clearSelectedCar() {
    _selectedCar = null;
    notifyListeners();
  }

  void searchCars(String query) {
    _searchQuery = query.toLowerCase();
    _filterAndSortCars();
    notifyListeners();
  }

  void filterByCategory(String category) {
    _selectedCategory = category;
    _filterAndSortCars();
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _selectedCategory = '';
    _minPrice = 0;
    _maxPrice = 1000;
    _filterAndSortCars();
    notifyListeners();
  }

  void sortCars(String sortBy, {bool ascending = true}) {
    _sortBy = sortBy;
    _sortAscending = ascending;
    _filterAndSortCars();
    notifyListeners();
  }

  void setPriceRange(double min, double max) {
    _minPrice = min;
    _maxPrice = max;
    _filterAndSortCars();
    notifyListeners();
  }

  Future<Car?> getCarById(String id) async {
    try {
      return _cars.firstWhere((car) => car.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<List<Car>> getSimilarCars(Car car) async {
    return _cars
        .where((c) =>
            c.id != car.id &&
            (c.category == car.category || c.brand == car.brand))
        .take(4)
        .toList();
  }

  Future<List<Car>> getPopularCars() async {
    return _cars.where((car) => car.rating >= 4.0).toList()
      ..sort((a, b) => b.rating.compareTo(a.rating));
  }

  Future<List<Car>> getFeaturedCars() async {
    return _cars
        .where((car) => car.features.contains('Premium'))
        .take(6)
        .toList();
  }

  void _filterAndSortCars() {
    var filtered = List<Car>.from(_cars);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((car) =>
              car.name.toLowerCase().contains(_searchQuery) ||
              car.brand.toLowerCase().contains(_searchQuery) ||
              car.model.toLowerCase().contains(_searchQuery) ||
              car.category.toLowerCase().contains(_searchQuery))
          .toList();
    }

    // Apply category filter
    if (_selectedCategory.isNotEmpty) {
      filtered =
          filtered.where((car) => car.category == _selectedCategory).toList();
    }

    // Apply price filter
    filtered = filtered
        .where((car) =>
            car.pricePerDay >= _minPrice && car.pricePerDay <= _maxPrice)
        .toList();

    // Apply sorting
    switch (_sortBy) {
      case 'name':
        filtered.sort((a, b) => _sortAscending
            ? a.name.compareTo(b.name)
            : b.name.compareTo(a.name));
        break;
      case 'price':
        filtered.sort((a, b) => _sortAscending
            ? a.pricePerDay.compareTo(b.pricePerDay)
            : b.pricePerDay.compareTo(a.pricePerDay));
        break;
      case 'rating':
        filtered.sort((a, b) => _sortAscending
            ? a.rating.compareTo(b.rating)
            : b.rating.compareTo(a.rating));
        break;
      case 'year':
        filtered.sort((a, b) => _sortAscending
            ? a.year.compareTo(b.year)
            : b.year.compareTo(a.year));
        break;
    }

    _filteredCars = filtered;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  List<Car> _generateMockCars() {
    return [
      Car(
        id: '1',
        name: 'BMW 3 Series',
        brand: 'BMW',
        model: '330i',
        year: 2023,
        category: 'Luxury',
        pricePerDay: 250.0,
        rating: 4.8,
        reviewCount: 45,
        images: [
          'https://example.com/bmw1.jpg',
          'https://example.com/bmw2.jpg',
        ],
        features: ['Premium', 'GPS', 'Bluetooth', 'Air Conditioning'],
        specifications: {
          'engine': '2.0L Turbo',
          'horsepower': '255 HP',
          'topSpeed': '250 km/h',
        },
        isAvailable: true,
        description: 'Luxury sedan with premium features',
        location: 'Dubai Marina',
        fuelType: 'Petrol',
        transmission: 'Automatic',
        seats: 5,
        doors: 4,
        color: 'White',
        plateNumber: 'D-12345',
      ),
      Car(
        id: '2',
        name: 'Toyota Camry',
        brand: 'Toyota',
        model: 'Camry',
        year: 2022,
        category: 'Economy',
        pricePerDay: 120.0,
        rating: 4.5,
        reviewCount: 89,
        images: [
          'https://example.com/toyota1.jpg',
          'https://example.com/toyota2.jpg',
        ],
        features: ['GPS', 'Bluetooth', 'Air Conditioning', 'Backup Camera'],
        specifications: {
          'engine': '2.5L',
          'horsepower': '203 HP',
          'topSpeed': '200 km/h',
        },
        isAvailable: true,
        description: 'Reliable and economical sedan',
        location: 'Dubai Mall',
        fuelType: 'Petrol',
        transmission: 'Automatic',
        seats: 5,
        doors: 4,
        color: 'Silver',
        plateNumber: 'D-67890',
      ),
    ];
  }

  // Favorites functionality
  final List<String> _favoriteCarIds = [];

  List<String> get favoriteCarIds => List.unmodifiable(_favoriteCarIds);

  Future<bool> isCarFavorite(String carId) async {
    return _favoriteCarIds.contains(carId);
  }

  Future<bool> toggleFavorite(String carId) async {
    try {
      if (_favoriteCarIds.contains(carId)) {
        _favoriteCarIds.remove(carId);
      } else {
        _favoriteCarIds.add(carId);
      }
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Failed to toggle favorite: $e';
      notifyListeners();
      return false;
    }
  }

  List<Car> get favoriteCars {
    return _cars.where((car) => _favoriteCarIds.contains(car.id)).toList();
  }
}
