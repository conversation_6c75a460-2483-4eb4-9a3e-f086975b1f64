name: dubai_car_rental
description: تطبيق تأجير السيارات الفاخرة في دبي - Premium Car Rental App in Dubai

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Backend & Database
  supabase_flutter: ^2.0.0
  
  # State Management
  provider: ^6.1.1
  
  # UI Components & Fonts
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  
  # Maps & Location
  flutter_map: ^6.1.0
  latlong2: ^0.9.1
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  permission_handler: ^11.0.1
  
  # Authentication
  google_sign_in: ^6.1.6
  
  # Payment - تحديث إصدار Stripe
  flutter_stripe: ^9.5.0
  
  # Signature & Documents - تحديث الإصدارات
  signature: ^5.4.0
  image_picker: ^1.0.4
  
  # Image & File handling
  cached_network_image: ^3.3.0
  
  # HTTP & Storage
  http: ^1.1.0
  path_provider: ^2.1.1
  flutter_secure_storage: ^9.0.0
  
  # Date & Time
  intl: 0.19.0
  
  # Utilities
  uuid: ^4.1.0
  url_launcher: ^6.2.1
  share_plus: ^7.2.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  
  # Animations & UI
  lottie: ^2.7.0
  shimmer: ^3.0.0
  
  # PDF Generation & Documents
  pdf: ^3.10.7
  printing: ^5.11.1
  
  # Notifications - استخدام إشعارات محلية فقط مع Supabase
  flutter_local_notifications: ^16.3.0
  
  # Connectivity & Network
  connectivity_plus: ^5.0.2
  
  # Device Info
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  
  # Biometric Authentication
  local_auth: ^2.1.7
  
  # QR Code
  qr_flutter: ^4.1.0
  
  # Rating & Reviews
  flutter_rating_bar: ^4.0.1
  
  # Calendar & Date Picker
  table_calendar: ^3.0.9
  
  # Video Player (إزالة مؤقتة لتجنب المشاكل)
  # video_player: ^2.8.1
  
  # WebView
  webview_flutter: ^4.4.2
  equatable: ^2.0.7

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  generate: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/logos/

flutter_intl:
  enabled: true
  arb_dir: lib/l10n
  output_dir: lib/l10n/generated
  main_locale: ar

# إعدادات flutter_gen
flutter_gen:
  output: lib/gen/
  line_length: 80
  integrations:
    flutter_svg: false
    flare_flutter: false
    rive: false
    lottie: false