import 'app_localizations.dart';

class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr() : super('ar');

  @override
  String get appTitle => 'تأجير السيارات دبي';

  @override
  String get welcome => 'مرحباً';

  @override
  String get welcomeBack => 'مرحباً بعودتك';

  @override
  String get hello => 'أهلاً';

  @override
  String get user => 'مستخدم';

  @override
  String get findYourPerfectCar => 'ابحث عن سيارتك المثالية';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'تسجيل حساب جديد';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get joinUs => 'انضم إلينا';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get phone => 'الهاتف';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get dateOfBirth => 'تاريخ الميلاد';

  @override
  String get nationality => 'الجنسية';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get loginWithGoogle => 'تسجيل الدخول بجوجل';

  @override
  String get signUpWithGoogle => 'تسجيل حساب جديد بجوجل';

  @override
  String get orContinueWith => 'أو المتابعة بـ';

  @override
  String get orSignUpWith => 'أو التسجيل بـ';

  @override
  String get dontHaveAccount => 'لا تملك حساباً؟';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get signUp => 'تسجيل حساب جديد';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get home => 'الرئيسية';

  @override
  String get cars => 'السيارات';

  @override
  String get bookings => 'الحجوزات';

  @override
  String get profile => 'الحساب';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get cancel => 'إلغاء';

  @override
  String get ok => 'موافق';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get update => 'تحديث';

  @override
  String get loading => 'جاري التحميل';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get clear => 'مسح';

  @override
  String get apply => 'تطبيق';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get skip => 'تخطي';

  @override
  String get continue_ => 'متابعة';

  @override
  String get finish => 'إنهاء';

  @override
  String get done => 'تم';

  @override
  String get close => 'إغلاق';

  @override
  String get open => 'فتح';

  @override
  String get view => 'عرض';

  @override
  String get details => 'التفاصيل';

  @override
  String get more => 'أكثر';

  @override
  String get less => 'أقل';

  @override
  String get showAll => 'عرض الكل';

  @override
  String get showLess => 'عرض أقل';
}
