// ملف JavaScript لإدارة لوحة التحكم الرئيسية والإحصائيات

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
    try {
        showLoading();
        
        // جلب البيانات من قاعدة البيانات
        await updateDashboardStats();
        await initializeCharts();
        await loadRecentActivities();
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        showNotification('حدث خطأ في تحميل بيانات لوحة التحكم', 'error');
    } finally {
        hideLoading();
    }
}

// تحديث الإحصائيات من قاعدة البيانات
async function updateDashboardStats() {
    try {
        // جلب التواريخ للفترة الحالية والسابقة
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        const previousStartDate = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        
        // جلب الإحصائيات الحالية
        const currentStats = await fetchDashboardStats(startDate, endDate);
        
        // جلب الإحصائيات السابقة للمقارنة
        const previousStats = await fetchDashboardStats(previousStartDate, startDate);
        
        // تحديث الأرقام مع تأثير العد
        animateCounter('totalCars', currentStats.totalCars || 0);
        animateCounter('totalBookings', currentStats.totalBookings || 0);
        animateCounter('totalRevenue', currentStats.totalRevenue || 0);
        animateCounter('totalUsers', currentStats.totalUsers || 0);
        
        // حساب وتحديث النسب المئوية للتغيير
        updateStatChanges(currentStats, previousStats);
        
        // حفظ البيانات للاستخدام في الرسوم البيانية
        window.dashboardStats = currentStats;
        
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
        
        // عرض بيانات افتراضية في حالة الخطأ
        const fallbackStats = {
            totalCars: 0,
            totalBookings: 0,
            totalRevenue: 0,
            totalUsers: 0
        };
        
        animateCounter('totalCars', fallbackStats.totalCars);
        animateCounter('totalBookings', fallbackStats.totalBookings);
        animateCounter('totalRevenue', fallbackStats.totalRevenue);
        animateCounter('totalUsers', fallbackStats.totalUsers);
        
        throw error;
    }
}

// تحريك العد
function animateCounter(elementId, targetValue) {
    const element = document.getElementById(elementId);
    const startValue = 0;
    const duration = 1500;
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        
        // استخدام easing function للحصول على تأثير سلس
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);
        
        if (elementId === 'totalRevenue') {
            element.textContent = formatNumber(currentValue);
        } else {
            element.textContent = formatNumber(currentValue);
        }
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

// تحديث نسب التغيير بناءً على البيانات الحقيقية
function updateStatChanges(currentStats, previousStats) {
    const changes = {
        carsChange: calculatePercentageChange(previousStats?.totalCars || 0, currentStats?.totalCars || 0),
        bookingsChange: calculatePercentageChange(previousStats?.totalBookings || 0, currentStats?.totalBookings || 0),
        revenueChange: calculatePercentageChange(previousStats?.totalRevenue || 0, currentStats?.totalRevenue || 0),
        usersChange: calculatePercentageChange(previousStats?.totalUsers || 0, currentStats?.totalUsers || 0)
    };
    
    Object.keys(changes).forEach(changeId => {
        const element = document.getElementById(changeId);
        if (element) {
            const changeValue = changes[changeId];
            element.textContent = changeValue;
            
            // تحديد اللون بناءً على الزيادة أو النقصان
            if (changeValue.startsWith('+')) {
                element.className = 'stat-change positive';
            } else if (changeValue.startsWith('-')) {
                element.className = 'stat-change negative';
            } else {
                element.className = 'stat-change neutral';
            }
        }
        });
}

// رسم بياني احتياطي للحجوزات
function initializeFallbackBookingsChart() {
    const ctx = document.getElementById('bookingsChart').getContext('2d');
    
    const fallbackData = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
        datasets: [{
            label: 'الحجوزات المؤكدة',
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    };
    
    if (!window.charts) {
        window.charts = {};
    }
    
    window.charts.bookingsChart = new Chart(ctx, {
        type: 'line',
        data: fallbackData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// حساب النسبة المئوية للتغيير
function calculatePercentageChange(oldValue, newValue) {
    if (oldValue === 0) {
        return newValue > 0 ? '+100%' : '0%';
    }
    
    const change = ((newValue - oldValue) / oldValue) * 100;
    const sign = change >= 0 ? '+' : '';
    return `${sign}${Math.round(change)}%`;
}

// تهيئة الرسوم البيانية
async function initializeCharts() {
    try {
        await initializeBookingsChart();
        await initializeCarsChart();
    } catch (error) {
        console.error('خطأ في تهيئة الرسوم البيانية:', error);
        showNotification('حدث خطأ في تحميل الرسوم البيانية', 'warning');
    }
}

// رسم بياني للحجوزات
async function initializeBookingsChart() {
    try {
        const ctx = document.getElementById('bookingsChart').getContext('2d');
        
        // جلب بيانات الحجوزات الشهرية
        const bookingsData = await fetchMonthlyBookingsData();
        
        const chartData = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
            label: 'الحجوزات المؤكدة',
            data: bookingsData.confirmed || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: 'rgba(102, 126, 234, 1)',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6
        }, {
            label: 'الحجوزات الملغاة',
            data: bookingsData.cancelled || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            backgroundColor: 'rgba(245, 87, 108, 0.1)',
            borderColor: 'rgba(245, 87, 108, 1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: 'rgba(245, 87, 108, 1)',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6
        }]
        };
        
        if (window.charts && window.charts.bookingsChart) {
            window.charts.bookingsChart.destroy();
        }
        
        if (!window.charts) {
            window.charts = {};
        }
        
        window.charts.bookingsChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        padding: 20,
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleFont: {
                        family: 'Cairo'
                    },
                    bodyFont: {
                        family: 'Cairo'
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });
    } catch (error) {
        console.error('خطأ في تهيئة رسم الحجوزات البياني:', error);
        // عرض رسم بياني فارغ في حالة الخطأ
        initializeFallbackBookingsChart();
    }
}

// رسم بياني دائري للسيارات
async function initializeCarsChart() {
    try {
        const ctx = document.getElementById('carsChart').getContext('2d');
        
        // جلب بيانات السيارات حسب الفئة
        const carsData = await fetchCarsCategoryData();
        
        const chartData = {
            labels: ['اقتصادية', 'عادية', 'متميزة', 'فاخرة', 'خارقة'],
            datasets: [{
                data: carsData.counts || [0, 0, 0, 0, 0],
                backgroundColor: [
                    '#4facfe',
                    '#43e97b',
                    '#f093fb',
                    '#667eea',
                    '#764ba2'
                ],
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#fff'
            }]
        };
        
        if (!window.charts) {
            window.charts = {};
        }
        
        window.charts.carsChart = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        font: {
                            family: 'Cairo',
                            size: 12
                        },
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleFont: {
                        family: 'Cairo'
                    },
                    bodyFont: {
                        family: 'Cairo'
                    },
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.formattedValue;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%',
            elements: {
                arc: {
                    borderWidth: 0
                }
            }
        }
    });
    } catch (error) {
        console.error('خطأ في تهيئة رسم السيارات البياني:', error);
        
        // إنشاء رسم بياني احتياطي في حالة الخطأ
        const ctx = document.getElementById('carsChart');
        if (ctx) {
            if (!window.charts) {
                window.charts = {};
            }
            
            window.charts.carsChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['اقتصادية', 'عادية', 'متميزة', 'فاخرة', 'خارقة'],
                    datasets: [{
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: [
                            '#4facfe',
                            '#43e97b',
                            '#f093fb',
                            '#667eea',
                            '#764ba2'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                font: {
                                    family: 'Cairo',
                                    size: 12
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }
    }
}

// تحديث رسم الحجوزات بناءً على الفترة المختارة
function updateBookingsChart() {
    const period = document.getElementById('bookingsChartPeriod').value;
    let newData, newLabels;
    
    switch(period) {
        case 'month':
            newLabels = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'];
            newData = [
                [8, 12, 10, 15],
                [2, 3, 2, 4]
            ];
            break;
        case 'quarter':
            newLabels = ['الشهر 1', 'الشهر 2', 'الشهر 3'];
            newData = [
                [45, 52, 48],
                [8, 10, 9]
            ];
            break;
        case 'year':
            newLabels = ['الربع 1', 'الربع 2', 'الربع 3', 'الربع 4'];
            newData = [
                [120, 145, 135, 160],
                [25, 30, 28, 35]
            ];
            break;
    }
    
    charts.bookingsChart.data.labels = newLabels;
    charts.bookingsChart.data.datasets[0].data = newData[0];
    charts.bookingsChart.data.datasets[1].data = newData[1];
    charts.bookingsChart.update();
}

// تحميل الأنشطة الأخيرة
async function loadRecentActivities() {
    try {
        const activities = [];
        
        // جلب أحدث الحجوزات
        const recentBookings = await fetchBookings(1, 3, {});
        recentBookings.data?.forEach(booking => {
            activities.push({
                type: 'booking',
                title: 'حجز جديد',
                description: `حجز جديد - ${booking.booking_number}`,
                time: formatTimeAgo(booking.created_at),
                created_at: booking.created_at
            });
        });
        
        // جلب أحدث المستخدمين
        const recentUsers = await fetchUsers(1, 2, {});
        recentUsers.data?.forEach(user => {
            activities.push({
                type: 'user',
                title: 'مستخدم جديد',
                description: `انضم ${user.full_name || 'غير محدد'} إلى التطبيق`,
                time: formatTimeAgo(user.created_at),
                created_at: user.created_at
            });
        });
        
        // ترتيب الأنشطة حسب التاريخ
        activities.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        const activitiesList = document.getElementById('recentActivities');
        if (activitiesList) {
            if (activities.length > 0) {
                activitiesList.innerHTML = activities.slice(0, 5).map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon ${activity.type}">
                            <i class="fas ${getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-content">
                            <h4>${activity.title}</h4>
                            <p>${activity.description}</p>
                        </div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                `).join('');
            } else {
                activitiesList.innerHTML = '<p class="no-activities">لا توجد أنشطة حديثة</p>';
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل الأنشطة الحديثة:', error);
        
        // عرض رسالة خطأ
        const activitiesList = document.getElementById('recentActivities');
        if (activitiesList) {
            activitiesList.innerHTML = '<p class="error-message">حدث خطأ في تحميل الأنشطة</p>';
        }
    }
}

// تنسيق الوقت المنقضي
function formatTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return `منذ ${diffInSeconds} ثانية`;
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}

// الحصول على أيقونة النشاط
function getActivityIcon(type) {
    const icons = {
        booking: 'fa-calendar-check',
        payment: 'fa-credit-card',
        review: 'fa-star',
        user: 'fa-user-plus'
    };
    return icons[type] || 'fa-info-circle';
}

// تحديث لوحة التحكم بناءً على التواريخ المختارة
function updateDashboard() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
        return;
    }
    
    showLoading();
    
    // محاكاة تحميل البيانات الجديدة
    setTimeout(() => {
        updateDashboardStats();
        updateChartsData();
        loadRecentActivities();
        hideLoading();
        showNotification('تم تحديث البيانات بنجاح', 'success');
    }, 1500);
}

// جلب بيانات الحجوزات الشهرية
async function fetchMonthlyBookingsData() {
    try {
        const currentYear = new Date().getFullYear();
        const monthlyData = { confirmed: [], cancelled: [] };
        
        for (let month = 0; month < 12; month++) {
            const startDate = new Date(currentYear, month, 1).toISOString().split('T')[0];
            const endDate = new Date(currentYear, month + 1, 0).toISOString().split('T')[0];
            
            // جلب الحجوزات المؤكدة
            const confirmedBookings = await fetchBookings(1, 1000, {
                status: 'confirmed',
                start_date: startDate,
                end_date: endDate
            });
            
            // جلب الحجوزات الملغاة
            const cancelledBookings = await fetchBookings(1, 1000, {
                status: 'cancelled',
                start_date: startDate,
                end_date: endDate
            });
            
            monthlyData.confirmed.push(confirmedBookings.total || 0);
            monthlyData.cancelled.push(cancelledBookings.total || 0);
        }
        
        return monthlyData;
    } catch (error) {
        console.error('خطأ في جلب بيانات الحجوزات الشهرية:', error);
        return {
            confirmed: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            cancelled: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        };
    }
}

// جلب بيانات السيارات حسب الفئة
async function fetchCarsCategoryData() {
    try {
        const categories = ['economy', 'standard', 'premium', 'luxury', 'supercar'];
        const counts = [];
        
        for (const category of categories) {
            const carsData = await fetchCars(1, 1000, { category });
            counts.push(carsData.total || 0);
        }
        
        return { counts };
    } catch (error) {
        console.error('خطأ في جلب بيانات السيارات حسب الفئة:', error);
        return { counts: [0, 0, 0, 0, 0] };
    }
}

// إنشاء رسم بياني احتياطي للحجوزات
function initializeFallbackBookingsChart() {
    const ctx = document.getElementById('bookingsChart');
    if (!ctx) return;
    
    // تدمير الرسم البياني الموجود إن وجد
    if (window.charts && window.charts.bookingsChart) {
        window.charts.bookingsChart.destroy();
    }
    
    if (!window.charts) {
        window.charts = {};
    }
    
    window.charts.bookingsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'الحجوزات المؤكدة',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'الحجوزات الملغاة',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                title: {
                    display: true,
                    text: 'إحصائيات الحجوزات الشهرية (لا توجد بيانات)',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// تحديث بيانات الرسوم البيانية
async function updateChartsData() {
    try {
        // تحديث رسم الحجوزات
        if (window.charts && window.charts.bookingsChart) {
            const monthlyData = await fetchMonthlyBookingsData();
            window.charts.bookingsChart.data.datasets[0].data = monthlyData.confirmed;
            window.charts.bookingsChart.data.datasets[1].data = monthlyData.cancelled;
            window.charts.bookingsChart.update();
        }
        
        // تحديث رسم السيارات
        if (window.charts && window.charts.carsChart) {
            const categoryData = await fetchCarsCategoryData();
            window.charts.carsChart.data.datasets[0].data = categoryData.counts;
            window.charts.carsChart.update();
        }
    } catch (error) {
        console.error('خطأ في تحديث بيانات الرسوم البيانية:', error);
        // استخدام بيانات احتياطية في حالة الخطأ
        if (window.charts && window.charts.bookingsChart) {
            const fallbackBookingsData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
            window.charts.bookingsChart.data.datasets[0].data = fallbackBookingsData;
            window.charts.bookingsChart.data.datasets[1].data = fallbackBookingsData;
            window.charts.bookingsChart.update();
        }
        
        if (window.charts && window.charts.carsChart) {
            const fallbackCarsData = [0, 0, 0, 0, 0];
            window.charts.carsChart.data.datasets[0].data = fallbackCarsData;
            window.charts.carsChart.update();
        }
    }
}

// تحديث البيانات كل فترة زمنية
function startDashboardRefresh() {
    setInterval(async () => {
        if (currentSection === 'dashboard') {
            try {
                await updateDashboardStats();
                await updateChartsData();
            } catch (error) {
                console.error('خطأ في التحديث التلقائي للوحة التحكم:', error);
            }
        }
    }, 30000); // كل 30 ثانية
}

// إنشاء رسم بياني مخصص للإيرادات
function createRevenueChart(canvasId) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(74, 172, 254, 0.4)');
    gradient.addColorStop(1, 'rgba(74, 172, 254, 0.0)');
    
    const revenueData = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'الإيرادات (د.إ)',
            data: [12000, 19000, 15000, 25000, 22000, 30000],
            backgroundColor: gradient,
            borderColor: '#4facfe',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#4facfe',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6
        }]
    };
    
    return new Chart(ctx, {
        type: 'line',
        data: revenueData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleFont: {
                        family: 'Cairo'
                    },
                    bodyFont: {
                        family: 'Cairo'
                    },
                    callbacks: {
                        label: function(context) {
                            return `الإيرادات: ${formatCurrency(context.parsed.y)}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        },
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

// تصدير إحصائيات لوحة التحكم
function exportDashboardData() {
    showLoading();
    
    const dashboardData = {
        stats: {
            totalCars: document.getElementById('totalCars').textContent,
            totalBookings: document.getElementById('totalBookings').textContent,
            totalRevenue: document.getElementById('totalRevenue').textContent,
            totalUsers: document.getElementById('totalUsers').textContent
        },
        period: {
            startDate: document.getElementById('startDate').value,
            endDate: document.getElementById('endDate').value
        },
        exportDate: new Date().toISOString()
    };
    
    // محاكاة تصدير البيانات
    setTimeout(() => {
        hideLoading();
        
        // إنشاء وتنزيل الملف
        const blob = new Blob([JSON.stringify(dashboardData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dashboard-stats-${formatDate(new Date())}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('تم تصدير إحصائيات لوحة التحكم بنجاح', 'success');
    }, 1000);
}

// طباعة تقرير لوحة التحكم
function printDashboard() {
    const printWindow = window.open('', '_blank');
    const dashboardContent = document.getElementById('dashboard').innerHTML;
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير لوحة التحكم - ${new Date().toLocaleDateString('ar-AE')}</title>
            <style>
                body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; }
                .stats-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 2rem; }
                .stat-card { border: 1px solid #ddd; padding: 1rem; border-radius: 8px; }
                .chart-container { margin-bottom: 2rem; }
                .no-print { display: none; }
                @media print {
                    .section-actions, .filters-row { display: none; }
                }
            </style>
        </head>
        <body>
            <h1>تقرير لوحة التحكم</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-AE')}</p>
            ${dashboardContent}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}
