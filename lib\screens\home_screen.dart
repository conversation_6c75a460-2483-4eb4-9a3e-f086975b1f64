import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../providers/auth_provider.dart';
import '../providers/language_provider.dart';
import '../services/car_service.dart';
import '../models/car_model.dart';
import '../widgets/car_card.dart';
import '../widgets/professional_app_bar.dart';
import '../constants/app_colors.dart';
import 'cars_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final CarService _carService = CarService();
  final ScrollController _scrollController = ScrollController();

  List<CarModel> _featuredCars = [];
  List<CarModel> _allCars = [];
  Map<String, int> _categories = {};
  bool _isLoading = true;

  // Dubai coordinates for map
  final LatLng _dubaiCenter = const LatLng(25.2048, 55.2708);
  List<Marker> _mapMarkers = [];

  @override
  void initState() {
    super.initState();
    _loadData();
    _initializeMapMarkers();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Load all required data
  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      print('🔄 جاري تحميل بيانات الصفحة الرئيسية...');

      // Test database connection
      final hasConnection = await _carService.checkDatabaseConnection();
      print('📡 حالة الاتصال: $hasConnection');

      if (!hasConnection) {
        print(
            '⚠️ لا يوجد اتصال بقاعدة البيانات - محاولة إضافة بيانات تجريبية...');
        final dataAdded = await _carService.addSampleDataIfEmpty();
        print('📊 حالة إضافة البيانات التجريبية: $dataAdded');
        await Future.delayed(const Duration(seconds: 2));

        // إعادة فحص الاتصال بعد إضافة البيانات
        final newConnection = await _carService.checkDatabaseConnection();
        print('📡 حالة الاتصال بعد إضافة البيانات: $newConnection');
      }

      // Load featured cars - استخدام قاعدة البيانات فقط
      print('🔄 جلب السيارات المميزة من قاعدة البيانات...');
      final featuredCars = await _carService.getFeaturedCars(limit: 6);
      print('📊 تم جلب ${featuredCars.length} سيارة مميزة');

      // Load categories - استخدام قاعدة البيانات فقط
      print('🔄 جلب فئات السيارات من قاعدة البيانات...');
      final categories = await _carService.getPopularCategories();
      print('📊 تم جلب ${categories.length} فئة');

      // Load all cars for stats - استخدام قاعدة البيانات فقط
      print('🔄 جلب جميع السيارات للإحصائيات...');
      final allCars = await _carService.getCars(limit: 50);
      print('📊 تم جلب ${allCars.length} سيارة للإحصائيات');

      if (mounted) {
        setState(() {
          // استخدام البيانات من قاعدة البيانات فقط
          _featuredCars = featuredCars;
          _allCars = allCars;
          _categories = categories;
          _isLoading = false;
        });

        // رسالة تأكيد جلب البيانات من قاعدة البيانات
        print('✅ تم تحميل البيانات الحقيقية من قاعدة البيانات بنجاح!');
        print('📈 السيارات المميزة: ${featuredCars.length}');
        print('📈 إجمالي السيارات: ${allCars.length}');
        print('📈 الفئات: ${categories.length}');

        // رسالة واضحة للمستخدم
        if (featuredCars.isEmpty && allCars.isEmpty && categories.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                '⚠️ قاعدة البيانات فارغة - لا توجد سيارات',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'إضافة بيانات تجريبية',
                textColor: Colors.white,
                onPressed: () async {
                  await _carService.addSampleDataIfEmpty();
                  _loadData();
                },
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '✅ تم جلب البيانات من قاعدة البيانات: ${featuredCars.length} سيارات',
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      print('❌ خطأ في تحميل البيانات من قاعدة البيانات: $e');
      if (mounted) {
        setState(() {
          // في حالة الخطأ، اعرض رسالة للمستخدم
          _featuredCars = [];
          _allCars = [];
          _categories = {};
          _isLoading = false;
        });

        // عرض رسالة خطأ للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى.',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadData(),
            ),
          ),
        );
      }
    }
  }

  // Initialize map markers for Dubai locations
  void _initializeMapMarkers() {
    final locations = [
      {'name': 'مطار دبي الدولي', 'lat': 25.2532, 'lng': 55.3657, 'cars': 25},
      {'name': 'دبي مول', 'lat': 25.1972, 'lng': 55.2796, 'cars': 18},
      {'name': 'دبي مارينا', 'lat': 25.0757, 'lng': 55.1395, 'cars': 15},
      {'name': 'برج العرب', 'lat': 25.1413, 'lng': 55.1853, 'cars': 12},
      {'name': 'وسط البلد', 'lat': 25.1938, 'lng': 55.2744, 'cars': 20},
    ];

    _mapMarkers = locations.map((location) {
      return Marker(
        point: LatLng(location['lat'] as double, location['lng'] as double),
        width: 40,
        height: 40,
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: const Icon(
            Icons.directions_car,
            color: Colors.white,
            size: 20,
          ),
        ),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: ProfessionalAppBar(
        title: authProvider.isAuthenticated
            ? 'مرحباً ${authProvider.currentUser?.email?.split('@')[0] ?? "بك"}!'
            : null,
        subtitle: 'اكتشف أفضل السيارات في دبي',
        showLogo: true,
        onSearchPressed: () => _navigateToSearch(),
        onNotificationPressed: () => _showNotifications(),
        onLogoPressed: () => _showAppInfo(),
        notificationCount: 3, // يمكن تغييرها حسب الحاجة
      ),
      body: _isLoading
          ? _buildLoadingScreen()
          : _featuredCars.isEmpty && _allCars.isEmpty
              ? _buildNoDataScreen()
              : CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    _buildWelcomeBanner(context, authProvider, isArabic),
                    _buildQuickStats(context, isArabic),
                    _buildMapSection(context, isArabic),
                    _buildCategoriesSection(context, isArabic),
                    _buildFeaturedCarsSection(context, isArabic),
                    _buildFooterSection(context, isArabic),
                  ],
                ),
    );
  }

  // Loading screen
  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primary),
          const SizedBox(height: 20),
          Text(
            'جاري تحميل البيانات من قاعدة البيانات...',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // No data screen
  Widget _buildNoDataScreen() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.storage,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 20),
            Text(
              'لا توجد بيانات في قاعدة البيانات',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'يرجى التأكد من وجود بيانات السيارات في قاعدة البيانات',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () => _loadData(),
              icon: const Icon(Icons.refresh, color: Colors.white),
              label: Text(
                'إعادة تحميل البيانات',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Welcome Banner
  Widget _buildWelcomeBanner(
      BuildContext context, AuthProvider authProvider, bool isArabic) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.primary, AppColors.secondary],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    authProvider.isAuthenticated
                        ? 'مرحباً ${authProvider.currentUser?.email ?? "بك"}!'
                        : 'مرحباً بك في تأجير السيارات',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: (_featuredCars.isNotEmpty || _allCars.isNotEmpty)
                        ? Colors.green.withOpacity(0.8)
                        : Colors.red.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white.withOpacity(0.3)),
                  ),
                  child: Text(
                    (_featuredCars.isNotEmpty || _allCars.isNotEmpty)
                        ? '🗄️ متصل'
                        : '⚠️ غير متصل',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'استكشف أفضل السيارات المتاحة للإيجار في دبي',
              style: GoogleFonts.cairo(
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _navigateToAllCars(),
              icon: Icon(Icons.car_rental, color: AppColors.primary),
              label: Text(
                'تصفح السيارات',
                style: GoogleFonts.cairo(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Quick Stats
  Widget _buildQuickStats(BuildContext context, bool isArabic) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final screenWidth = constraints.maxWidth;

            if (screenWidth < 400) {
              // للشاشات الصغيرة، نعرض البطاقات بتخطيط مختلف
              return Column(
                children: [
                  // البطاقة الأولى منفردة
                  SizedBox(
                    height: 75, // ارتفاع محدد لمنع الـ Overflow
                    child: _buildStatCard('${_allCars.length}+', 'سيارة متاحة',
                        Icons.directions_car, true),
                  ),
                  const SizedBox(height: 10),
                  // البطاقتان الأخريان جنباً إلى جنب
                  SizedBox(
                    height: 85, // ارتفاع محدد لمنع الـ Overflow
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildStatCard('${_categories.length}+',
                              'فئة متنوعة', Icons.category, false),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: _buildStatCard('24/7', 'خدمة العملاء',
                              Icons.support_agent, false),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            } else if (screenWidth < 600) {
              // للتابلت الصغير
              return SizedBox(
                height: 90, // ارتفاع محدد لمنع الـ Overflow
                child: Row(
                  children: [
                    Expanded(
                      child: _buildStatCard('${_allCars.length}+',
                          'سيارة متاحة', Icons.directions_car, false),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: _buildStatCard('${_categories.length}+',
                          'فئة متنوعة', Icons.category, false),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: _buildStatCard(
                          '24/7', 'خدمة العملاء', Icons.support_agent, false),
                    ),
                  ],
                ),
              );
            } else {
              // للشاشات الكبيرة
              return SizedBox(
                height: 95, // ارتفاع محدد لمنع الـ Overflow
                child: Row(
                  children: [
                    Expanded(
                      child: _buildStatCard('${_allCars.length}+',
                          'سيارة متاحة', Icons.directions_car, false),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard('${_categories.length}+',
                          'فئة متنوعة', Icons.category, false),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                          '24/7', 'خدمة العملاء', Icons.support_agent, false),
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildStatCard(
      String value, String label, IconData icon, bool isWide) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;

        // حساب الأبعاد المتجاوبة مع حل مشكلة Overflow
        final double cardPadding = screenWidth < 400 ? 8.0 : 12.0;
        final double iconSize = screenWidth < 400 ? 18.0 : 22.0;
        final double valueSize = screenWidth < 400
            ? 14.0
            : isWide
                ? 18.0
                : 16.0;
        final double labelSize = screenWidth < 400
            ? 10.0
            : isWide
                ? 12.0
                : 11.0;
        final double iconContainerSize = screenWidth < 400 ? 8.0 : 10.0;

        return Container(
          // ارتفاع محدود لمنع الـ Overflow
          height: isWide
              ? (screenWidth < 400 ? 75.0 : 85.0)
              : (screenWidth < 400 ? 85.0 : 95.0),
          constraints: BoxConstraints(
            minHeight: screenWidth < 400 ? 70.0 : 80.0,
            maxHeight: screenWidth < 400 ? 85.0 : 100.0,
          ),
          padding: EdgeInsets.all(cardPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: isWide
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(iconContainerSize),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child:
                          Icon(icon, color: AppColors.primary, size: iconSize),
                    ),
                    SizedBox(width: screenWidth < 400 ? 8 : 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              value,
                              style: GoogleFonts.cairo(
                                fontSize: valueSize,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(height: screenWidth < 400 ? 2 : 3),
                          Flexible(
                            child: Text(
                              label,
                              style: GoogleFonts.cairo(
                                fontSize: labelSize,
                                color: Colors.grey[600],
                                height: 1.2,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      flex: 2,
                      child:
                          Icon(icon, color: AppColors.primary, size: iconSize),
                    ),
                    SizedBox(height: screenWidth < 400 ? 4 : 6),
                    Flexible(
                      flex: 1,
                      child: Text(
                        value,
                        style: GoogleFonts.cairo(
                          fontSize: valueSize,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(height: screenWidth < 400 ? 2 : 3),
                    Flexible(
                      flex: 1,
                      child: Text(
                        label,
                        style: GoogleFonts.cairo(
                          fontSize: labelSize,
                          color: Colors.grey[600],
                          height: 1.1,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
        );
      },
    );
  }

  // Map Section
  Widget _buildMapSection(BuildContext context, bool isArabic) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'مواقع التأجير في دبي',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _showFullMap(),
                  child: Text(
                    'عرض الخريطة كاملة',
                    style: GoogleFonts.cairo(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: FlutterMap(
                  options: MapOptions(
                    initialCenter: _dubaiCenter,
                    initialZoom: 11.0,
                    interactionOptions: const InteractionOptions(
                      flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag,
                    ),
                  ),
                  children: [
                    TileLayer(
                      urlTemplate:
                          'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                      userAgentPackageName: 'com.dubaicarrental.app',
                    ),
                    MarkerLayer(markers: _mapMarkers),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Categories Section
  Widget _buildCategoriesSection(BuildContext context, bool isArabic) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'فئات السيارات',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _navigateToAllCars(),
                  child: Text(
                    'عرض الكل',
                    style: GoogleFonts.cairo(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LayoutBuilder(
              builder: (context, constraints) {
                final screenWidth = constraints.maxWidth;
                final cardWidth = screenWidth < 400 ? 100.0 : 120.0;
                final cardHeight =
                    screenWidth < 400 ? 80.0 : 90.0; // ارتفاع أقل

                return SizedBox(
                  height: cardHeight,
                  child: _categories.isEmpty
                      ? Center(
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            child: Text(
                              'لا توجد فئات متاحة',
                              style: GoogleFonts.cairo(
                                color: Colors.grey[500],
                                fontSize: screenWidth < 400 ? 12 : 14,
                              ),
                            ),
                          ),
                        )
                      : ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding:
                              EdgeInsets.only(right: screenWidth < 400 ? 8 : 0),
                          itemCount: _categories.length,
                          itemBuilder: (context, index) {
                            final category = _categories.keys.elementAt(index);
                            final count = _categories[category]!;
                            return _buildCategoryCard(
                                category, count, cardWidth, screenWidth);
                          },
                        ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(
      String category, int count, double cardWidth, double screenWidth) {
    return Container(
      width: cardWidth,
      height: screenWidth < 400 ? 80 : 90, // ارتفاع مقلل لمنع الـ Overflow
      constraints: BoxConstraints(
        minHeight: screenWidth < 400 ? 75 : 85,
        maxHeight: screenWidth < 400 ? 85 : 95,
      ),
      margin: EdgeInsets.only(right: screenWidth < 400 ? 8 : 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _navigateToCategory(category),
          child: Padding(
            padding: EdgeInsets.all(screenWidth < 400 ? 6 : 10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.all(screenWidth < 400 ? 6 : 8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getCategoryIcon(category),
                      color: AppColors.primary,
                      size: screenWidth < 400 ? 18 : 20, // أيقونة أصغر
                    ),
                  ),
                ),
                SizedBox(height: screenWidth < 400 ? 3 : 4),
                Flexible(
                  flex: 1,
                  child: Text(
                    category,
                    style: GoogleFonts.cairo(
                      fontSize: screenWidth < 400 ? 10 : 12,
                      fontWeight: FontWeight.w600,
                      height: 1.0,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: screenWidth < 400 ? 2 : 3),
                Flexible(
                  flex: 1,
                  child: Text(
                    '$count سيارة',
                    style: GoogleFonts.cairo(
                      fontSize: screenWidth < 400 ? 8 : 10,
                      color: Colors.grey[600],
                      height: 1.0,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Featured Cars Section
  Widget _buildFeaturedCarsSection(BuildContext context, bool isArabic) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final screenWidth = constraints.maxWidth;

            // حساب عدد الأعمدة والنسب بناءً على عرض الشاشة
            int crossAxisCount;
            double crossAxisSpacing;

            if (screenWidth < 400) {
              // الجوال: عمود واحد
              crossAxisCount = 1;
              crossAxisSpacing = 0;
            } else if (screenWidth < 600) {
              // التابلت الصغير: عمودان
              crossAxisCount = 2;
              crossAxisSpacing = 12;
            } else if (screenWidth < 900) {
              // التابلت الكبير: عمودان
              crossAxisCount = 2;
              crossAxisSpacing = 16;
            } else {
              // سطح المكتب: ثلاثة أعمدة
              crossAxisCount = 3;
              crossAxisSpacing = 20;
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'السيارات المميزة',
                      style: GoogleFonts.cairo(
                        fontSize: screenWidth < 400 ? 18 : 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _featuredCars.isNotEmpty
                                ? Colors.green
                                : Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _featuredCars.isNotEmpty
                                ? '🗄️ من قاعدة البيانات'
                                : '⚠️ لا توجد بيانات',
                            style: GoogleFonts.cairo(
                              color: Colors.white,
                              fontSize: screenWidth < 400 ? 9 : 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        TextButton(
                          onPressed: () => _navigateToAllCars(),
                          child: Text(
                            'عرض الكل',
                            style: GoogleFonts.cairo(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                              fontSize: screenWidth < 400 ? 12 : 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // تحسين عرض السيارات المميزة - متجاوب تماماً
                _featuredCars.isNotEmpty
                    ? LayoutBuilder(
                        builder: (context, constraints) {
                          // للجوال: عرض عمودي لتجنب overflow
                          if (screenWidth < 400) {
                            return Column(
                              children: _featuredCars.take(3).map((car) {
                                return Container(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  child: CarCard(
                                    car: car,
                                    onTap: () => _navigateToCarDetails(car),
                                  ),
                                );
                              }).toList(),
                            );
                          }
                          // للتابلت وسطح المكتب: Grid متجاوب
                          else {
                            return GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: crossAxisCount,
                                childAspectRatio:
                                    screenWidth < 600 ? 0.7 : 0.75,
                                crossAxisSpacing: crossAxisSpacing,
                                mainAxisSpacing: 16,
                              ),
                              itemCount: _featuredCars.length,
                              itemBuilder: (context, index) {
                                final car = _featuredCars[index];
                                return CarCard(
                                  car: car,
                                  onTap: () => _navigateToCarDetails(car),
                                );
                              },
                            );
                          }
                        },
                      )
                    : Container(
                        height: 200,
                        margin: EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(16),
                          border:
                              Border.all(color: Colors.grey[300]!, width: 1),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.cloud_off,
                              size: screenWidth < 400 ? 28 : 36,
                              color: Colors.grey[400],
                            ),
                            SizedBox(height: 12),
                            Text(
                              'لا توجد سيارات في قاعدة البيانات',
                              style: GoogleFonts.cairo(
                                color: Colors.grey[700],
                                fontWeight: FontWeight.w600,
                                fontSize: screenWidth < 400 ? 13 : 15,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () async {
                                await _carService.addSampleDataIfEmpty();
                                _loadData();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                padding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                              child: Text(
                                'إضافة بيانات تجريبية',
                                style: GoogleFonts.cairo(
                                  color: Colors.white,
                                  fontSize: screenWidth < 400 ? 11 : 13,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
              ],
            );
          },
        ),
      ),
    );
  }

  // Support/Footer Section
  Widget _buildFooterSection(BuildContext context, bool isArabic) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Text(
              'هل تحتاج مساعدة؟',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'فريق خدمة العملاء متاح 24/7 لمساعدتك',
              style: GoogleFonts.cairo(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _contactSupport(),
              icon: const Icon(Icons.phone, color: Colors.white),
              label: Text(
                'تواصل معنا',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper Methods
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'سيدان':
      case 'sedan':
        return Icons.directions_car;
      case 'دفع رباعي':
      case 'suv':
        return Icons.airport_shuttle;
      case 'فاخرة':
      case 'luxury':
        return Icons.local_taxi;
      case 'رياضية':
      case 'sports':
        return Icons.car_repair;
      default:
        return Icons.directions_car;
    }
  }

  // Navigation Methods
  void _navigateToSearch() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CarsScreen()),
    );
  }

  void _navigateToAllCars() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CarsScreen()),
    );
  }

  void _navigateToCategory(String category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CarsScreen(),
      ),
    );
  }

  void _navigateToCarDetails(CarModel car) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                car.name,
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text('الماركة: ${car.make}'),
              Text('الموديل: ${car.model}'),
              Text('السنة: ${car.year}'),
              Text('الفئة: ${car.category}'),
              Text('السعر اليومي: \$${car.dailyRate}'),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إغلاق'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _showBookingDialog(car);
                    },
                    child: const Text('احجز الآن'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFullMap() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            children: [
              AppBar(
                title: const Text('مواقع التأجير في دبي'),
                leading: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              Expanded(
                child: FlutterMap(
                  options: MapOptions(
                    initialCenter: _dubaiCenter,
                    initialZoom: 11.0,
                  ),
                  children: [
                    TileLayer(
                      urlTemplate:
                          'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                      userAgentPackageName: 'com.dubaicarrental.app',
                    ),
                    MarkerLayer(markers: _mapMarkers),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('لا توجد إشعارات جديدة'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showAppInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [AppColors.primary, AppColors.secondary],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.directions_car_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'دبي كار رنتل',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أفضل خدمة تأجير السيارات في دبي',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'الإصدار: 1.0.0',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.star, color: AppColors.gold, size: 20),
                const SizedBox(width: 4),
                const Text('خدمة 5 نجوم'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.verified, color: AppColors.success, size: 20),
                const SizedBox(width: 4),
                const Text('مرخص ومعتمد'),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showBookingDialog(CarModel car) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('جاري تطوير نظام الحجز للسيارة: ${car.name}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('يمكنك التواصل معنا على الرقم: +971-4-123-4567'),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
