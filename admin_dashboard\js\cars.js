// ملف JavaScript لإدارة السيارات

// متغيرات خاصة بالسيارات
let carsData = [];
let filteredCars = [];
let currentCarsPage = 1;
let carsPerPage = 10;
let totalCarsCount = 0;
let isLoadingCars = false;

// تحميل بيانات السيارات
async function loadCarsData() {
    try {
        showLoading();
        
        // جلب البيانات من قاعدة البيانات
        const response = await fetchCars(currentCarsPage, carsPerPage, {
            search: document.getElementById('carsSearch')?.value || '',
            category: document.getElementById('carsCategory')?.value || '',
            status: document.getElementById('carsStatus')?.value || ''
        });
        
        if (response && response.data) {
            carsData = response.data;
            filteredCars = [...carsData];
            totalCarsCount = response.total || 0;
            
            displayCarsTable();
            setupCarsPagination();
        } else {
            throw new Error('لم يتم استلام البيانات بشكل صحيح');
        }
        
        hideLoading();
    } catch (error) {
        console.error('خطأ في تحميل بيانات السيارات:', error);
        hideLoading();
        
        // عرض رسالة خطأ
        document.getElementById('carsTableBody').innerHTML = `
            <tr>
                <td colspan="8" class="text-center" style="padding: 3rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                    <p style="color: #e53e3e; font-size: 1.1rem; margin-bottom: 1rem;">حدث خطأ في تحميل بيانات السيارات</p>
                    <button class="btn btn-primary" onclick="loadCarsData()">إعادة المحاولة</button>
                </td>
            </tr>
        `;
        
        showNotification('حدث خطأ في تحميل بيانات السيارات', 'error');
    }
}

// إنشاء بيانات تجريبية للسيارات
function generateMockCarsData() {
    const carBrands = [
        { make: 'Toyota', models: ['Camry', 'Corolla', 'RAV4', 'Land Cruiser'] },
        { make: 'Nissan', models: ['Altima', 'Sentra', 'X-Trail', 'Patrol'] },
        { make: 'BMW', models: ['3 Series', '5 Series', 'X3', 'X5'] },
        { make: 'Mercedes', models: ['C-Class', 'E-Class', 'GLC', 'S-Class'] },
        { make: 'Audi', models: ['A3', 'A4', 'Q5', 'Q7'] }
    ];
    
    const categories = ['economy', 'standard', 'premium', 'luxury', 'exotic'];
    const statuses = ['available', 'rented', 'maintenance', 'unavailable'];
    const colors = ['أبيض', 'أسود', 'فضي', 'أحمر', 'أزرق'];
    
    const cars = [];
    
    for (let i = 1; i <= 48; i++) {
        const brandInfo = carBrands[Math.floor(Math.random() * carBrands.length)];
        const model = brandInfo.models[Math.floor(Math.random() * brandInfo.models.length)];
        const category = categories[Math.floor(Math.random() * categories.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const color = colors[Math.floor(Math.random() * colors.length)];
        const year = 2020 + Math.floor(Math.random() * 5);
        
        cars.push({
            id: `car-${i}`,
            name: `${brandInfo.make} ${model} ${year}`,
            make: brandInfo.make,
            model: model,
            year: year,
            category: category,
            type: 'sedan',
            color: color,
            plateNumber: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}-${String(Math.floor(Math.random() * 90000) + 10000)}`,
            dailyPrice: Math.floor(Math.random() * 300) + 100,
            weeklyPrice: Math.floor(Math.random() * 1800) + 600,
            monthlyPrice: Math.floor(Math.random() * 6000) + 2000,
            status: status,
            rating: (Math.random() * 2 + 3).toFixed(1), // من 3.0 إلى 5.0
            totalReviews: Math.floor(Math.random() * 200) + 10,
            images: [
                '/placeholder.svg?height=300&width=400',
                '/placeholder.svg?height=300&width=400',
                '/placeholder.svg?height=300&width=400'
            ],
            features: ['GPS', 'AC', 'Bluetooth', 'USB Charging'],
            location: 'Dubai Marina',
            mileage: Math.floor(Math.random() * 50000) + 5000,
            fuelType: Math.random() > 0.7 ? 'hybrid' : 'gasoline',
            transmission: Math.random() > 0.8 ? 'manual' : 'automatic',
            doors: Math.random() > 0.5 ? 4 : 2,
            seats: Math.random() > 0.3 ? 5 : 7,
            insuranceExpiry: new Date(2025, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
            registrationExpiry: new Date(2025, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
            lastMaintenance: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
            createdAt: new Date(2024, Math.floor(Math.random() * 6), Math.floor(Math.random() * 28) + 1)
        });
    }
    
    return cars;
}

// عرض جدول السيارات
function displayCarsTable() {
    const tableBody = document.getElementById('carsTableBody');
    const startIndex = (currentCarsPage - 1) * carsPerPage;
    const endIndex = startIndex + carsPerPage;
    const carsToDisplay = filteredCars.slice(startIndex, endIndex);
    
    tableBody.innerHTML = carsToDisplay.map(car => `
        <tr>
            <td>
                <img src="${car.images[0]}" alt="${car.name}" class="table-img">
            </td>
            <td>
                <div class="car-info">
                    <strong>${car.name}</strong>
                    <small class="text-muted">${car.plateNumber}</small>
                </div>
            </td>
            <td>${car.make} ${car.model} (${car.year})</td>
            <td>
                <span class="category-badge category-${car.category}">
                    ${getCategoryName(car.category)}
                </span>
            </td>
            <td>${formatCurrency(car.dailyPrice)}</td>
            <td>
                <span class="status-badge status-${car.status}">
                    ${getStatusName(car.status)}
                </span>
            </td>
            <td>
                <div class="rating-display">
                    <div class="rating-stars">
                        ${generateStars(car.rating)}
                    </div>
                    <small>${car.rating} (${car.totalReviews} تقييم)</small>
                </div>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewCar('${car.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editCar('${car.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteCar('${car.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // تحديث معلومات الصفحة
    updateCarsPageInfo();
}

// إعداد ترقيم الصفحات للسيارات
function setupCarsPagination() {
    const totalPages = Math.ceil(totalCarsCount / carsPerPage);
    const paginationContainer = document.getElementById('carsPagination');
    
    if (!paginationContainer) return;
    
    let paginationHTML = '';
    
    // إذا كان هناك صفحة واحدة فقط أو لا توجد بيانات، لا نعرض الترقيم
    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    // زر السابق
    paginationHTML += `
        <button class="pagination-btn" ${currentCarsPage === 1 ? 'disabled' : ''} 
                onclick="changeCarsPage(${currentCarsPage - 1})">
            <i class="fas fa-chevron-right"></i> السابق
        </button>
    `;
    
    // أرقام الصفحات
    const startPage = Math.max(1, currentCarsPage - 2);
    const endPage = Math.min(totalPages, currentCarsPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="pagination-btn" onclick="changeCarsPage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="pagination-dots">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="pagination-btn ${i === currentCarsPage ? 'active' : ''}" 
                    onclick="changeCarsPage(${i})">${i}</button>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<span class="pagination-dots">...</span>`;
        }
        paginationHTML += `<button class="pagination-btn" onclick="changeCarsPage(${totalPages})">${totalPages}</button>`;
    }
    
    // زر التالي
    paginationHTML += `
        <button class="pagination-btn" ${currentCarsPage === totalPages ? 'disabled' : ''} 
                onclick="changeCarsPage(${currentCarsPage + 1})">
            التالي <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// تغيير صفحة السيارات
async function changeCarsPage(page) {
    const totalPages = Math.ceil(totalCarsCount / carsPerPage);
    if (page >= 1 && page <= totalPages && !isLoadingCars) {
        currentCarsPage = page;
        await loadCarsData();
    }
}

// تحديث معلومات الصفحة
function updateCarsPageInfo() {
    const startIndex = (currentCarsPage - 1) * carsPerPage + 1;
    const endIndex = Math.min(currentCarsPage * carsPerPage, totalCarsCount);
    
    // تحديث عداد النتائج إذا كان موجوداً
    const resultsInfo = document.getElementById('carsResultsInfo');
    if (resultsInfo) {
        if (totalCarsCount > 0) {
            resultsInfo.textContent = `عرض ${startIndex} - ${Math.min(endIndex, carsData.length)} من أصل ${totalCarsCount} سيارة`;
        } else {
            resultsInfo.textContent = 'لا توجد سيارات';
        }
    }
}

// فلترة السيارات
async function filterCars() {
    if (isLoadingCars) return;
    
    try {
        isLoadingCars = true;
        currentCarsPage = 1; // إعادة تعيين الصفحة إلى الأولى عند التصفية
        
        await loadCarsData();
        
    } catch (error) {
        console.error('خطأ في تصفية السيارات:', error);
        showNotification('حدث خطأ في تصفية السيارات', 'error');
    } finally {
        isLoadingCars = false;
    }
}

// عرض تفاصيل السيارة
function viewCar(carId) {
    const car = carsData.find(c => c.id === carId);
    if (!car) return;
    
    // إنشاء مودال عرض السيارة
    const modal = createCarViewModal(car);
    document.body.appendChild(modal);
    modal.classList.add('show');
}

// إنشاء مودال عرض السيارة
function createCarViewModal(car) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'carViewModal';
    
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2>${car.name}</h2>
                <button class="close-btn" onclick="closeCarViewModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="car-view-content">
                    <div class="car-images">
                        <div class="main-image">
                            <img src="${car.images[0]}" alt="${car.name}" style="width: 100%; border-radius: 8px;">
                        </div>
                        <div class="image-thumbnails" style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            ${car.images.map((img, index) => `
                                <img src="${img}" alt="صورة ${index + 1}" 
                                     style="width: 60px; height: 60px; border-radius: 4px; cursor: pointer;"
                                     onclick="changeMainImage('${img}')">
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="car-details" style="margin-top: 1.5rem;">
                        <div class="details-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                            <div class="detail-group">
                                <h4>المعلومات الأساسية</h4>
                                <div class="detail-item">
                                    <strong>العلامة التجارية:</strong> ${car.make}
                                </div>
                                <div class="detail-item">
                                    <strong>الطراز:</strong> ${car.model}
                                </div>
                                <div class="detail-item">
                                    <strong>السنة:</strong> ${car.year}
                                </div>
                                <div class="detail-item">
                                    <strong>الفئة:</strong> ${getCategoryName(car.category)}
                                </div>
                                <div class="detail-item">
                                    <strong>اللون:</strong> ${car.color}
                                </div>
                            </div>
                            
                            <div class="detail-group">
                                <h4>المواصفات التقنية</h4>
                                <div class="detail-item">
                                    <strong>ناقل الحركة:</strong> ${car.transmission === 'automatic' ? 'أوتوماتيك' : 'يدوي'}
                                </div>
                                <div class="detail-item">
                                    <strong>نوع الوقود:</strong> ${car.fuelType === 'gasoline' ? 'بنزين' : car.fuelType === 'hybrid' ? 'هجين' : 'ديزل'}
                                </div>
                                <div class="detail-item">
                                    <strong>عدد الأبواب:</strong> ${car.doors}
                                </div>
                                <div class="detail-item">
                                    <strong>عدد المقاعد:</strong> ${car.seats}
                                </div>
                                <div class="detail-item">
                                    <strong>المسافة المقطوعة:</strong> ${formatNumber(car.mileage)} كم
                                </div>
                            </div>
                            
                            <div class="detail-group">
                                <h4>الأسعار</h4>
                                <div class="detail-item">
                                    <strong>السعر اليومي:</strong> ${formatCurrency(car.dailyPrice)}
                                </div>
                                <div class="detail-item">
                                    <strong>السعر الأسبوعي:</strong> ${formatCurrency(car.weeklyPrice)}
                                </div>
                                <div class="detail-item">
                                    <strong>السعر الشهري:</strong> ${formatCurrency(car.monthlyPrice)}
                                </div>
                            </div>
                            
                            <div class="detail-group">
                                <h4>معلومات إضافية</h4>
                                <div class="detail-item">
                                    <strong>رقم اللوحة:</strong> ${car.plateNumber}
                                </div>
                                <div class="detail-item">
                                    <strong>الموقع:</strong> ${car.location}
                                </div>
                                <div class="detail-item">
                                    <strong>الحالة:</strong> ${getStatusName(car.status)}
                                </div>
                                <div class="detail-item">
                                    <strong>التقييم:</strong> ${car.rating} نجمة (${car.totalReviews} تقييم)
                                </div>
                                <div class="detail-item">
                                    <strong>انتهاء التأمين:</strong> ${car.insuranceExpiry.toLocaleDateString('ar-AE')}
                                </div>
                            </div>
                        </div>
                        
                        <div class="car-features" style="margin-top: 1.5rem;">
                            <h4>المميزات</h4>
                            <div class="features-list" style="display: flex; flex-wrap: wrap; gap: 0.5rem; margin-top: 0.5rem;">
                                ${car.features.map(feature => `
                                    <span class="feature-badge" style="background: #f0f9ff; color: #0369a1; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.875rem;">
                                        ${feature}
                                    </span>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeCarViewModal()">إغلاق</button>
                <button class="btn btn-primary" onclick="editCar('${car.id}'); closeCarViewModal();">تعديل السيارة</button>
            </div>
        </div>
    `;
    
    return modal;
}

// إغلاق مودال عرض السيارة
function closeCarViewModal() {
    const modal = document.getElementById('carViewModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// تغيير الصورة الرئيسية
function changeMainImage(imageSrc) {
    const mainImage = document.querySelector('.main-image img');
    if (mainImage) {
        mainImage.src = imageSrc;
    }
}

// تعديل السيارة
function editCar(carId) {
    const car = carsData.find(c => c.id === carId);
    if (!car) return;
    
    // ملء النموذج ببيانات السيارة
    document.getElementById('carName').value = car.name;
    document.getElementById('carMake').value = car.make;
    document.getElementById('carModel').value = car.model;
    document.getElementById('carYear').value = car.year;
    document.getElementById('carCategory').value = car.category;
    document.getElementById('carType').value = car.type;
    document.getElementById('carDailyPrice').value = car.dailyPrice;
    document.getElementById('carPlateNumber').value = car.plateNumber;
    document.getElementById('carDescription').value = car.description || '';
    
    // تغيير عنوان المودال
    document.querySelector('#addCarModal .modal-header h2').textContent = 'تعديل السيارة';
    document.querySelector('#addCarModal .modal-footer .btn-primary').textContent = 'حفظ التعديلات';
    document.querySelector('#addCarModal .modal-footer .btn-primary').setAttribute('onclick', `updateCar('${carId}')`);
    
    showModal('addCarModal');
}

// تحديث السيارة
async function updateCar(carId) {
    try {
        showLoading();
        
        // جمع بيانات النموذج
        const carData = {
            name: document.getElementById('carName').value,
            make: document.getElementById('carMake').value,
            model: document.getElementById('carModel').value,
            year: parseInt(document.getElementById('carYear').value),
            category: document.getElementById('carCategory').value,
            type: document.getElementById('carType').value,
            daily_price: parseFloat(document.getElementById('carDailyPrice').value),
            plate_number: document.getElementById('carPlateNumber').value,
            description: document.getElementById('carDescription').value || ''
        };
        
        // تحديث السيارة في قاعدة البيانات
        const response = await updateCarInDB(carId, carData);
        
        if (response) {
            hideLoading();
            closeModal('addCarModal');
            
            // إعادة تعيين النموذج
            document.querySelector('#addCarModal .modal-header h2').textContent = 'إضافة سيارة جديدة';
            document.querySelector('#addCarModal .modal-footer .btn-primary').textContent = 'إضافة السيارة';
            document.querySelector('#addCarModal .modal-footer .btn-primary').setAttribute('onclick', 'handleAddCar()');
            
            // إعادة تحميل البيانات
            await loadCarsData();
            showNotification('تم تحديث السيارة بنجاح', 'success');
        } else {
            throw new Error('فشل في تحديث السيارة');
        }
        
    } catch (error) {
        console.error('خطأ في تحديث السيارة:', error);
        hideLoading();
        showNotification('حدث خطأ في تحديث السيارة', 'error');
    }
}

// حذف السيارة
async function deleteCar(carId) {
    const car = carsData.find(c => c.id === carId);
    if (!car) return;
    
    const carName = car.name || car.make + ' ' + car.model;
    
    if (confirm(`هل أنت متأكد من حذف السيارة "${carName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        try {
            showLoading();
            
            // حذف السيارة من قاعدة البيانات
            const response = await deleteCarFromDB(carId);
            
            if (response) {
                hideLoading();
                
                // إعادة تحميل البيانات
                await loadCarsData();
                showNotification('تم حذف السيارة بنجاح', 'success');
            } else {
                throw new Error('فشل في حذف السيارة');
            }
            
        } catch (error) {
            console.error('خطأ في حذف السيارة:', error);
            hideLoading();
            showNotification('حدث خطأ في حذف السيارة', 'error');
        }
    }
}

// تغيير حالة السيارة
async function changeCarStatus(carId, newStatus) {
    try {
        showLoading();
        
        // تحديث حالة السيارة في قاعدة البيانات
        const response = await updateCarInDB(carId, { status: newStatus });
        
        if (response) {
            hideLoading();
            
            // إعادة تحميل البيانات
            await loadCarsData();
            showNotification(`تم تغيير حالة السيارة إلى "${getStatusName(newStatus)}"`, 'success');
        } else {
            throw new Error('فشل في تغيير حالة السيارة');
        }
        
    } catch (error) {
        console.error('خطأ في تغيير حالة السيارة:', error);
        hideLoading();
        showNotification('حدث خطأ في تغيير حالة السيارة', 'error');
    }
}

// الحصول على اسم الفئة
function getCategoryName(category) {
    const categoryNames = {
        'economy': 'اقتصادية',
        'standard': 'عادية',
        'premium': 'متميزة',
        'luxury': 'فاخرة',
        'exotic': 'خارقة'
    };
    return categoryNames[category] || category;
}

// الحصول على اسم الحالة
function getStatusName(status) {
    const statusNames = {
        'available': 'متاحة',
        'rented': 'مؤجرة',
        'maintenance': 'صيانة',
        'unavailable': 'غير متاحة'
    };
    return statusNames[status] || status;
}

// إنشاء النجوم للتقييم
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHTML = '';
    
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star star"></i>';
    }
    
    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt star"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star star empty"></i>';
    }
    
    return starsHTML;
}

// تصدير بيانات السيارات
async function exportCars() {
    try {
        showLoading();
        
        // جلب جميع السيارات من قاعدة البيانات
        const allCars = await fetchCars(1, 1000, {}); // جلب عدد كبير للحصول على جميع السيارات
        
        if (!allCars || !allCars.data || allCars.data.length === 0) {
            throw new Error('لا توجد بيانات للتصدير');
        }
        
        // إعداد البيانات للتصدير
        const exportData = allCars.data.map(car => ({
            'الاسم': car.name || (car.make + ' ' + car.model),
            'العلامة التجارية': car.make,
            'الطراز': car.model,
            'السنة': car.year,
            'الفئة': getCategoryName(car.category),
            'النوع': car.type || 'غير محدد',
            'السعر اليومي': car.dailyPrice + ' درهم',
            'رقم اللوحة': car.plateNumber,
            'الحالة': getStatusName(car.status),
            'التقييم': (car.rating || 0) + ' نجوم',
            'عدد التقييمات': car.totalReviews || 0,
            'الموقع': car.location || 'غير محدد',
            'المسافة المقطوعة': car.mileage || 0,
            'تاريخ الإنشاء': car.createdAt ? car.createdAt.toLocaleDateString('ar-AE') : 'غير محدد'
        }));
        
        // تحويل البيانات إلى CSV
        const csv = convertToCSV(exportData);
        downloadCSV(csv, `cars-${formatDate(new Date())}.csv`);
        
        hideLoading();
        showNotification(`تم تصدير ${exportData.length} سيارة بنجاح`, 'success');
        
    } catch (error) {
        console.error('خطأ في تصدير السيارات:', error);
        hideLoading();
        showNotification('حدث خطأ في تصدير البيانات', 'error');
    }
}

// تحويل البيانات إلى CSV
function convertToCSV(data) {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];
    
    data.forEach(row => {
        const values = headers.map(header => {
            const value = row[header];
            return typeof value === 'string' ? `"${value}"` : value;
        });
        csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
}

// تنزيل ملف CSV
function downloadCSV(csvContent, filename) {
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}
