// ملف JavaScript لإدارة التقييمات والمراجعات في لوحة التحكم

// متغيرات عامة للتقييمات
let reviewsData = [];
let currentReviewPage = 1;
const reviewsPerPage = 10;
let totalReviewPages = 1;

// بيانات وهمية للتقييمات (للاختبار)
const mockReviewsData = [
    {
        id: 'REV001',
        car_id: 'CAR001',
        car_name: 'BMW X5 2023',
        booking_id: 'BOOK001',
        user_id: 'user1',
        user_name: 'أحمد محمد',
        user_email: '<EMAIL>',
        user_avatar: '/images/avatars/user1.jpg',
        rating: 5,
        comment: 'تجربة ممتازة! السيارة كانت نظيفة والخدمة احترافية. أنصح بشدة بالتعامل مع هذه الشركة. سأكرر التجربة بالتأكيد.',
        verified: true,
        helpful_votes: 12,
        response: null,
        status: 'published',
        created_at: new Date('2024-01-15T14:30:00'),
        updated_at: new Date('2024-01-15T14:30:00')
    },
    {
        id: 'REV002',
        car_id: 'CAR002',
        car_name: 'Mercedes C-Class 2022',
        booking_id: 'BOOK002',
        user_id: 'user2',
        user_name: 'فاطمة علي',
        user_email: '<EMAIL>',
        user_avatar: '/images/avatars/user2.jpg',
        rating: 4,
        comment: 'السيارة جيدة بشكل عام، لكن كان هناك تأخير في التسليم. الموظفون ودودون والسيارة نظيفة.',
        verified: true,
        helpful_votes: 8,
        response: {
            text: 'شكراً لك على تقييمك. نعتذر عن التأخير ونحرص على تحسين خدماتنا.',
            responded_at: new Date('2024-01-13T10:15:00'),
            responded_by: 'admin'
        },
        status: 'published',
        created_at: new Date('2024-01-12T16:45:00'),
        updated_at: new Date('2024-01-13T10:15:00')
    },
    {
        id: 'REV003',
        car_id: 'CAR003',
        car_name: 'Toyota Corolla 2023',
        booking_id: 'BOOK003',
        user_id: 'user3',
        user_name: 'محمد خالد',
        user_email: '<EMAIL>',
        user_avatar: '/images/avatars/user3.jpg',
        rating: 2,
        comment: 'السيارة بها مشاكل في المكيف والخدمة لم تكن على المستوى المطلوب. أتمنى تحسين الجودة.',
        verified: false,
        helpful_votes: 3,
        response: null,
        status: 'pending',
        created_at: new Date('2024-01-14T09:20:00'),
        updated_at: new Date('2024-01-14T09:20:00')
    },
    {
        id: 'REV004',
        car_id: 'CAR004',
        car_name: 'Audi A4 2023',
        booking_id: 'BOOK004',
        user_id: 'user4',
        user_name: 'سارة أحمد',
        user_email: '<EMAIL>',
        user_avatar: '/images/avatars/user4.jpg',
        rating: 5,
        comment: 'خدمة ممتازة وسيارة فاخرة. كل شيء كان مثالياً من البداية للنهاية. أشكركم على الاهتمام بالتفاصيل.',
        verified: true,
        helpful_votes: 15,
        response: {
            text: 'شكراً جزيلاً لك على هذا التقييم الرائع! نسعد بخدمتك دائماً.',
            responded_at: new Date('2024-01-11T14:30:00'),
            responded_by: 'admin'
        },
        status: 'published',
        created_at: new Date('2024-01-10T18:15:00'),
        updated_at: new Date('2024-01-11T14:30:00')
    },
    {
        id: 'REV005',
        car_id: 'CAR005',
        car_name: 'Nissan Altima 2022',
        booking_id: 'BOOK005',
        user_id: 'user5',
        user_name: 'عبدالله حسن',
        user_email: '<EMAIL>',
        user_avatar: '/images/avatars/user5.jpg',
        rating: 3,
        comment: 'السيارة مقبولة، لكن السعر مرتفع نسبياً مقارنة بالجودة. الخدمة عادية.',
        verified: true,
        helpful_votes: 5,
        response: null,
        status: 'published',
        created_at: new Date('2024-01-11T12:40:00'),
        updated_at: new Date('2024-01-11T12:40:00')
    },
    {
        id: 'REV006',
        car_id: 'CAR001',
        car_name: 'BMW X5 2023',
        booking_id: 'BOOK006',
        user_id: 'user6',
        user_name: 'ليلى محمود',
        user_email: '<EMAIL>',
        user_avatar: '/images/avatars/user6.jpg',
        rating: 1,
        comment: 'تجربة سيئة جداً. السيارة متسخة والموظفون غير محترمين. لن أتعامل معكم مرة أخرى.',
        verified: false,
        helpful_votes: 1,
        response: null,
        status: 'reported',
        created_at: new Date('2024-01-13T20:10:00'),
        updated_at: new Date('2024-01-13T20:10:00')
    }
];

// تهيئة صفحة التقييمات
function initializeReviews() {
    console.log('تهيئة صفحة التقييمات...');
    
    // تحميل البيانات
    loadReviewsData();
    
    // ربط الأحداث
    bindReviewEvents();
    
    // تحديث الإحصائيات
    updateReviewStats();
}

// تحميل بيانات التقييمات
async function loadReviewsData() {
    try {
        showLoading(true);
        
        // في الواقع، هذا سيكون استدعاء API
        // const response = await fetchReviews(currentReviewPage, reviewsPerPage, getReviewFilters());
        // reviewsData = response.data;
        // totalReviewPages = response.totalPages;
        
        // استخدام البيانات الوهمية للآن
        await new Promise(resolve => setTimeout(resolve, 800));
        reviewsData = mockReviewsData;
        totalReviewPages = Math.ceil(reviewsData.length / reviewsPerPage);
        
        displayReviews();
        updateReviewPagination();
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات التقييمات:', error);
        showNotification('خطأ في تحميل بيانات التقييمات', 'error');
    } finally {
        showLoading(false);
    }
}

// عرض التقييمات
function displayReviews() {
    const reviewsContainer = document.getElementById('reviewsContainer');
    if (!reviewsContainer) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentReviewPage - 1) * reviewsPerPage;
    const endIndex = startIndex + reviewsPerPage;
    const currentReviews = reviewsData.slice(startIndex, endIndex);
    
    reviewsContainer.innerHTML = '';
    
    currentReviews.forEach(review => {
        const reviewCard = document.createElement('div');
        reviewCard.className = 'review-card';
        reviewCard.innerHTML = `
            <div class="review-header">
                <div class="reviewer-info">
                    <img src="${review.user_avatar}" alt="${review.user_name}" class="reviewer-avatar" 
                         onerror="this.src='/images/default-avatar.png'">
                    <div class="reviewer-details">
                        <h4>${review.user_name}</h4>
                        <p>${review.user_email}</p>
                        <div class="review-meta">
                            <span class="car-name">
                                <i class="fas fa-car"></i>
                                ${review.car_name}
                            </span>
                            <span class="review-date">
                                <i class="fas fa-calendar"></i>
                                ${formatDate(review.created_at)}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="review-actions">
                    <span class="review-status status-${review.status}">
                        ${getReviewStatusText(review.status)}
                    </span>
                    ${review.verified ? '<span class="verified-badge"><i class="fas fa-check-circle"></i> محقق</span>' : ''}
                </div>
            </div>
            
            <div class="review-content">
                <div class="rating-display">
                    ${generateStarRating(review.rating)}
                    <span class="rating-number">(${review.rating}/5)</span>
                </div>
                
                <div class="review-comment">
                    <p>${review.comment}</p>
                </div>
                
                ${review.helpful_votes > 0 ? `
                    <div class="helpful-votes">
                        <i class="fas fa-thumbs-up"></i>
                        ${review.helpful_votes} شخص وجد هذا التقييم مفيداً
                    </div>
                ` : ''}
                
                ${review.response ? `
                    <div class="admin-response">
                        <div class="response-header">
                            <i class="fas fa-reply"></i>
                            <strong>رد الإدارة</strong>
                            <span class="response-date">${formatDateTime(review.response.responded_at)}</span>
                        </div>
                        <p>${review.response.text}</p>
                    </div>
                ` : ''}
            </div>
            
            <div class="review-footer">
                <div class="review-buttons">
                    ${review.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="approveReview('${review.id}')">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="rejectReview('${review.id}')">
                            <i class="fas fa-times"></i> رفض
                        </button>
                    ` : ''}
                    
                    ${!review.response && review.status === 'published' ? `
                        <button class="btn btn-primary btn-sm" onclick="showResponseModal('${review.id}')">
                            <i class="fas fa-reply"></i> رد
                        </button>
                    ` : ''}
                    
                    ${review.status === 'reported' ? `
                        <button class="btn btn-warning btn-sm" onclick="moderateReview('${review.id}')">
                            <i class="fas fa-flag"></i> مراجعة
                        </button>
                    ` : ''}
                    
                    <button class="btn btn-outline btn-sm" onclick="viewReviewDetails('${review.id}')">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                    
                    <button class="btn btn-danger btn-sm" onclick="deleteReview('${review.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
        
        reviewsContainer.appendChild(reviewCard);
    });
    
    // تحديث معلومات العدد
    updateReviewCount();
}

// ربط أحداث التقييمات
function bindReviewEvents() {
    // فلترة التقييمات
    const statusFilter = document.getElementById('reviewStatusFilter');
    const ratingFilter = document.getElementById('reviewRatingFilter');
    const verifiedFilter = document.getElementById('reviewVerifiedFilter');
    const searchInput = document.getElementById('searchReviews');
    
    if (statusFilter) {
        statusFilter.addEventListener('change', filterReviews);
    }
    
    if (ratingFilter) {
        ratingFilter.addEventListener('change', filterReviews);
    }
    
    if (verifiedFilter) {
        verifiedFilter.addEventListener('change', filterReviews);
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterReviews, 300));
    }
    
    // تصدير البيانات
    const exportBtn = document.getElementById('exportReviews');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportReviews);
    }
    
    // تحديث البيانات
    const refreshBtn = document.getElementById('refreshReviews');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            currentReviewPage = 1;
            loadReviewsData();
        });
    }
}

// فلترة التقييمات
function filterReviews() {
    const statusFilter = document.getElementById('reviewStatusFilter')?.value || '';
    const ratingFilter = document.getElementById('reviewRatingFilter')?.value || '';
    const verifiedFilter = document.getElementById('reviewVerifiedFilter')?.value || '';
    const searchText = document.getElementById('searchReviews')?.value.toLowerCase() || '';
    
    let filteredData = mockReviewsData.filter(review => {
        // فلترة بالحالة
        if (statusFilter && review.status !== statusFilter) {
            return false;
        }
        
        // فلترة بالتقييم
        if (ratingFilter) {
            const filterRating = parseInt(ratingFilter);
            if (review.rating !== filterRating) {
                return false;
            }
        }
        
        // فلترة بحالة التحقق
        if (verifiedFilter) {
            const isVerified = verifiedFilter === 'true';
            if (review.verified !== isVerified) {
                return false;
            }
        }
        
        // فلترة بالبحث النصي
        if (searchText) {
            const searchableText = `${review.user_name} ${review.car_name} ${review.comment}`.toLowerCase();
            if (!searchableText.includes(searchText)) {
                return false;
            }
        }
        
        return true;
    });
    
    reviewsData = filteredData;
    totalReviewPages = Math.ceil(reviewsData.length / reviewsPerPage);
    currentReviewPage = 1;
    
    displayReviews();
    updateReviewPagination();
    updateReviewStats();
}

// موافقة على التقييم
function approveReview(reviewId) {
    const review = mockReviewsData.find(r => r.id === reviewId);
    if (!review) return;
    
    if (confirm('هل أنت متأكد من الموافقة على هذا التقييم؟')) {
        review.status = 'published';
        review.updated_at = new Date();
        
        showNotification('تم نشر التقييم بنجاح', 'success');
        displayReviews();
        updateReviewStats();
    }
}

// رفض التقييم
function rejectReview(reviewId) {
    const review = mockReviewsData.find(r => r.id === reviewId);
    if (!review) return;
    
    const reason = prompt('يرجى إدخال سبب الرفض (اختياري):');
    if (reason !== null) {
        review.status = 'rejected';
        review.rejection_reason = reason;
        review.updated_at = new Date();
        
        showNotification('تم رفض التقييم', 'success');
        displayReviews();
        updateReviewStats();
    }
}

// عرض موديل الرد على التقييم
function showResponseModal(reviewId) {
    const review = mockReviewsData.find(r => r.id === reviewId);
    if (!review) return;
    
    // ملء بيانات التقييم
    document.getElementById('responseReviewId').value = reviewId;
    document.getElementById('responseReviewDetails').innerHTML = `
        <div class="review-summary">
            <h4>${review.user_name}</h4>
            <div class="rating-display">
                ${generateStarRating(review.rating)}
                <span class="rating-number">(${review.rating}/5)</span>
            </div>
            <p><strong>السيارة:</strong> ${review.car_name}</p>
            <div class="original-comment">
                <strong>التعليق:</strong>
                <p>${review.comment}</p>
            </div>
        </div>
    `;
    
    document.getElementById('responseText').value = '';
    showModal('responseModal');
}

// إرسال الرد على التقييم
function submitResponse() {
    const reviewId = document.getElementById('responseReviewId').value;
    const responseText = document.getElementById('responseText').value.trim();
    
    if (!responseText) {
        showNotification('يرجى كتابة نص الرد', 'error');
        return;
    }
    
    const review = mockReviewsData.find(r => r.id === reviewId);
    if (review) {
        review.response = {
            text: responseText,
            responded_at: new Date(),
            responded_by: 'admin'
        };
        review.updated_at = new Date();
        
        hideModal('responseModal');
        showNotification('تم إرسال الرد بنجاح', 'success');
        displayReviews();
    }
}

// مراجعة تقييم مبلغ عنه
function moderateReview(reviewId) {
    const review = mockReviewsData.find(r => r.id === reviewId);
    if (!review) return;
    
    const action = confirm('هل تريد الموافقة على التقييم؟\nضغط موافق للنشر أو إلغاء للرفض');
    
    if (action) {
        review.status = 'published';
        showNotification('تم نشر التقييم بعد المراجعة', 'success');
    } else {
        review.status = 'rejected';
        review.rejection_reason = 'تم الرفض بعد المراجعة بسبب البلاغات';
        showNotification('تم رفض التقييم', 'success');
    }
    
    review.updated_at = new Date();
    displayReviews();
    updateReviewStats();
}

// عرض تفاصيل التقييم
function viewReviewDetails(reviewId) {
    const review = mockReviewsData.find(r => r.id === reviewId);
    if (!review) return;
    
    // ملء تفاصيل التقييم
    document.getElementById('detailReviewId').textContent = review.id;
    document.getElementById('detailBookingId').textContent = review.booking_id;
    document.getElementById('detailCarName').textContent = review.car_name;
    document.getElementById('detailUserName').textContent = review.user_name;
    document.getElementById('detailUserEmail').textContent = review.user_email;
    document.getElementById('detailRating').innerHTML = `
        ${generateStarRating(review.rating)}
        <span class="rating-number">(${review.rating}/5)</span>
    `;
    document.getElementById('detailComment').textContent = review.comment;
    document.getElementById('detailVerified').innerHTML = review.verified 
        ? '<span class="verified-badge"><i class="fas fa-check-circle"></i> محقق</span>'
        : '<span class="unverified-badge"><i class="fas fa-times-circle"></i> غير محقق</span>';
    document.getElementById('detailStatus').innerHTML = `
        <span class="review-status status-${review.status}">
            ${getReviewStatusText(review.status)}
        </span>
    `;
    document.getElementById('detailHelpfulVotes').textContent = review.helpful_votes;
    document.getElementById('detailCreatedAt').textContent = formatDateTime(review.created_at);
    document.getElementById('detailUpdatedAt').textContent = formatDateTime(review.updated_at);
    
    // معلومات الرد
    const responseSection = document.getElementById('detailResponse');
    if (review.response) {
        responseSection.style.display = 'block';
        responseSection.innerHTML = `
            <h4>رد الإدارة</h4>
            <div class="admin-response">
                <p>${review.response.text}</p>
                <small>تم الرد في: ${formatDateTime(review.response.responded_at)}</small>
            </div>
        `;
    } else {
        responseSection.style.display = 'none';
    }
    
    showModal('reviewDetailsModal');
}

// حذف التقييم
function deleteReview(reviewId) {
    if (!confirm('هل أنت متأكد من حذف هذا التقييم؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    const index = mockReviewsData.findIndex(r => r.id === reviewId);
    if (index !== -1) {
        mockReviewsData.splice(index, 1);
        showNotification('تم حذف التقييم بنجاح', 'success');
        
        // إعادة تحديث البيانات
        filterReviews();
    }
}

// تصدير التقييمات
function exportReviews() {
    showLoading(true);
    
    setTimeout(() => {
        const csvContent = generateReviewsCSV(reviewsData);
        downloadCSV(csvContent, 'reviews_export.csv');
        showLoading(false);
        showNotification('تم تصدير البيانات بنجاح', 'success');
    }, 1500);
}

// إنشاء محتوى CSV للتقييمات
function generateReviewsCSV(reviews) {
    const headers = [
        'معرف التقييم',
        'معرف الحجز',
        'اسم العميل',
        'البريد الإلكتروني',
        'اسم السيارة',
        'التقييم',
        'التعليق',
        'محقق',
        'الحالة',
        'التصويتات المفيدة',
        'تاريخ الإنشاء',
        'رد الإدارة',
        'تاريخ الرد'
    ];
    
    let csvContent = headers.join(',') + '\n';
    
    reviews.forEach(review => {
        const row = [
            review.id,
            review.booking_id,
            `"${review.user_name}"`,
            review.user_email,
            `"${review.car_name}"`,
            review.rating,
            `"${review.comment.replace(/"/g, '""')}"`,
            review.verified ? 'نعم' : 'لا',
            getReviewStatusText(review.status),
            review.helpful_votes,
            formatDateTime(review.created_at),
            review.response ? `"${review.response.text.replace(/"/g, '""')}"` : '',
            review.response ? formatDateTime(review.response.responded_at) : ''
        ];
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

// تحديث إحصائيات التقييمات
function updateReviewStats() {
    const stats = calculateReviewStats(reviewsData);
    
    // تحديث الإحصائيات في الصفحة
    const totalReviewsEl = document.getElementById('totalReviews');
    const averageRatingEl = document.getElementById('averageRating');
    const publishedReviewsEl = document.getElementById('publishedReviews');
    const pendingReviewsEl = document.getElementById('pendingReviews');
    
    if (totalReviewsEl) {
        totalReviewsEl.textContent = stats.total;
    }
    
    if (averageRatingEl) {
        averageRatingEl.innerHTML = `
            ${stats.averageRating.toFixed(1)}
            ${generateStarRating(Math.round(stats.averageRating))}
        `;
    }
    
    if (publishedReviewsEl) {
        publishedReviewsEl.textContent = stats.published;
    }
    
    if (pendingReviewsEl) {
        pendingReviewsEl.textContent = stats.pending;
    }
}

// حساب إحصائيات التقييمات
function calculateReviewStats(reviews) {
    const stats = {
        total: reviews.length,
        published: 0,
        pending: 0,
        rejected: 0,
        reported: 0,
        verified: 0,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    };
    
    let totalRating = 0;
    
    reviews.forEach(review => {
        // إحصائيات الحالة
        stats[review.status]++;
        
        // إحصائيات التحقق
        if (review.verified) {
            stats.verified++;
        }
        
        // إحصائيات التقييم
        totalRating += review.rating;
        stats.ratingDistribution[review.rating]++;
    });
    
    stats.averageRating = reviews.length > 0 ? totalRating / reviews.length : 0;
    
    return stats;
}

// إنشاء عرض النجوم
function generateStarRating(rating) {
    let starsHTML = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            starsHTML += '<i class="fas fa-star star-filled"></i>';
        } else {
            starsHTML += '<i class="far fa-star star-empty"></i>';
        }
    }
    return starsHTML;
}

// تحديث عدد التقييمات
function updateReviewCount() {
    const countElement = document.getElementById('reviewsCount');
    if (countElement) {
        const startIndex = (currentReviewPage - 1) * reviewsPerPage + 1;
        const endIndex = Math.min(currentReviewPage * reviewsPerPage, reviewsData.length);
        countElement.textContent = `عرض ${startIndex}-${endIndex} من ${reviewsData.length} تقييم`;
    }
}

// دوال مساعدة للتقييمات
function getReviewStatusText(status) {
    const statuses = {
        'pending': 'في الانتظار',
        'published': 'منشور',
        'rejected': 'مرفوض',
        'reported': 'مبلغ عنه'
    };
    
    return statuses[status] || status;
}

// التنقل بين الصفحات
function goToReviewPage(page) {
    if (page >= 1 && page <= totalReviewPages) {
        currentReviewPage = page;
        displayReviews();
        updateReviewPagination();
    }
}

function updateReviewPagination() {
    const paginationContainer = document.querySelector('#reviewsPagination .pagination');
    if (!paginationContainer) return;
    
    let paginationHTML = '';
    
    // زر السابق
    paginationHTML += `
        <button class="page-btn ${currentReviewPage === 1 ? 'disabled' : ''}" 
                onclick="goToReviewPage(${currentReviewPage - 1})" 
                ${currentReviewPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // أرقام الصفحات
    const startPage = Math.max(1, currentReviewPage - 2);
    const endPage = Math.min(totalReviewPages, currentReviewPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="goToReviewPage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="page-dots">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="page-btn ${i === currentReviewPage ? 'active' : ''}" 
                    onclick="goToReviewPage(${i})">
                ${i}
            </button>
        `;
    }
    
    if (endPage < totalReviewPages) {
        if (endPage < totalReviewPages - 1) {
            paginationHTML += `<span class="page-dots">...</span>`;
        }
        paginationHTML += `<button class="page-btn" onclick="goToReviewPage(${totalReviewPages})">${totalReviewPages}</button>`;
    }
    
    // زر التالي
    paginationHTML += `
        <button class="page-btn ${currentReviewPage === totalReviewPages ? 'disabled' : ''}" 
                onclick="goToReviewPage(${currentReviewPage + 1})" 
                ${currentReviewPage === totalReviewPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// تصدير الوظائف للاستخدام العام
window.initializeReviews = initializeReviews;
window.approveReview = approveReview;
window.rejectReview = rejectReview;
window.showResponseModal = showResponseModal;
window.submitResponse = submitResponse;
window.moderateReview = moderateReview;
window.viewReviewDetails = viewReviewDetails;
window.deleteReview = deleteReview;
window.exportReviews = exportReviews;
window.goToReviewPage = goToReviewPage;
