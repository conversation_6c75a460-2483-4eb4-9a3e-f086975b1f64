import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

enum NotificationType {
  booking,
  promotional,
  reminder,
  system,
  payment,
  maintenance,
  verification,
  welcome,
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

class NotificationModel extends Equatable {
  final String id;
  final String userId;
  final String title;
  final String message;
  final String? imageUrl;
  final NotificationType type;
  final NotificationPriority priority;
  final bool isRead;
  final bool isPinned;
  final String? actionUrl;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? expiresAt;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    this.imageUrl,
    required this.type,
    this.priority = NotificationPriority.normal,
    this.isRead = false,
    this.isPinned = false,
    this.actionUrl,
    this.metadata,
    required this.createdAt,
    this.readAt,
    this.expiresAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      imageUrl: json['image_url'] as String?,
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationType.system,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      isRead: json['is_read'] as bool? ?? false,
      isPinned: json['is_pinned'] as bool? ?? false,
      actionUrl: json['action_url'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      readAt: json['read_at'] != null
          ? DateTime.parse(json['read_at'] as String)
          : null,
      expiresAt: json['expires_at'] != null
          ? DateTime.parse(json['expires_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'message': message,
      'image_url': imageUrl,
      'type': type.name,
      'priority': priority.name,
      'is_read': isRead,
      'is_pinned': isPinned,
      'action_url': actionUrl,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? message,
    String? imageUrl,
    NotificationType? type,
    NotificationPriority? priority,
    bool? isRead,
    bool? isPinned,
    String? actionUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? expiresAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      message: message ?? this.message,
      imageUrl: imageUrl ?? this.imageUrl,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      isPinned: isPinned ?? this.isPinned,
      actionUrl: actionUrl ?? this.actionUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        message,
        imageUrl,
        type,
        priority,
        isRead,
        isPinned,
        actionUrl,
        metadata,
        createdAt,
        readAt,
        expiresAt,
      ];
}

// Extension for display helpers
extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.booking:
        return 'الحجوزات';
      case NotificationType.promotional:
        return 'العروض';
      case NotificationType.reminder:
        return 'التذكيرات';
      case NotificationType.system:
        return 'النظام';
      case NotificationType.payment:
        return 'المدفوعات';
      case NotificationType.maintenance:
        return 'الصيانة';
      case NotificationType.verification:
        return 'التحقق';
      case NotificationType.welcome:
        return 'الترحيب';
    }
  }

  IconData get icon {
    switch (this) {
      case NotificationType.booking:
        return Icons.car_rental;
      case NotificationType.promotional:
        return Icons.local_offer;
      case NotificationType.reminder:
        return Icons.alarm;
      case NotificationType.system:
        return Icons.settings;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.maintenance:
        return Icons.build;
      case NotificationType.verification:
        return Icons.verified_user;
      case NotificationType.welcome:
        return Icons.waving_hand;
    }
  }

  Color get color {
    switch (this) {
      case NotificationType.booking:
        return Colors.blue;
      case NotificationType.promotional:
        return Colors.orange;
      case NotificationType.reminder:
        return Colors.amber;
      case NotificationType.system:
        return Colors.grey;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.maintenance:
        return Colors.red;
      case NotificationType.verification:
        return Colors.purple;
      case NotificationType.welcome:
        return Colors.teal;
    }
  }
}
