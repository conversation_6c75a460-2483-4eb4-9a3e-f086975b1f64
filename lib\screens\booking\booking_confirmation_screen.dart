import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../models/car.dart';
import '../../models/booking.dart';
import '../../providers/booking_provider.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../main_navigation_screen.dart';

class BookingConfirmationScreen extends StatefulWidget {
  final String bookingId;
  final Car car;

  const BookingConfirmationScreen({
    Key? key,
    required this.bookingId,
    required this.car,
  }) : super(key: key);

  @override
  _BookingConfirmationScreenState createState() =>
      _BookingConfirmationScreenState();
}

class _BookingConfirmationScreenState extends State<BookingConfirmationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  Booking? _booking;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadBookingDetails();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  void _loadBookingDetails() async {
    try {
      final bookingProvider =
          Provider.of<BookingProvider>(context, listen: false);
      final booking = await bookingProvider.getBookingById(widget.bookingId);

      setState(() {
        _booking = booking;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppTheme.lightGrey,
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const SizedBox(height: 40),

                      // أيقونة النجاح مع الرسوم المتحركة
                      ScaleTransition(
                        scale: _scaleAnimation,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: AppTheme.successGreen,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.successGreen.withOpacity(0.3),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 60,
                          ),
                        ),
                      ),

                      const SizedBox(height: 30),

                      // رسالة التأكيد
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: Column(
                          children: [
                            Text(
                              l10n.bookingConfirmed,
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.successGreen,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              l10n.bookingConfirmationMessage,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color: AppTheme.mediumGrey,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 40),

                      // تفاصيل الحجز
                      if (_booking != null) ...[
                        _buildBookingDetailsCard(l10n),
                        const SizedBox(height: 20),
                        _buildCarDetailsCard(l10n),
                        const SizedBox(height: 20),
                        _buildQRCodeCard(l10n),
                        const SizedBox(height: 20),
                        _buildNextStepsCard(l10n),
                      ],

                      const SizedBox(height: 40),

                      // الأزرار
                      _buildActionButtons(l10n),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildBookingDetailsCard(AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.confirmation_number,
                  color: AppTheme.royalBlue,
                ),
                const SizedBox(width: 8),
                Text(
                  l10n.bookingDetails,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildDetailRow(
              l10n.bookingNumber,
              _booking!.bookingNumber,
              isHighlighted: true,
            ),
            _buildDetailRow(
              l10n.pickupDate,
              DateFormat('EEEE, dd MMMM yyyy').format(_booking!.pickupDate),
            ),
            _buildDetailRow(
              l10n.returnDate,
              DateFormat('EEEE, dd MMMM yyyy').format(_booking!.returnDate),
            ),
            _buildDetailRow(
              l10n.pickupLocation,
              _booking!.pickupAddress,
            ),
            _buildDetailRow(
              l10n.totalAmount,
              '${_booking!.totalAmount.toStringAsFixed(0)} ${l10n.aed}',
              isHighlighted: true,
            ),
            _buildDetailRow(
              l10n.status,
              _getStatusText(l10n, _booking!.status),
              statusColor: AppTheme.getBookingStatusColor(_booking!.status),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCarDetailsCard(AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.directions_car,
                  color: AppTheme.royalBlue,
                ),
                const SizedBox(width: 8),
                Text(
                  l10n.carDetails,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    widget.car.images.first,
                    width: 100,
                    height: 75,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 100,
                        height: 75,
                        color: AppTheme.lightGrey,
                        child: Icon(
                          Icons.directions_car,
                          color: AppTheme.mediumGrey,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.car.name,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.car.brand} ${widget.car.model} ${widget.car.year}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.mediumGrey,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildCarSpec(Icons.people, '${widget.car.seats}'),
                          const SizedBox(width: 12),
                          _buildCarSpec(
                              Icons.local_gas_station, widget.car.fuelType),
                          const SizedBox(width: 12),
                          _buildCarSpec(
                              Icons.settings, widget.car.transmission),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRCodeCard(AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.qr_code,
                  color: AppTheme.royalBlue,
                ),
                const SizedBox(width: 8),
                Text(
                  l10n.bookingQRCode,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.borderGrey),
              ),
              child: QrImageView(
                data: _booking!.bookingNumber,
                version: QrVersions.auto,
                size: 150,
                backgroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              l10n.showQRCodeAtPickup,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.mediumGrey,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextStepsCard(AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.list_alt,
                  color: AppTheme.royalBlue,
                ),
                const SizedBox(width: 8),
                Text(
                  l10n.nextSteps,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildStepItem(
              1,
              l10n.step1Title,
              l10n.step1Description,
              Icons.email,
            ),
            _buildStepItem(
              2,
              l10n.step2Title,
              l10n.step2Description,
              Icons.description,
            ),
            _buildStepItem(
              3,
              l10n.step3Title,
              l10n.step3Description,
              Icons.location_on,
            ),
            _buildStepItem(
              4,
              l10n.step4Title,
              l10n.step4Description,
              Icons.key,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepItem(
      int step, String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppTheme.royalBlue,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$step',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      icon,
                      size: 16,
                      color: AppTheme.mediumGrey,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.mediumGrey,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value,
      {bool isHighlighted = false, Color? statusColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.mediumGrey,
                ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w500,
                  color: statusColor ??
                      (isHighlighted ? AppTheme.royalBlue : AppTheme.darkGrey),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildCarSpec(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: AppTheme.mediumGrey,
        ),
        const SizedBox(width: 2),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.mediumGrey,
              ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(AppLocalizations l10n) {
    return Column(
      children: [
        // زر العودة للرئيسية
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const MainNavigationScreen(),
                ),
                (route) => false,
              );
            },
            icon: const Icon(Icons.home),
            label: Text(l10n.backToHome),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // زر عرض الحجوزات
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const MainNavigationScreen(),
                ),
                (route) => false,
              );
            },
            icon: const Icon(Icons.list_alt),
            label: Text(l10n.viewMyBookings),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // زر المشاركة
        SizedBox(
          width: double.infinity,
          child: TextButton.icon(
            onPressed: () {
              // مشاركة تفاصيل الحجز
            },
            icon: const Icon(Icons.share),
            label: Text(l10n.shareBooking),
          ),
        ),
      ],
    );
  }

  String _getStatusText(AppLocalizations l10n, dynamic status) {
    if (status == null) return 'Unknown';

    final statusString = status.toString().split('.').last;
    switch (statusString.toLowerCase()) {
      case 'pending':
        return l10n.pending;
      case 'confirmed':
        return l10n.confirmed;
      case 'active':
        return l10n.active;
      case 'completed':
        return l10n.completed;
      case 'cancelled':
        return l10n.cancelled;
      case 'rejected':
        return l10n.cancelled; // Use cancelled text for rejected
      default:
        return statusString;
    }
  }
}
