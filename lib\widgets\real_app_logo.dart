import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

// مكون الشعار الجديد باستخدام الشعار الحقيقي
class RealAppLogo extends StatefulWidget {
  final double size;
  final bool showCompanyName;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final bool isAnimated;
  final bool showShadow;

  const RealAppLogo({
    Key? key,
    this.size = 50,
    this.showCompanyName = true,
    this.backgroundColor,
    this.onTap,
    this.isAnimated = false,
    this.showShadow = true,
  }) : super(key: key);

  @override
  State<RealAppLogo> createState() => _RealAppLogoState();
}

class _RealAppLogoState extends State<RealAppLogo>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _hoverController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.05,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isAnimated) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _hoverController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _hoverController.reverse();
    widget.onTap?.call();
  }

  void _onTapCancel() {
    _hoverController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: Listenable.merge([_controller, _hoverController]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: widget.backgroundColor?.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                  boxShadow: widget.showShadow
                      ? [
                          BoxShadow(
                            color: AppColors.primary
                                .withOpacity(0.2 * _glowAnimation.value),
                            blurRadius: 15,
                            offset: const Offset(0, 4),
                            spreadRadius: 2,
                          ),
                        ]
                      : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // الشعار الحقيقي
                    Container(
                      width: widget.size,
                      height: widget.size,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(widget.size * 0.2),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(widget.size * 0.2),
                        child: Image.asset(
                          'assets/logos/app_logo.png',
                          width: widget.size,
                          height: widget.size,
                          fit: BoxFit.contain, // تغيير من cover إلى contain
                          errorBuilder: (context, error, stackTrace) {
                            // في حالة عدم وجود الشعار، استخدم شعار بديل
                            print('خطأ في تحميل الشعار: $error');
                            return _buildFallbackLogo();
                          },
                        ),
                      ),
                    ),
                    // نص الشركة
                    if (widget.showCompanyName) ...[
                      const SizedBox(width: 12),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'دبي',
                            style: TextStyle(
                              fontSize: widget.size * 0.32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              height: 1.0,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.3),
                                  offset: const Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                          ),
                          Text(
                            'كار رنتل',
                            style: TextStyle(
                              fontSize: widget.size * 0.24,
                              fontWeight: FontWeight.w500,
                              color: Colors.white.withOpacity(0.95),
                              height: 1.0,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.2),
                                  offset: const Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // شعار بديل في حالة عدم وجود الصورة
  Widget _buildFallbackLogo() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF2196F3), // اللون الأزرق من الشعار الأصلي
            Color(0xFF1976D2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(widget.size * 0.2),
      ),
      child: Stack(
        children: [
          // خلفية متدرجة لإضافة عمق
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.size * 0.2),
              gradient: RadialGradient(
                colors: [
                  Colors.white.withOpacity(0.2),
                  Colors.transparent,
                ],
                center: const Alignment(-0.5, -0.5),
                radius: 1.2,
              ),
            ),
          ),
          // رمز النسر المبسط (مشابه للشعار الأصلي)
          Center(
            child: CustomPaint(
              size: Size(widget.size * 0.6, widget.size * 0.6),
              painter: EaglePainter(),
            ),
          ),
        ],
      ),
    );
  }
}

// رسام مخصص لرمز النسر
class EaglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();

    // رسم شكل مبسط للنسر
    final width = size.width;
    final height = size.height;

    // الرأس
    path.moveTo(width * 0.5, height * 0.1);
    path.quadraticBezierTo(
        width * 0.7, height * 0.2, width * 0.8, height * 0.4);

    // الجناح الأيمن
    path.quadraticBezierTo(
        width * 0.9, height * 0.6, width * 0.7, height * 0.8);

    // الجسم
    path.lineTo(width * 0.5, height * 0.9);

    // الجناح الأيسر
    path.lineTo(width * 0.3, height * 0.8);
    path.quadraticBezierTo(
        width * 0.1, height * 0.6, width * 0.2, height * 0.4);

    // العودة للرأس
    path.quadraticBezierTo(
        width * 0.3, height * 0.2, width * 0.5, height * 0.1);

    canvas.drawPath(path, paint);

    // العين
    final eyePaint = Paint()
      ..color = const Color(0xFF2196F3)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(width * 0.6, height * 0.3),
      width * 0.05,
      eyePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// مكون شعار صغير للأماكن المحدودة
class CompactLogo extends StatelessWidget {
  final double size;
  final bool showGlow;

  const CompactLogo({
    Key? key,
    this.size = 30,
    this.showGlow = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size * 0.2),
        boxShadow: showGlow
            ? [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.4),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                  spreadRadius: 1,
                ),
              ]
            : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(size * 0.2),
        child: Image.asset(
          'assets/logos/app_logo.png',
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // شعار بديل مبسط
            return Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF2196F3),
                    Color(0xFF1976D2),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.flight_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
