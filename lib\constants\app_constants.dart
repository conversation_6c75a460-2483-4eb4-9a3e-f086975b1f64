class AppConstants {
  // App Info
  static const String appName = 'Dubai Car Rental';
  static const String appNameArabic = 'تأجير السيارات دبي';

  // Supabase Configuration
  static const String supabaseUrl = 'https://zvzaixlygdhloganycjt.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2emFpeGx5Z2RobG9nYW55Y2p0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjE4ODUsImV4cCI6MjA3MDM5Nzg4NX0.xI_qvs6kupVt0EM6nnFzgfe1QBmUkelNO7c0O64GCXQ';

  // Stripe Configuration - Test Keys (Replace with your own)
  static const String stripePublishableKey =
      'pk_test_51234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789';
  static const String stripeSecretKey =
      'sk_test_placeholder_replace_with_your_secret_key';

  // Google API Keys
  static const String googleMapsApiKey =
      'AIzaSyBCN4qJ7O4gF3O9S2qP9t8aE2fM6yG3jKA'; // Replace with your key
  static const String googleWebClientId =
      'YOUR_GOOGLE_CLIENT_ID_HERE.apps.googleusercontent.com';

  // Dubai Boundaries for Map
  static const double dubaiBoundsNorth = 25.3375;
  static const double dubaiBoundsSouth = 24.8607;
  static const double dubaiBoundsEast = 55.5731;
  static const double dubaiBoundsWest = 54.8969;

  // Default Dubai Center
  static const double dubaiCenterLat = 25.2048;
  static const double dubaiCenterLng = 55.2708;

  // Storage Buckets
  static const String carImagesBucket = 'car-images';
  static const String documentsImagesBucket = 'documents';
  static const String signaturesBucket = 'signatures';
  static const String userAvatarsBucket = 'user-avatars';

  // App Store URLs
  static const String appStoreUrl =
      'https://apps.apple.com/app/dubai-car-rental';
  static const String playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.dubaicarrental';

  // Support
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+971501234567';
  static const String whatsappUrl = 'https://wa.me/971501234567';

  // Booking Constants
  static const int minBookingHours = 24;
  static const int maxBookingDays = 30;
  static const double securityDeposit = 500.0;
  static const double deliveryFee = 50.0;

  // File Upload Constants
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 300);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(milliseconds: 800);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
}

class AppRoutes {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String verifyEmail = '/verify-email';
  static const String home = '/home';
  static const String carDetails = '/car-details';
  static const String booking = '/booking';
  static const String signature = '/signature';
  static const String payment = '/payment';
  static const String paymentSuccess = '/payment-success';
  static const String orders = '/orders';
  static const String orderDetails = '/order-details';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String support = '/support';
  static const String terms = '/terms';
  static const String privacy = '/privacy';
  static const String about = '/about';
}
