import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/booking_model.dart';
import '../models/car_model.dart';
import 'car_service.dart';
import 'stripe_service.dart';
import 'mock_payment_service.dart';

class BookingService {
  final CarService _carService = CarService();
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get user bookings with status filter
  Future<List<BookingModel>> getUserBookings({
    required String userId,
    String? status,
  }) async {
    return await _carService.getBookings(userId: userId, status: status);
  }

  // Get booking statistics
  Future<Map<String, dynamic>> getBookingStats(String userId) async {
    return await _carService.getBookingStats(userId);
  }

  // Create a new booking
  Future<BookingModel?> createBooking({
    required String userId,
    required String carId,
    required DateTime startDate,
    required DateTime endDate,
    required double totalAmount,
    required Map<String, dynamic> pickupLocation,
    required Map<String, dynamic> returnLocation,
    double? dailyRate,
  }) async {
    return await _carService.createBooking(
      userId: userId,
      carId: carId,
      startDate: startDate,
      endDate: endDate,
      totalAmount: totalAmount,
      pickupLocation: pickupLocation,
      returnLocation: returnLocation,
      dailyRate: dailyRate,
    );
  }

  // Cancel a booking
  Future<bool> cancelBooking(String bookingId) async {
    return await _carService.cancelBooking(bookingId);
  }

  // Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    return await _carService.updateBookingStatus(bookingId, status);
  }

  // Get booking by ID
  Future<BookingModel?> getBookingById(String bookingId) async {
    try {
      final response = await _supabase
          .from('bookings')
          .select()
          .eq('id', bookingId)
          .single();

      return BookingModel.fromJson(response);
    } catch (e) {
      print('Error fetching booking by ID: $e');
      return null;
    }
  }

  // Validate booking dates
  bool validateBookingDates(DateTime startDate, DateTime endDate) {
    final now = DateTime.now();

    // Check if start date is not in the past
    if (startDate.isBefore(now.subtract(Duration(hours: 1)))) {
      return false;
    }

    // Check if end date is after start date
    if (endDate.isBefore(startDate)) {
      return false;
    }

    // Check if booking duration is reasonable (max 30 days)
    if (endDate.difference(startDate).inDays > 30) {
      return false;
    }

    return true;
  }

  // Calculate booking duration in days
  int calculateBookingDuration(DateTime startDate, DateTime endDate) {
    return endDate.difference(startDate).inDays +
        1; // Include both start and end day
  }

  // Calculate booking total with pricing breakdown
  Map<String, double> calculateBookingTotal({
    required double dailyRate,
    required int days,
    double securityDeposit = 0.0,
    double deliveryFee = 0.0,
    double discountAmount = 0.0,
    double taxRate = 0.05, // 5% VAT
  }) {
    final subtotal = dailyRate * days;
    final taxAmount = subtotal * taxRate;
    final total =
        subtotal + taxAmount + deliveryFee + securityDeposit - discountAmount;

    return {
      'dailyRate': dailyRate,
      'days': days.toDouble(),
      'subtotal': subtotal,
      'taxAmount': taxAmount,
      'deliveryFee': deliveryFee,
      'securityDeposit': securityDeposit,
      'discountAmount': discountAmount,
      'total': total,
    };
  }

  // Check if car is available for booking period
  Future<bool> isCarAvailable({
    required String carId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // Check for overlapping bookings - simplified approach
      final confirmedBookings = await _supabase
          .from('bookings')
          .select('id')
          .eq('car_id', carId)
          .eq('status', 'confirmed');

      final activeBookings = await _supabase
          .from('bookings')
          .select('id')
          .eq('car_id', carId)
          .eq('status', 'active');

      // If no overlapping bookings found, car is available
      return (confirmedBookings as List).isEmpty &&
          (activeBookings as List).isEmpty;
    } catch (e) {
      print('Error checking car availability: $e');
      return false;
    }
  }

  // Get upcoming bookings for a user
  Future<List<BookingModel>> getUpcomingBookings(String userId) async {
    final now = DateTime.now();
    try {
      final response = await _supabase
          .from('bookings')
          .select()
          .eq('user_id', userId)
          .gte('pickup_date_time', now.toIso8601String())
          .or('status.eq.confirmed,status.eq.active')
          .order('pickup_date_time', ascending: true);

      return (response as List)
          .map((booking) => BookingModel.fromJson(booking))
          .toList();
    } catch (e) {
      print('Error fetching upcoming bookings: $e');
      return [];
    }
  }

  // Get booking history for a user
  Future<List<BookingModel>> getBookingHistory(String userId) async {
    try {
      final response = await _supabase
          .from('bookings')
          .select()
          .eq('user_id', userId)
          .or('status.eq.completed,status.eq.cancelled')
          .order('created_at', ascending: false);

      return (response as List)
          .map((booking) => BookingModel.fromJson(booking))
          .toList();
    } catch (e) {
      print('Error fetching booking history: $e');
      return [];
    }
  }

  // Update booking payment status
  Future<bool> updatePaymentStatus(
    String bookingId,
    String paymentStatus,
  ) async {
    try {
      await _supabase
          .from('bookings')
          .update({'payment_status': paymentStatus}).eq('id', bookingId);
      return true;
    } catch (e) {
      print('Error updating payment status: $e');
      return false;
    }
  }

  // Process payment with Stripe
  Future<bool> processPaymentWithStripe({
    required BuildContext context,
    required BookingModel booking,
    required CarModel car,
  }) async {
    try {
      Map<String, dynamic> paymentResult;

      // في بيئة التطوير، استخدم المحاكي بدلاً من Stripe الحقيقي
      if (kDebugMode) {
        paymentResult = await MockPaymentService.processPayment(
          context: context,
          amount: booking.totalAmount,
          currency: 'AED',
          description: 'حجز ${car.make} ${car.model}',
        );
      } else {
        // في الإنتاج، استخدم Stripe الحقيقي
        final paymentSuccess = await StripeService.processPayment(
          context: context,
          amount: booking.totalAmount,
          carName: '${car.make} ${car.model}',
          bookingId: booking.bookingNumber,
        );
        paymentResult = {'success': paymentSuccess};
      }

      final paymentSuccess = paymentResult['success'] == true;

      if (paymentSuccess) {
        // تحديث حالة الحجز في قاعدة البيانات
        await updateBookingStatus(booking.id, 'confirmed');
        await updatePaymentStatus(booking.id, 'paid');

        if (!context.mounted) return true;

        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '✅ ${paymentResult['message'] ?? 'تم الدفع بنجاح!'} رقم الحجز: ${booking.bookingNumber}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );

        return true;
      } else {
        if (!context.mounted) return false;

        // عرض رسالة فشل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ ${paymentResult['message'] ?? 'فشل في الدفع'}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في معالجة الدفع: $e');

      if (!context.mounted) return false;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ فشل في معالجة الدفع: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );

      return false;
    }
  }

  // Create booking with Stripe payment
  Future<BookingModel?> createBookingWithPayment({
    required BuildContext context,
    required String userId,
    required CarModel car,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> pickupLocation,
    required Map<String, dynamic> returnLocation,
    required Map<String, dynamic> customerInfo,
  }) async {
    try {
      print('🔧 BookingService: بدء إنشاء الحجز مع الدفع');
      print('🚗 السيارة: ${car.make} ${car.model}');
      print('📅 من ${startDate.toLocal()} إلى ${endDate.toLocal()}');
      // حساب التكلفة
      final costs = calculateBookingCosts(
        dailyRate: car.dailyPrice,
        startDate: startDate,
        endDate: endDate,
      );

      print('💰 التكلفة الإجمالية: ${costs['totalAmount']} درهم');

      // إنشاء الحجز
      print('📝 إنشاء الحجز في قاعدة البيانات...');
      final booking = await createBooking(
        userId: userId,
        carId: car.id,
        startDate: startDate,
        endDate: endDate,
        totalAmount: costs['totalAmount']!,
        pickupLocation: pickupLocation,
        returnLocation: returnLocation,
        dailyRate: car.dailyPrice,
      );

      if (booking != null) {
        print('✅ تم إنشاء الحجز برقم: ${booking.bookingNumber}');

        // معالجة الدفع
        print('💳 بدء معالجة الدفع...');
        final paymentSuccess = await processPaymentWithStripe(
          context: context,
          booking: booking,
          car: car,
        );

        print('💳 نتيجة الدفع: ${paymentSuccess ? 'نجح' : 'فشل'}');

        if (paymentSuccess) {
          print('🎉 تم الحجز والدفع بنجاح!');
          return booking;
        } else {
          // إلغاء الحجز في حالة فشل الدفع
          await cancelBooking(booking.id);
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في إنشاء الحجز مع الدفع: $e');
      return null;
    }
  }

  // Calculate booking costs
  Map<String, double> calculateBookingCosts({
    required double dailyRate,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    // حساب عدد الأيام بشكل صحيح
    final daysDifference = endDate.difference(startDate).inDays;
    final numberOfDays = daysDifference <= 0 ? 1 : daysDifference + 1;

    print('💰 حساب التكلفة:');
    print('   السعر اليومي: $dailyRate درهم');
    print('   عدد الأيام: $numberOfDays');

    final subtotal = dailyRate * numberOfDays;
    final taxAmount = subtotal * 0.05; // ضريبة 5%
    final securityDeposit = dailyRate; // تأمين يساوي يوم واحد
    final totalAmount = subtotal + taxAmount;

    print('   المجموع الفرعي: $subtotal درهم');
    print('   الضريبة: $taxAmount درهم');
    print('   المجموع النهائي: $totalAmount درهم');

    return {
      'numberOfDays': numberOfDays.toDouble(),
      'subtotal': subtotal,
      'taxAmount': taxAmount,
      'securityDeposit': securityDeposit,
      'totalAmount': totalAmount,
    };
  }

  // Check if car is available for booking
  Future<bool> isCarAvailableForBooking({
    required String carId,
    required DateTime startDate,
    required DateTime endDate,
    String? excludeBookingId,
  }) async {
    try {
      var query = _supabase
          .from('bookings')
          .select('id')
          .eq('car_id', carId)
          .inFilter('status', ['confirmed', 'active']).or(
        'and(pickup_date.lte.${endDate.toIso8601String()},return_date.gte.${startDate.toIso8601String()})',
      );

      if (excludeBookingId != null) {
        query = query.neq('id', excludeBookingId);
      }

      final response = await query;
      return response.isEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق من توفر السيارة: $e');
      return false;
    }
  }
}
