import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_colors.dart';

class CustomBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<BottomNavItem> items;

  const CustomBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 70, // تقليل الارتفاع من 80 إلى 70
          padding: const EdgeInsets.symmetric(
              horizontal: 16, vertical: 6), // تقليل الـ padding
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == currentIndex;

              return Expanded(
                // استخدام Expanded لضمان التوزيع المتساوي
                child: GestureDetector(
                  onTap: () {
                    if (!isSelected) {
                      // Haptic feedback للتفاعل
                      HapticFeedback.selectionClick();
                      onTap(index);
                    }
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    padding: EdgeInsets.symmetric(
                      horizontal:
                          isSelected ? 8 : 4, // تقليل الـ padding الأفقي
                      vertical: 6, // تقليل الـ padding العمودي
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary.withOpacity(0.15)
                          : Colors.transparent,
                      borderRadius:
                          BorderRadius.circular(20), // تقليل الـ border radius
                      border: isSelected
                          ? Border.all(
                              color: AppColors.primary.withOpacity(0.3),
                              width: 1,
                            )
                          : null,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment:
                          MainAxisAlignment.center, // توسيط العناصر
                      children: [
                        // أيقونة متحركة
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          transitionBuilder: (child, animation) {
                            return ScaleTransition(
                              scale: animation,
                              child: child,
                            );
                          },
                          child: Icon(
                            isSelected ? item.activeIcon : item.icon,
                            key: ValueKey(isSelected),
                            color: isSelected
                                ? AppColors.primary
                                : AppColors.onSurfaceVariant,
                            size: isSelected ? 24 : 22, // تقليل حجم الأيقونات
                          ),
                        ),
                        const SizedBox(height: 2), // تقليل المسافة
                        // تسمية متحركة
                        Flexible(
                          // استخدام Flexible لمنع الـ overflow
                          child: AnimatedDefaultTextStyle(
                            duration: const Duration(milliseconds: 300),
                            style: TextStyle(
                              fontSize: isSelected ? 11 : 10, // تقليل حجم الخط
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.onSurfaceVariant,
                              height: 1.0, // تقليل ارتفاع السطر
                            ),
                            child: Text(
                              item.label,
                              textAlign: TextAlign.center,
                              maxLines: 1, // تحديد سطر واحد فقط
                              overflow:
                                  TextOverflow.ellipsis, // في حالة النص الطويل
                            ),
                          ),
                        ),
                        // مؤشر سفلي
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          height: 2, // تقليل ارتفاع المؤشر
                          width: isSelected ? 16 : 0, // تقليل عرض المؤشر
                          margin: const EdgeInsets.only(
                              top: 2), // تقليل المسافة العلوية
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(1),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

class BottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  const BottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
