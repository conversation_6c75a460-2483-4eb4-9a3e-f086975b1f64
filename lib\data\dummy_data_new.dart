import '../models/car_model.dart';
import '../models/booking_model.dart';

class DummyData {
  static List<CarModel> getDummyCars() {
    return [
      CarModel(
        id: '1',
        name: 'BMW X5 2023',
        make: 'BMW',
        brand: 'BMW',
        model: 'X5',
        type: 'SUV',
        year: 2023,
        category: 'luxury',
        fuelType: 'gasoline',
        transmission: 'automatic',
        seats: 5,
        doors: 4,
        color: 'أبيض',
        plateNumber: 'A 12345',
        licensePlate: 'A 12345',
        images: [
          'https://images.unsplash.com/photo-**********-3004980ad54e?w=500',
        ],
        imageUrls: [
          'https://images.unsplash.com/photo-**********-3004980ad54e?w=500',
        ],
        thumbnailUrl:
            'https://images.unsplash.com/photo-**********-3004980ad54e?w=500',
        hourlyPrice: 50.0,
        dailyRate: 350.0,
        dailyPrice: 350.0,
        weeklyRate: 2100.0,
        weeklyPrice: 2100.0,
        monthlyRate: 8400.0,
        monthlyPrice: 8400.0,
        securityDeposit: 1000.0,
        description: 'سيارة BMW X5 فاخرة ومريحة للسفر والرحلات العائلية',
        descriptionAr: 'سيارة BMW X5 فاخرة ومريحة للسفر والرحلات العائلية',
        descriptionEn: 'Luxury BMW X5, comfortable for travel and family trips',
        features: ['GPS', 'Bluetooth', 'AC', 'Leather Seats'],
        safetyFeatures: ['ABS', 'Airbags', 'Stability Control'],
        comfortFeatures: ['Leather Seats', 'Climate Control', 'Premium Sound'],
        rating: 4.8,
        totalReviews: 45,
        totalRatings: 45,
        isAvailable: true,
        availabilityStatus: 'available',
        status: CarStatus.active,
        mileage: 12000,
        locationLat: 25.2048,
        locationLng: 55.2708,
        locationAddress: 'Dubai Mall, Dubai',
        currentLocation: {
          'lat': 25.2048,
          'lng': 55.2708,
          'address': 'Dubai Mall'
        },
        insurance: {
          'provider': 'AXA',
          'policy_number': 'INS001',
          'coverage': 'comprehensive'
        },
        insuranceExpiry: DateTime.now().add(Duration(days: 365)),
        registrationExpiry: DateTime.now().add(Duration(days: 180)),
        lastServiceDate: DateTime.now().subtract(Duration(days: 30)),
        nextServiceDate: DateTime.now().add(Duration(days: 90)),
        pickupLocations: ['Dubai Mall', 'Airport', 'City Center'],
        isFeatured: true,
        createdAt: DateTime.now().subtract(Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      CarModel(
        id: '2',
        name: 'Mercedes C-Class 2023',
        make: 'Mercedes',
        brand: 'Mercedes-Benz',
        model: 'C-Class',
        type: 'Sedan',
        year: 2023,
        category: 'luxury',
        fuelType: 'gasoline',
        transmission: 'automatic',
        seats: 5,
        doors: 4,
        color: 'أسود',
        plateNumber: 'B 67890',
        licensePlate: 'B 67890',
        images: [
          'https://images.unsplash.com/photo-1563720223185-11003d516935?w=500',
        ],
        imageUrls: [
          'https://images.unsplash.com/photo-1563720223185-11003d516935?w=500',
        ],
        thumbnailUrl:
            'https://images.unsplash.com/photo-1563720223185-11003d516935?w=500',
        hourlyPrice: 40.0,
        dailyRate: 280.0,
        dailyPrice: 280.0,
        weeklyRate: 1680.0,
        weeklyPrice: 1680.0,
        monthlyRate: 6720.0,
        monthlyPrice: 6720.0,
        securityDeposit: 800.0,
        description: 'سيارة Mercedes C-Class أنيقة ومريحة',
        descriptionAr: 'سيارة Mercedes C-Class أنيقة ومريحة',
        descriptionEn: 'Elegant and comfortable Mercedes C-Class',
        features: ['GPS', 'Bluetooth', 'AC', 'Sunroof'],
        safetyFeatures: ['ABS', 'Airbags', 'ESP'],
        comfortFeatures: ['Leather Interior', 'Sunroof', 'Heated Seats'],
        rating: 4.6,
        totalReviews: 32,
        totalRatings: 32,
        isAvailable: true,
        availabilityStatus: 'available',
        status: CarStatus.active,
        mileage: 8000,
        locationLat: 25.1972,
        locationLng: 55.2796,
        locationAddress: 'Burj Khalifa, Dubai',
        currentLocation: {
          'lat': 25.1972,
          'lng': 55.2796,
          'address': 'Burj Khalifa'
        },
        insurance: {
          'provider': 'Zurich',
          'policy_number': 'INS002',
          'coverage': 'full'
        },
        insuranceExpiry: DateTime.now().add(Duration(days: 300)),
        registrationExpiry: DateTime.now().add(Duration(days: 150)),
        lastServiceDate: DateTime.now().subtract(Duration(days: 20)),
        nextServiceDate: DateTime.now().add(Duration(days: 100)),
        pickupLocations: ['Burj Khalifa', 'DIFC', 'JBR'],
        isFeatured: true,
        createdAt: DateTime.now().subtract(Duration(days: 20)),
        updatedAt: DateTime.now(),
      ),
      CarModel(
        id: '3',
        name: 'Toyota Camry 2022',
        make: 'Toyota',
        brand: 'Toyota',
        model: 'Camry',
        type: 'Sedan',
        year: 2022,
        category: 'standard',
        fuelType: 'gasoline',
        transmission: 'automatic',
        seats: 5,
        doors: 4,
        color: 'فضي',
        plateNumber: 'C 11111',
        licensePlate: 'C 11111',
        images: [
          'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=500',
        ],
        imageUrls: [
          'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=500',
        ],
        thumbnailUrl:
            'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=500',
        hourlyPrice: 22.0,
        dailyRate: 150.0,
        dailyPrice: 150.0,
        weeklyRate: 900.0,
        weeklyPrice: 900.0,
        monthlyRate: 3600.0,
        monthlyPrice: 3600.0,
        securityDeposit: 500.0,
        description: 'سيارة Toyota Camry اقتصادية وموثوقة',
        descriptionAr: 'سيارة Toyota Camry اقتصادية وموثوقة',
        descriptionEn: 'Economic and reliable Toyota Camry',
        features: ['GPS', 'Bluetooth', 'AC'],
        safetyFeatures: ['ABS', 'Airbags'],
        comfortFeatures: ['AC', 'Radio', 'USB'],
        rating: 4.4,
        totalReviews: 28,
        totalRatings: 28,
        isAvailable: true,
        availabilityStatus: 'available',
        status: CarStatus.active,
        mileage: 25000,
        locationLat: 25.0760,
        locationLng: 55.1368,
        locationAddress: 'Dubai Marina',
        currentLocation: {
          'lat': 25.0760,
          'lng': 55.1368,
          'address': 'Dubai Marina'
        },
        insurance: {
          'provider': 'Emirates Insurance',
          'policy_number': 'INS003',
          'coverage': 'basic'
        },
        insuranceExpiry: DateTime.now().add(Duration(days: 200)),
        registrationExpiry: DateTime.now().add(Duration(days: 120)),
        lastServiceDate: DateTime.now().subtract(Duration(days: 60)),
        nextServiceDate: DateTime.now().add(Duration(days: 60)),
        pickupLocations: ['Marina', 'JLT', 'Media City'],
        isFeatured: false,
        createdAt: DateTime.now().subtract(Duration(days: 60)),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  static List<BookingModel> getDummyBookings() {
    return [
      BookingModel(
        id: '1',
        bookingNumber: 'DCR-2024-001',
        userId: 'user123',
        carId: '1',
        pickupDate: DateTime.now().add(Duration(days: 1)),
        returnDate: DateTime.now().add(Duration(days: 4)),
        pickupDateTime: DateTime.now().add(Duration(days: 1, hours: 10)),
        returnDateTime: DateTime.now().add(Duration(days: 4, hours: 10)),
        pickupLocation: {
          'latitude': 25.1972,
          'longitude': 55.2796,
          'address': 'دبي مول، دبي',
          'city': 'Dubai',
          'type': 'landmark'
        },
        returnLocation: {
          'latitude': 25.1972,
          'longitude': 55.2796,
          'address': 'دبي مول، دبي',
          'city': 'Dubai',
          'type': 'landmark'
        },
        pickupLocationLat: 25.1972,
        pickupLocationLng: 55.2796,
        pickupAddress: 'دبي مول، دبي',
        pickupType: 'pickup',
        returnLocationLat: 25.1972,
        returnLocationLng: 55.2796,
        returnAddress: 'دبي مول، دبي',
        returnType: 'return',
        rentalDurationDays: 3,
        hourlyRate: 50.0,
        dailyRate: 350.0,
        subtotal: 1050.0,
        taxAmount: 52.5,
        securityDeposit: 1000.0,
        deliveryFee: 15.0,
        additionalFees: 0.0,
        discountAmount: 0.0,
        totalAmount: 1117.5,
        pricing: {
          'baseRate': 350.0,
          'days': 3,
          'subtotal': 1050.0,
          'deliveryFee': 15.0,
          'securityDeposit': 1000.0,
          'tax': 52.5,
          'discount': 0.0,
          'total': 1117.5,
          'currency': 'AED'
        },
        status: BookingStatus.confirmed,
        paymentStatus: 'pending',
        documentUrls: [],
        pickupInspectionPhotos: [],
        returnInspectionPhotos: [],
        termsAccepted: true,
        termsAcceptedAt: DateTime.now().subtract(Duration(hours: 2)),
        additionalDrivers: [],
        statusHistory: [
          {
            'status': 'pending',
            'timestamp':
                DateTime.now().subtract(Duration(hours: 2)).toIso8601String(),
            'note': 'Booking created'
          },
          {
            'status': 'confirmed',
            'timestamp':
                DateTime.now().subtract(Duration(hours: 1)).toIso8601String(),
            'note': 'Booking confirmed by admin'
          }
        ],
        createdAt: DateTime.now().subtract(Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(Duration(hours: 1)),
      ),
      BookingModel(
        id: '2',
        bookingNumber: 'DCR-2024-002',
        userId: 'user123',
        carId: '2',
        pickupDate: DateTime.now().subtract(Duration(days: 10)),
        returnDate: DateTime.now().subtract(Duration(days: 7)),
        pickupDateTime: DateTime.now().subtract(Duration(days: 10, hours: 14)),
        returnDateTime: DateTime.now().subtract(Duration(days: 7, hours: 14)),
        pickupLocation: {
          'latitude': 25.1972,
          'longitude': 55.2796,
          'address': 'برج خليفة، دبي',
          'city': 'Dubai',
          'type': 'landmark'
        },
        returnLocation: {
          'latitude': 25.1972,
          'longitude': 55.2796,
          'address': 'برج خليفة، دبي',
          'city': 'Dubai',
          'type': 'landmark'
        },
        pickupLocationLat: 25.1972,
        pickupLocationLng: 55.2796,
        pickupAddress: 'برج خليفة، دبي',
        pickupType: 'pickup',
        returnLocationLat: 25.1972,
        returnLocationLng: 55.2796,
        returnAddress: 'برج خليفة، دبي',
        returnType: 'return',
        rentalDurationDays: 3,
        hourlyRate: 40.0,
        dailyRate: 280.0,
        subtotal: 840.0,
        taxAmount: 42.0,
        securityDeposit: 800.0,
        deliveryFee: 15.0,
        additionalFees: 0.0,
        discountAmount: 50.0,
        totalAmount: 847.0,
        pricing: {
          'baseRate': 280.0,
          'days': 3,
          'subtotal': 840.0,
          'deliveryFee': 15.0,
          'securityDeposit': 800.0,
          'tax': 42.0,
          'discount': 50.0,
          'total': 847.0,
          'currency': 'AED'
        },
        status: BookingStatus.completed,
        paymentStatus: 'completed',
        actualPickupDate:
            DateTime.now().subtract(Duration(days: 10, hours: 14)),
        actualReturnDate: DateTime.now().subtract(Duration(days: 7, hours: 14)),
        documentUrls: [],
        pickupInspectionPhotos: [],
        returnInspectionPhotos: [],
        termsAccepted: true,
        termsAcceptedAt: DateTime.now().subtract(Duration(days: 10, hours: 16)),
        additionalDrivers: [],
        statusHistory: [
          {
            'status': 'pending',
            'timestamp': DateTime.now()
                .subtract(Duration(days: 10, hours: 16))
                .toIso8601String(),
            'note': 'Booking created'
          },
          {
            'status': 'confirmed',
            'timestamp': DateTime.now()
                .subtract(Duration(days: 10, hours: 15))
                .toIso8601String(),
            'note': 'Booking confirmed'
          },
          {
            'status': 'active',
            'timestamp': DateTime.now()
                .subtract(Duration(days: 10, hours: 14))
                .toIso8601String(),
            'note': 'Car picked up'
          },
          {
            'status': 'completed',
            'timestamp': DateTime.now()
                .subtract(Duration(days: 7, hours: 14))
                .toIso8601String(),
            'note': 'Car returned successfully'
          }
        ],
        createdAt: DateTime.now().subtract(Duration(days: 10, hours: 16)),
        updatedAt: DateTime.now().subtract(Duration(days: 7, hours: 14)),
      ),
    ];
  }
}
