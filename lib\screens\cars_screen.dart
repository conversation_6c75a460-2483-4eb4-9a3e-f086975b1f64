import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/car_service.dart';
import '../models/car_model.dart';
import '../widgets/car_card.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/professional_app_bar.dart';
import '../constants/app_colors.dart';

class CarsScreen extends StatefulWidget {
  const CarsScreen({Key? key}) : super(key: key);

  @override
  State<CarsScreen> createState() => _CarsScreenState();
}

class _CarsScreenState extends State<CarsScreen> {
  final CarService _carService = CarService();
  final ScrollController _scrollController = ScrollController();

  List<CarModel> _cars = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Filters
  String? _selectedCategory;
  double? _minPrice;
  double? _maxPrice;
  bool _availableOnly = true;
  String _searchQuery = '';

  final List<String> _categories = [
    'الكل',
    'economy',
    'standard',
    'premium',
    'luxury',
    'exotic',
    'suv'
  ];

  @override
  void initState() {
    super.initState();
    _loadCars();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreCars();
    }
  }

  Future<void> _loadCars({bool refresh = false}) async {
    if (!mounted) return;

    if (refresh) {
      setState(() {
        _currentPage = 1;
        _hasMoreData = true;
        _cars.clear();
      });
    }

    setState(() {
      _isLoading = refresh || _cars.isEmpty;
      _error = null;
    });

    try {
      List<CarModel> cars;

      if (_searchQuery.isNotEmpty) {
        // Use search for queries
        cars = await _carService.searchCars(
          query: _searchQuery,
          limit: 20,
        );
      } else {
        // Regular get cars with filters
        cars = await _carService.getCars(
          page: _currentPage,
          category: _selectedCategory == 'الكل' ? null : _selectedCategory,
          minPrice: _minPrice,
          maxPrice: _maxPrice,
          availableOnly: _availableOnly,
        );
      }

      if (mounted) {
        setState(() {
          if (refresh || _currentPage == 1) {
            _cars = cars;
          } else {
            _cars.addAll(cars);
          }
          _isLoading = false;
          _isLoadingMore = false;
          _hasMoreData = cars.length >= 20;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'حدث خطأ في تحميل البيانات';
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    }
  }

  Future<void> _loadMoreCars() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    await _loadCars();
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildFilterDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SimpleAppBar(
        title: 'السيارات المتاحة',
        actions: [
          IconButton(
            icon: const Icon(Icons.tune_rounded, color: Colors.white),
            onPressed: _showFilterDialog,
            tooltip: 'فلترة',
          ),
        ],
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Search Bar Container
          Container(
            padding: const EdgeInsets.all(20),
            color: Colors.white,
            child: SearchBarWidget(
              hintText: 'البحث عن سيارة...',
              onTap: () {
                _showSearchDialog();
              },
            ),
          ),

          // Categories Filter
          Container(
            height: 50,
            color: Colors.white,
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category ||
                    (category == 'الكل' && _selectedCategory == null);

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategory = category == 'الكل' ? null : category;
                    });
                    _loadCars(refresh: true);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(left: 12),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected ? AppColors.primary : Colors.grey[300]!,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        _getCategoryDisplayName(category),
                        style: GoogleFonts.cairo(
                          color:
                              isSelected ? Colors.white : AppColors.onSurface,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Cars Grid
          Expanded(
            child: _buildCarsGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildCarsGrid() {
    if (_isLoading && _cars.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null && _cars.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: GoogleFonts.cairo(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadCars(refresh: true),
              child: Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_cars.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.directions_car_outlined, color: Colors.grey, size: 48),
            const SizedBox(height: 16),
            Text(
              'لا توجد سيارات متاحة',
              style: GoogleFonts.cairo(color: Colors.grey, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadCars(refresh: true),
      child: GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          childAspectRatio: 1.2,
          mainAxisSpacing: 16,
        ),
        itemCount: _cars.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _cars.length) {
            return const Center(child: CircularProgressIndicator());
          }

          final car = _cars[index];
          return CarCard(
            car: car,
            onTap: () {
              // Navigate to car details
            },
          );
        },
      ),
    );
  }

  Widget _buildFilterDialog() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تصفية السيارات',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // Price Range
          Text(
            'نطاق السعر (درهم/يوم)',
            style: GoogleFonts.cairo(fontWeight: FontWeight.w500),
          ),
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'من',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _minPrice = double.tryParse(value);
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'إلى',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _maxPrice = double.tryParse(value);
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Available Only
          CheckboxListTile(
            title: Text('السيارات المتاحة فقط'),
            value: _availableOnly,
            onChanged: (value) {
              setState(() {
                _availableOnly = value ?? true;
              });
            },
          ),

          const SizedBox(height: 20),

          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedCategory = null;
                      _minPrice = null;
                      _maxPrice = null;
                      _availableOnly = true;
                      _searchQuery = '';
                    });
                    Navigator.pop(context);
                    _loadCars(refresh: true);
                  },
                  child: Text('إعادة تعيين'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _loadCars(refresh: true);
                  },
                  child: Text('تطبيق'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('البحث عن سيارة'),
        content: TextField(
          decoration: InputDecoration(
            hintText: 'اسم السيارة أو العلامة التجارية',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            _searchQuery = value;
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _loadCars(refresh: true);
            },
            child: Text('بحث'),
          ),
        ],
      ),
    );
  }

  String _getCategoryDisplayName(String category) {
    switch (category.toLowerCase()) {
      case 'الكل':
        return 'الكل';
      case 'economy':
        return 'اقتصادية';
      case 'standard':
        return 'عادية';
      case 'premium':
        return 'مميزة';
      case 'luxury':
        return 'فاخرة';
      case 'exotic':
        return 'سوبر';
      case 'suv':
        return 'SUV';
      default:
        return category;
    }
  }
}
