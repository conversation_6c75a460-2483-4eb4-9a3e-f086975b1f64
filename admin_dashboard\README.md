
# لوحة تحكم تطبيق تأجير السيارات - دبي

## نظرة عامة

لوحة تحكم إدارية شاملة لتطبيق تأجير السيارات في دبي، مصممة باللغة العربية مع دعم كامل للـ RTL والخطوط العربية.

## الميزات الرئيسية

### 🚗 إدارة السيارات
- عرض جميع السيارات المتاحة مع الصور والمواصفات
- إضافة وتعديل وحذف السيارات
- إدارة أسعار التأجير والعروض الخاصة
- تتبع حالة السيارات (متاحة، محجوزة، في الصيانة)
- معرض صور للسيارات مع إمكانية الرفع المتعدد

### 📅 إدارة الحجوزات
- عرض جميع الحجوزات مع الفلترة والبحث
- تتبع مراحل الحجز (معلقة، مؤكدة، جارية، مكتملة، ملغية)
- إدارة مواعيد الاستلام والتسليم
- طباعة عقود الإيجار والفواتير
- نظام إشعارات للحجوزات المقتربة

### 👥 إدارة المستخدمين
- عرض ملفات العملاء الشخصية
- إدارة التحقق من الهوية ورخص القيادة
- تعليق وإلغاء تعليق الحسابات
- تتبع تاريخ الحجوزات لكل عميل
- إدارة المستندات المطلوبة

### 💳 إدارة المدفوعات
- عرض جميع المعاملات المالية
- دعم متعدد لطرق الدفع (بطاقات ائتمان، تحويل مصرفي، محافظ رقمية)
- معالجة استردادات المبالغ
- تقارير مالية مفصلة
- إدارة الرسوم والعمولات

### ⭐ إدارة التقييمات
- مراجعة تقييمات العملاء قبل النشر
- الرد على التقييمات والتعليقات
- إدارة التقييمات المبلغ عنها
- إحصائيات التقييمات والرضا العام

### 📊 لوحة المعلومات والإحصائيات
- مؤشرات الأداء الرئيسية (KPIs)
- رسوم بيانية للإيرادات والحجوزات
- إحصائيات الاستخدام والنمو
- تقارير دورية قابلة للتصدير

## التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحة الأساسي
- **CSS3**: التصميم والتنسيق مع دعم RTL
- **Vanilla JavaScript (ES6+)**: البرمجة والتفاعل
- **Chart.js**: الرسوم البيانية والإحصائيات
- **Font Awesome**: الأيقونات
- **Cairo Font**: الخط العربي الأساسي

### Backend (متوقع)
- **PostgreSQL**: قاعدة البيانات الرئيسية
- **Supabase**: خدمات Backend والمصادقة
- **Node.js**: خادم API (اختياري)

## هيكل المشروع

```
admin_dashboard/
├── index.html                 # الصفحة الرئيسية
├── css/
│   └── styles.css            # ملف التنسيقات الأساسي
├── js/
│   ├── app.js               # التطبيق الرئيسي والملاحة
│   ├── dashboard.js         # لوحة المعلومات والإحصائيات
│   ├── cars.js              # إدارة السيارات
│   ├── bookings.js          # إدارة الحجوزات
│   ├── users.js             # إدارة المستخدمين
│   ├── payments.js          # إدارة المدفوعات
│   ├── reviews.js           # إدارة التقييمات
│   └── api.js               # التواصل مع API
└── README.md                # هذا الملف
```

## إعداد المشروع

### المتطلبات الأساسية
- متصفح ويب حديث يدعم ES6+
- خادم ويب محلي (اختياري للتطوير)
- قاعدة بيانات PostgreSQL

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd dubai_car_rental/admin_dashboard
```

2. **إعداد الخادم المحلي** (اختياري)
```bash
# باستخدام Python
python -m http.server 8000

# أو باستخدام Node.js
npx http-server -p 8000
```

3. **فتح لوحة التحكم**
- افتح `index.html` في المتصفح مباشرة
- أو اذهب إلى `http://localhost:8000`

### إعداد قاعدة البيانات

1. **إنشاء قاعدة البيانات**
```sql
-- استخدم ملف complete_schema.sql لإنشاء الجداول
psql -U username -d database_name -f complete_schema.sql
```

2. **إعداد Supabase**
- أنشئ مشروع جديد في Supabase
- ارفع ملف complete_schema.sql
- احصل على مفاتيح API وعدّل في api.js

3. **تحديث إعدادات API**
```javascript
// في api.js - الإعدادات الحالية لمشروع دبي لتأجير السيارات
const API_CONFIG = {
    baseURL: 'https://zvzaixlygdhloganycjt.supabase.co/rest/v1',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2emFpeGx5Z2RobG9nYW55Y2p0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjE4ODUsImV4cCI6MjA3MDM5Nzg4NX0.xI_qvs6kupVt0EM6nnFzgfe1QBmUkelNO7c0O64GCXQ',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2emFpeGx5Z2RobG9nYW55Y2p0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjE4ODUsImV4cCI6MjA3MDM5Nzg4NX0.xI_qvs6kupVt0EM6nnFzgfe1QBmUkelNO7c0O64GCXQ'
    }
};
```

## استخدام لوحة التحكم

### تسجيل الدخول
- استخدم بيانات المدير المعرّفة في قاعدة البيانات
- يتم التحقق من الصلاحيات تلقائياً

### التنقل
- استخدم الشريط الجانبي للتنقل بين الأقسام
- كل قسم له صفحة منفصلة مع أدوات الإدارة المناسبة

### إدارة البيانات
- **العرض**: جداول قابلة للفلترة والبحث
- **الإضافة**: نماذج مفصلة للبيانات الجديدة  
- **التعديل**: تحرير البيانات الموجودة
- **الحذف**: إزالة آمنة مع تأكيدات

### التقارير والتصدير
- تصدير البيانات بصيغة CSV
- طباعة التقارير
- إحصائيات مرئية بالرسوم البيانية

## خصائص التصميم

### دعم RTL
- جميع النصوص والعناصر تدعم الاتجاه من اليمين لليسار
- تخطيط مُحسّن للغة العربية

### التجاوب
- تصميم متجاوب يعمل على جميع أحجام الشاشات
- تحسين للأجهزة المحمولة واللوحية

### إمكانية الوصول
- دعم قراء الشاشة
- اختصارات لوحة المفاتيح
- ألوان متباينة للوضوح

## واجهة برمجة التطبيقات (API)

### نقاط النهاية الأساسية

#### السيارات
```
GET    /api/cars              # جلب جميع السيارات
GET    /api/cars/:id          # جلب سيارة محددة
POST   /api/cars              # إضافة سيارة جديدة  
PUT    /api/cars/:id          # تحديث سيارة
DELETE /api/cars/:id          # حذف سيارة
```

#### الحجوزات
```
GET    /api/bookings          # جلب جميع الحجوزات
GET    /api/bookings/:id      # جلب حجز محدد
POST   /api/bookings          # إنشاء حجز جديد
PUT    /api/bookings/:id      # تحديث حجز
DELETE /api/bookings/:id      # إلغاء حجز
```

#### المستخدمين
```
GET    /api/users             # جلب جميع المستخدمين
GET    /api/users/:id         # جلب مستخدم محدد
PUT    /api/users/:id/status  # تحديث حالة المستخدم
PUT    /api/users/:id/verify  # تحقيق المستخدم
```

#### المدفوعات
```
GET    /api/payments          # جلب جميع المدفوعات
POST   /api/payments/:id/refund # استرداد مبلغ
```

#### التقييمات
```
GET    /api/reviews           # جلب جميع التقييمات  
PUT    /api/reviews/:id/verify # تحقيق تقييم
DELETE /api/reviews/:id       # حذف تقييم
```

## معلومات قاعدة البيانات الحالية

### إعدادات Supabase
- **URL المشروع**: `https://zvzaixlygdhloganycjt.supabase.co`
- **مفتاح API العام**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2emFpeGx5Z2RobG9nYW55Y2p0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjE4ODUsImV4cCI6MjA3MDM5Nzg4NX0.xI_qvs6kupVt0EM6nnFzgfe1QBmUkelNO7c0O64GCXQ`
- **حالة الاتصال**: نشط ومُعد للاستخدام المباشر

### الجداول الأساسية
- `profiles` - ملفات المستخدمين الشخصية
- `cars` - بيانات السيارات والمخزون
- `bookings` - حجوزات السيارات ودورة حياتها
- `payments` - المعاملات المالية والمدفوعات
- `reviews` - تقييمات ومراجعات العملاء
- `locations` - مواقع الاستلام والتسليم
- `coupons` - كوبونات الخصم والعروض
- `notifications` - نظام الإشعارات
- `app_settings` - إعدادات التطبيق العامة

### نصائح مهمة للاستخدام
⚠️ **تأكد من رفع ملف `complete_schema.sql` إلى مشروع Supabase قبل الاستخدام**
⚠️ **قم بإعداد Row Level Security (RLS) للحماية**
⚠️ **أنشئ حسابات المديرين في جدول `profiles` مع `role = 'admin'`**

## الأمان والصلاحيات

### المصادقة
- تسجيل دخول آمن بـ JWT tokens
- انتهاء صلاحية الجلسة التلقائي
- تشفير كلمات المرور

### الصلاحيات
- مستويات صلاحيات متعددة (admin, manager, support)
- تحكم في الوصول لكل قسم
- سجل لجميع العمليات الحساسة

### حماية البيانات
- تشفير البيانات الحساسة
- النسخ الاحتياطية التلقائية
- سجلات التدقيق المفصلة

## الاستكشاف وإصلاح الأخطاء

### مشاكل شائعة

1. **عدم ظهور البيانات**
   - تحقق من اتصال قاعدة البيانات
   - راجع مفاتيح API في api.js
   - تأكد من وجود البيانات في الجداول

2. **مشاكل في التصميم**
   - تأكد من تحميل ملف CSS
   - تحقق من دعم RTL في المتصفح
   - راجع console للأخطاء

3. **بطء في الأداء**
   - قم بتحسين استعلامات قاعدة البيانات
   - استخدم الفلترة من جانب الخادم
   - فعّل الكاش للبيانات الثابتة

### سجلات الأخطاء
- راجع browser console للأخطاء
- تحقق من Network tab للطلبات الفاشلة
- استخدم developer tools للتشخيص

## التطوير والمساهمة

### معايير الكود
- استخدم ES6+ features
- اتبع تسمية متسقة للمتغيرات والوظائف
- أضف تعليقات باللغة العربية للوضوح

### إضافة ميزات جديدة
1. أنشئ ملف JavaScript منفصل للميزة
2. أضف الأنماط المطلوبة في styles.css
3. ربط الميزة بالواجهة الرئيسية
4. اختبر على مختلف المتصفحات والأجهزة

### اختبار التطبيق
- اختبار وظيفي لجميع الميزات
- اختبار التجاوب على الأجهزة المختلفة  
- اختبار الأمان والصلاحيات
- اختبار الأداء تحت الضغط

## الدعم والمساعدة

### الموارد المفيدة
- [Chart.js Documentation](https://www.chartjs.org/docs/)
- [Font Awesome Icons](https://fontawesome.com/icons)
- [MDN Web Docs](https://developer.mozilla.org/)
- [Supabase Documentation](https://supabase.com/docs)

### التواصل
- للأسئلة التقنية: راجع documentation
- لتقرير الأخطاء: أنشئ issue في المستودع
- لطلبات الميزات: اكتب feature request

## الترخيص

هذا المشروع مُرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل الكاملة.

---

## ملاحظات إضافية

### نصائح للأداء الأمثل
- استخدم lazy loading للصور
- قم بتحسين حجم البيانات المنقولة
- فعّل الضغط للملفات الثابتة
- استخدم CDN للمكتبات الخارجية

### تحديثات مستقبلية مقترحة
- إضافة نظام تقارير متقدم
- دمج مع خرائط Google لتتبع السيارات
- إضافة نظام إشعارات push
- تطوير mobile app للإدارة
- دعم multiple languages
- نظام chat مع العملاء

تم تطوير هذه اللوحة بعناية لتوفر تجربة إدارية شاملة ومتميزة لتطبيق تأجير السيارات. 🚗✨
