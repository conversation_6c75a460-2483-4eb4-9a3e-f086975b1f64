import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';

import '../../providers/document_provider.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../../widgets/document_upload_card.dart';
import '../../widgets/verification_status_card.dart';

class DocumentsScreen extends StatefulWidget {
  @override
  _DocumentsScreenState createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends State<DocumentsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DocumentProvider>(context, listen: false).loadUserDocuments();
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppTheme.lightGrey,
      appBar: AppBar(
        title: Text(l10n.documents),
        elevation: 0,
      ),
      body: Consumer<DocumentProvider>(
        builder: (context, documentProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // حالة التحقق
                VerificationStatusCard(
                  status: documentProvider.verificationStatus,
                  notes: documentProvider.verificationNotes,
                ),

                const SizedBox(height: 24),

                // العنوان
                Text(
                  l10n.requiredDocuments,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),

                const SizedBox(height: 8),

                Text(
                  l10n.documentsDescription,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.mediumGrey,
                      ),
                ),

                const SizedBox(height: 20),

                // الهوية الإماراتية - الوجه الأمامي
                DocumentUploadCard(
                  document: documentProvider
                          .documents[DocumentType.emiratesIdFront] ??
                      DocumentInfo(type: DocumentType.emiratesIdFront),
                  title: l10n.emiratesIdFront,
                  description: l10n.emiratesIdFrontDescription,
                  onTakePhoto: () => _pickImageAndUpload(
                      ImageSource.camera, DocumentType.emiratesIdFront),
                  onSelectFromGallery: () => _pickImageAndUpload(
                      ImageSource.gallery, DocumentType.emiratesIdFront),
                  onDelete: () => _showDeleteConfirmation(
                    context,
                    () => documentProvider
                        .deleteDocument(DocumentType.emiratesIdFront),
                  ),
                ),

                const SizedBox(height: 16),

                // الهوية الإماراتية - الوجه الخلفي
                DocumentUploadCard(
                  document:
                      documentProvider.documents[DocumentType.emiratesIdBack] ??
                          DocumentInfo(type: DocumentType.emiratesIdBack),
                  title: l10n.emiratesIdBack,
                  description: l10n.emiratesIdBackDescription,
                  onTakePhoto: () => _pickImageAndUpload(
                      ImageSource.camera, DocumentType.emiratesIdBack),
                  onSelectFromGallery: () => _pickImageAndUpload(
                      ImageSource.gallery, DocumentType.emiratesIdBack),
                  onDelete: () => _showDeleteConfirmation(
                    context,
                    () => documentProvider
                        .deleteDocument(DocumentType.emiratesIdBack),
                  ),
                ),

                const SizedBox(height: 16),

                // رخصة القيادة
                DocumentUploadCard(
                  document:
                      documentProvider.documents[DocumentType.drivingLicense] ??
                          DocumentInfo(type: DocumentType.drivingLicense),
                  title: l10n.drivingLicense,
                  description: l10n.drivingLicenseDescription,
                  onTakePhoto: () => _pickImageAndUpload(
                      ImageSource.camera, DocumentType.drivingLicense),
                  onSelectFromGallery: () => _pickImageAndUpload(
                      ImageSource.gallery, DocumentType.drivingLicense),
                  onDelete: () => _showDeleteConfirmation(
                    context,
                    () => documentProvider
                        .deleteDocument(DocumentType.drivingLicense),
                  ),
                ),

                const SizedBox(height: 24),

                // العنوان للوثائق الاختيارية
                Text(
                  l10n.optionalDocuments,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),

                const SizedBox(height: 8),

                Text(
                  l10n.optionalDocumentsDescription,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.mediumGrey,
                      ),
                ),

                const SizedBox(height: 20),

                // جواز السفر
                DocumentUploadCard(
                  document: documentProvider.documents[DocumentType.passport] ??
                      DocumentInfo(type: DocumentType.passport),
                  title: l10n.passport,
                  description: l10n.passportDescription,
                  onTakePhoto: () => _pickImageAndUpload(
                      ImageSource.camera, DocumentType.passport),
                  onSelectFromGallery: () => _pickImageAndUpload(
                      ImageSource.gallery, DocumentType.passport),
                  onDelete: () => _showDeleteConfirmation(
                    context,
                    () =>
                        documentProvider.deleteDocument(DocumentType.passport),
                  ),
                ),

                const SizedBox(height: 32),

                // زر إرسال للمراجعة
                if (documentProvider.areRequiredDocumentsUploaded &&
                    documentProvider.verificationStatus == 'pending')
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: documentProvider.isUploading
                          ? null
                          : () => _submitForReview(documentProvider),
                      icon: const Icon(Icons.send),
                      label: Text(l10n.submitForReview),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),

                // معلومات إضافية
                const SizedBox(height: 24),
                _buildInfoSection(l10n),

                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoSection(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.infoBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.infoBlue.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.infoBlue,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                l10n.importantInformation,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.infoBlue,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoItem(l10n.documentTip1),
          _buildInfoItem(l10n.documentTip2),
          _buildInfoItem(l10n.documentTip3),
          _buildInfoItem(l10n.documentTip4),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6, right: 8, left: 8),
            decoration: BoxDecoration(
              color: AppTheme.infoBlue,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.darkGrey,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, VoidCallback onConfirm) {
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteDocument),
        content: Text(l10n.deleteDocumentConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorRed,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  void _submitForReview(DocumentProvider documentProvider) async {
    final l10n = AppLocalizations.of(context)!;

    final success = await documentProvider.submitDocumentsForReview();

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 12),
              Expanded(child: Text(l10n.documentsSubmittedForReview)),
            ],
          ),
          backgroundColor: AppTheme.successGreen,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.all(16),
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 12),
              Expanded(
                  child: Text(documentProvider.error ?? l10n.submitFailed)),
            ],
          ),
          backgroundColor: AppTheme.errorRed,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  Future<void> _pickImageAndUpload(
      ImageSource source, DocumentType type) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        final documentProvider =
            Provider.of<DocumentProvider>(context, listen: false);

        switch (type) {
          case DocumentType.emiratesIdFront:
            await documentProvider.uploadEmiratesIdFront(image.path);
            break;
          case DocumentType.emiratesIdBack:
            await documentProvider.uploadEmiratesIdBack(image.path);
            break;
          case DocumentType.drivingLicense:
            await documentProvider.uploadDrivingLicense(image.path);
            break;
          case DocumentType.passport:
            await documentProvider.uploadPassport(image.path);
            break;
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفع الصورة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
