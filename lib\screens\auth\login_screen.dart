import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_button.dart';
import '../../widgets/social_login_button.dart';
import '../../widgets/simple_app_logo.dart';
import '../../l10n/app_localizations.dart';
import '../../utils/app_theme.dart';
import '../main_navigation_screen.dart';
import 'register_screen.dart';
import 'forgot_password_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSavedCredentials();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadSavedCredentials() async {
    // تحميل بيانات الدخول المحفوظة إذا كانت متاحة
    // يمكن تنفيذ هذا باستخدام SharedPreferences أو Secure Storage
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _signInWithEmail() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.signInWithEmail(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (success && mounted) {
        // حفظ بيانات الدخول إذا كان المستخدم يريد تذكرها
        if (_rememberMe) {
          await _saveCredentials();
        }

        _navigateToMain();
      } else if (mounted) {
        _showErrorSnackBar(AppLocalizations.of(context)!.loginFailed);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(_getErrorMessage(e.toString()));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _signInWithGoogle() async {
    setState(() => _isLoading = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.signInWithGoogle();

      if (success && mounted) {
        _navigateToMain();
      } else if (mounted) {
        _showErrorSnackBar(AppLocalizations.of(context)!.googleSignInFailed);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(_getErrorMessage(e.toString()));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveCredentials() async {
    // حفظ بيانات الدخول بشكل آمن
    // يمكن استخدام flutter_secure_storage
  }

  String _getErrorMessage(String error) {
    final l10n = AppLocalizations.of(context)!;

    if (error.contains('invalid-email')) {
      return l10n.emailInvalid;
    } else if (error.contains('user-not-found')) {
      return l10n.userNotFound;
    } else if (error.contains('wrong-password')) {
      return l10n.wrongPassword;
    } else if (error.contains('too-many-requests')) {
      return l10n.tooManyAttempts;
    } else {
      return l10n.loginFailed;
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.errorRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: AppLocalizations.of(context)!.dismiss,
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  void _navigateToMain() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            MainNavigationScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 20),

                      // الشعار والعنوان
                      _buildHeader(l10n),

                      const SizedBox(height: 40),

                      // نموذج تسجيل الدخول
                      _buildLoginForm(l10n),

                      const SizedBox(height: 24),

                      // خيارات إضافية
                      _buildRememberMeAndForgotPassword(l10n, languageProvider),

                      const SizedBox(height: 32),

                      // زر تسجيل الدخول
                      LoadingButton(
                        onPressed: _signInWithEmail,
                        isLoading: _isLoading,
                        text: l10n.signIn,
                        icon: Icon(Icons.login),
                      ),

                      const SizedBox(height: 24),

                      // فاصل "أو"
                      _buildDivider(l10n),

                      const SizedBox(height: 24),

                      // أزرار تسجيل الدخول الاجتماعي
                      _buildSocialLoginButtons(l10n),

                      const SizedBox(height: 32),

                      // رابط إنشاء حساب
                      _buildSignUpLink(l10n),

                      const SizedBox(height: 20),

                      // روابط إضافية
                      _buildAdditionalLinks(l10n),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(AppLocalizations l10n) {
    return Column(
      children: [
        // شعار الشركة
        Hero(
          tag: 'app_logo',
          child: SimpleAppLogo(
            size: 100,
            onTap: () {
              // يمكن إضافة أي إجراء عند النقر على الشعار
            },
          ),
        ),

        const SizedBox(height: 24),

        // العنوان الرئيسي
        Text(
          l10n.welcomeBack,
          style: Theme.of(context).textTheme.displaySmall?.copyWith(
                color: AppTheme.darkGrey,
                fontWeight: FontWeight.bold,
              ),
        ),

        const SizedBox(height: 8),

        // العنوان الفرعي
        Text(
          l10n.loginSubtitle,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.mediumGrey,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm(AppLocalizations l10n) {
    return Column(
      children: [
        // حقل البريد الإلكتروني
        CustomTextField(
          controller: _emailController,
          labelText: l10n.email,
          hintText: l10n.enterEmail,
          prefixIcon: Icon(Icons.email_outlined),
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return l10n.emailRequired;
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return l10n.emailInvalid;
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // حقل كلمة المرور
        CustomTextField(
          controller: _passwordController,
          labelText: l10n.password,
          hintText: l10n.enterPassword,
          prefixIcon: Icon(Icons.lock_outline),
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.done,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility : Icons.visibility_off,
              color: AppTheme.mediumGrey,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return l10n.passwordRequired;
            }
            if (value.length < 6) {
              return l10n.passwordTooShort;
            }
            return null;
          },
          onFieldSubmitted: (_) => _signInWithEmail(),
        ),
      ],
    );
  }

  Widget _buildRememberMeAndForgotPassword(
      AppLocalizations l10n, LanguageProvider languageProvider) {
    return Row(
      children: [
        // تذكرني
        Expanded(
          child: Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              Expanded(
                child: Text(
                  l10n.rememberMe,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ),

        // نسيت كلمة المرور
        TextButton(
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => ForgotPasswordScreen()),
            );
          },
          child: Text(
            l10n.forgotPassword,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
      ],
    );
  }

  Widget _buildDivider(AppLocalizations l10n) {
    return Row(
      children: [
        const Expanded(child: Divider()),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            l10n.orContinueWith,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.mediumGrey,
                ),
          ),
        ),
        const Expanded(child: Divider()),
      ],
    );
  }

  Widget _buildSocialLoginButtons(AppLocalizations l10n) {
    return Column(
      children: [
        // زر Google
        SocialLoginButton(
          onPressed: _isLoading
              ? () {}
              : () {
                  _signInWithGoogle();
                },
          iconAsset: 'assets/images/google_logo.png',
          text: l10n.loginWithGoogle,
          backgroundColor: Colors.white,
          textColor: AppTheme.darkGrey,
          borderColor: AppTheme.borderGrey,
        ),

        const SizedBox(height: 12),

        // يمكن إضافة أزرار أخرى مثل Apple أو Facebook
        // SocialLoginButton(
        //   onPressed: _signInWithApple,
        //   icon: 'assets/images/apple_logo.png',
        //   text: l10n.loginWithApple,
        //   backgroundColor: Colors.black,
        //   textColor: Colors.white,
        // ),
      ],
    );
  }

  Widget _buildSignUpLink(AppLocalizations l10n) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          l10n.dontHaveAccount,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).push(
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    RegisterScreen(),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  );
                },
                transitionDuration: const Duration(milliseconds: 300),
              ),
            );
          },
          child: Text(
            l10n.signUp,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalLinks(AppLocalizations l10n) {
    return Wrap(
      alignment: WrapAlignment.center,
      children: [
        TextButton(
          onPressed: () {
            // الانتقال لصفحة الشروط والأحكام
          },
          child: Text(
            l10n.termsAndConditions,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.mediumGrey,
                  decoration: TextDecoration.underline,
                ),
          ),
        ),
        Text(
          ' • ',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.mediumGrey,
              ),
        ),
        TextButton(
          onPressed: () {
            // الانتقال لصفحة سياسة الخصوصية
          },
          child: Text(
            l10n.privacyPolicy,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.mediumGrey,
                  decoration: TextDecoration.underline,
                ),
          ),
        ),
      ],
    );
  }
}
