import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/car_model.dart';
import '../constants/app_colors.dart';
import 'booking_dialog.dart';

class CarCard extends StatelessWidget {
  final CarModel car;
  final VoidCallback? onTap;
  final bool showBookButton;

  const CarCard({
    Key? key,
    required this.car,
    this.onTap,
    this.showBookButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final availableWidth = constraints.maxWidth;

        // حساب أبعاد البطاقة بناءً على المساحة المتاحة
        final cardWidth = availableWidth;
        final imageHeight = screenWidth < 400
            ? 120.0
            : screenWidth < 600
                ? 140.0
                : 160.0;
        final fontSize = screenWidth < 400
            ? 13.0
            : screenWidth < 600
                ? 14.0
                : 16.0;
        final smallFontSize = screenWidth < 400
            ? 9.0
            : screenWidth < 600
                ? 10.0
                : 12.0;
        final priceFontSize = screenWidth < 400
            ? 14.0
            : screenWidth < 600
                ? 16.0
                : 18.0;
        final padding = screenWidth < 400
            ? 8.0
            : screenWidth < 600
                ? 10.0
                : 12.0;

        return GestureDetector(
          onTap: onTap,
          child: Container(
            width: cardWidth,
            constraints: BoxConstraints(
              maxWidth: screenWidth < 400 ? double.infinity : 300,
              minHeight: screenWidth < 400 ? 250 : 280,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth < 400 ? 12 : 16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  blurRadius: screenWidth < 400 ? 6 : 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Car Image
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(screenWidth < 400 ? 12 : 16),
                      ),
                      child: Container(
                        height: imageHeight,
                        width: double.infinity,
                        child: car.thumbnailUrl?.isNotEmpty == true
                            ? CachedNetworkImage(
                                imageUrl: car.thumbnailUrl!,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  color: Colors.grey[200],
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: Colors.grey[200],
                                  child: Icon(
                                    Icons.directions_car,
                                    size: screenWidth < 400 ? 32 : 40,
                                    color: Colors.grey,
                                  ),
                                ),
                              )
                            : Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.directions_car,
                                  size: screenWidth < 400 ? 32 : 40,
                                  color: Colors.grey,
                                ),
                              ),
                      ),
                    ),

                    // Availability Badge
                    Positioned(
                      top: screenWidth < 400 ? 8 : 12,
                      right: screenWidth < 400 ? 8 : 12,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: screenWidth < 400 ? 6 : 8,
                          vertical: screenWidth < 400 ? 3 : 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getAvailabilityColor(),
                          borderRadius: BorderRadius.circular(
                              screenWidth < 400 ? 10 : 12),
                        ),
                        child: Text(
                          _getAvailabilityText(),
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: screenWidth < 400 ? 9 : 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),

                    // Rating Badge
                    if (car.rating > 0)
                      Positioned(
                        top: screenWidth < 400 ? 8 : 12,
                        left: screenWidth < 400 ? 8 : 12,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth < 400 ? 6 : 8,
                            vertical: screenWidth < 400 ? 3 : 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(
                                screenWidth < 400 ? 10 : 12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: screenWidth < 400 ? 10 : 12,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                car.rating.toStringAsFixed(1),
                                style: GoogleFonts.cairo(
                                  color: Colors.white,
                                  fontSize: screenWidth < 400 ? 9 : 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),

                // Car Details
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(padding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Car Name
                        Text(
                          '${car.make} ${car.model}',
                          style: GoogleFonts.cairo(
                            fontSize: fontSize,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        SizedBox(height: screenWidth < 400 ? 2 : 3),

                        // Car Year & Category
                        Text(
                          '${car.year} • ${_getCategoryName(car.category)}',
                          style: GoogleFonts.cairo(
                            fontSize: smallFontSize,
                            color: AppColors.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        SizedBox(height: screenWidth < 400 ? 4 : 6),

                        // Car Features - محسنة للمساحة الصغيرة
                        if (screenWidth >= 400)
                          Flexible(
                            child: Wrap(
                              spacing: 4,
                              runSpacing: 2,
                              children: [
                                _buildFeatureChip(
                                    Icons.people, '${car.seats}', screenWidth),
                                _buildFeatureChip(
                                    Icons.local_gas_station,
                                    _getFuelTypeName(car.fuelType),
                                    screenWidth),
                                _buildFeatureChip(
                                    Icons.settings,
                                    _getTransmissionName(car.transmission),
                                    screenWidth),
                              ],
                            ),
                          )
                        else
                          // عرض مبسط للجوال
                          Row(
                            children: [
                              _buildFeatureChip(
                                  Icons.people, '${car.seats}', screenWidth),
                              const SizedBox(width: 4),
                              _buildFeatureChip(
                                  Icons.settings,
                                  _getTransmissionName(car.transmission),
                                  screenWidth),
                            ],
                          ),

                        SizedBox(height: screenWidth < 400 ? 8 : 12),

                        // Price and Book Button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Expanded(
                              flex: 2,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    '${car.dailyPrice.toStringAsFixed(0)} درهم',
                                    style: GoogleFonts.cairo(
                                      fontSize: priceFontSize,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primary,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    'في اليوم',
                                    style: GoogleFonts.cairo(
                                      fontSize: smallFontSize,
                                      color: AppColors.onSurfaceVariant,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (showBookButton && _isCarBookable())
                              GestureDetector(
                                onTap: () => _showBookingDialog(context),
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth < 400 ? 12 : 16,
                                    vertical: screenWidth < 400 ? 6 : 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(
                                        screenWidth < 400 ? 16 : 20),
                                  ),
                                  child: Text(
                                    'احجز',
                                    style: GoogleFonts.cairo(
                                      color: Colors.white,
                                      fontSize: smallFontSize,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureChip(IconData icon, String label, double screenWidth) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth < 400 ? 4 : 6,
        vertical: screenWidth < 400 ? 2 : 3,
      ),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(screenWidth < 400 ? 6 : 8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: screenWidth < 400 ? 10 : 12,
            color: AppColors.onSurfaceVariant,
          ),
          SizedBox(width: screenWidth < 400 ? 1 : 2),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: screenWidth < 400 ? 9 : 10,
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category.toLowerCase()) {
      case 'economy':
        return 'اقتصادية';
      case 'standard':
        return 'عادية';
      case 'premium':
        return 'مميزة';
      case 'luxury':
        return 'فاخرة';
      case 'exotic':
        return 'سوبر';
      case 'suv':
        return 'SUV';
      default:
        return category;
    }
  }

  Color _getAvailabilityColor() {
    if (car.isAvailable) {
      return Colors.green.withOpacity(0.9);
    } else if (car.availabilityStatus == 'maintenance') {
      return Colors.orange.withOpacity(0.9);
    } else {
      return Colors.red.withOpacity(0.9);
    }
  }

  String _getAvailabilityText() {
    if (car.isAvailable) {
      return 'متاحة';
    } else if (car.availabilityStatus == 'maintenance') {
      return 'صيانة';
    } else if (car.availabilityStatus == 'rented') {
      return 'مؤجرة';
    } else {
      return 'غير متاحة';
    }
  }

  bool _isCarBookable() {
    return car.isAvailable && car.availabilityStatus == 'available';
  }

  // عرض نافذة الحجز السريع
  void _showBookingDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => BookingDialog(car: car),
    );
  }

  String _getFuelTypeName(String fuelType) {
    switch (fuelType.toLowerCase()) {
      case 'gasoline':
      case 'petrol':
        return 'بنزين';
      case 'diesel':
        return 'ديزل';
      case 'hybrid':
        return 'هجين';
      case 'electric':
        return 'كهربائي';
      default:
        return fuelType;
    }
  }

  String _getTransmissionName(String transmission) {
    switch (transmission.toLowerCase()) {
      case 'automatic':
        return 'أوتوماتيك';
      case 'manual':
        return 'يدوي';
      case 'cvt':
        return 'CVT';
      default:
        return transmission;
    }
  }
}
