<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال مع Supabase</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار الاتصال مع قاعدة بيانات Supabase</h1>
        
        <div class="test-controls">
            <button onclick="testBasicConnection()">اختبار الاتصال الأساسي</button>
            <button onclick="testCarsEndpoint()">اختبار جدول السيارات</button>
            <button onclick="testProfilesEndpoint()">اختبار جدول المستخدمين</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const SUPABASE_URL = 'https://zvzaixlygdhloganycjt.supabase.co/rest/v1';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2emFpeGx5Z2RobG9nYW55Y2p0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjE4ODUsImV4cCI6MjA3MDM5Nzg4NX0.xI_qvs6kupVt0EM6nnFzgfe1QBmUkelNO7c0O64GCXQ';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testBasicConnection() {
            addResult('بدء اختبار الاتصال الأساسي...', 'info');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/`, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    addResult('✅ الاتصال الأساسي نجح!', 'success');
                    addResult(`حالة الاستجابة: ${response.status} ${response.statusText}`, 'info');
                } else {
                    addResult(`❌ فشل الاتصال الأساسي: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في الاتصال الأساسي: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }
        
        async function testCarsEndpoint() {
            addResult('بدء اختبار جدول السيارات...', 'info');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/cars?limit=1`, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ اختبار جدول السيارات نجح!', 'success');
                    addResult(`عدد السيارات المسترجعة: ${data.length}`, 'info');
                    if (data.length > 0) {
                        addResult(`<pre>بيانات السيارة الأولى:\n${JSON.stringify(data[0], null, 2)}</pre>`, 'info');
                    }
                } else {
                    const errorText = await response.text();
                    addResult(`❌ فشل اختبار جدول السيارات: ${response.status} ${response.statusText}`, 'error');
                    addResult(`<pre>تفاصيل الخطأ:\n${errorText}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار جدول السيارات: ${error.message}`, 'error');
                console.error('Cars endpoint error:', error);
            }
        }
        
        async function testProfilesEndpoint() {
            addResult('بدء اختبار جدول المستخدمين...', 'info');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/profiles?limit=1`, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ اختبار جدول المستخدمين نجح!', 'success');
                    addResult(`عدد المستخدمين المسترجعين: ${data.length}`, 'info');
                    if (data.length > 0) {
                        addResult(`<pre>بيانات المستخدم الأول:\n${JSON.stringify(data[0], null, 2)}</pre>`, 'info');
                    }
                } else {
                    const errorText = await response.text();
                    addResult(`❌ فشل اختبار جدول المستخدمين: ${response.status} ${response.statusText}`, 'error');
                    addResult(`<pre>تفاصيل الخطأ:\n${errorText}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار جدول المستخدمين: ${error.message}`, 'error');
                console.error('Profiles endpoint error:', error);
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addResult('تم تحميل صفحة الاختبار بنجاح', 'success');
            addResult(`URL قاعدة البيانات: ${SUPABASE_URL}`, 'info');
            addResult('انقر على الأزرار أعلاه لبدء الاختبارات', 'info');
        });
    </script>
</body>
</html>