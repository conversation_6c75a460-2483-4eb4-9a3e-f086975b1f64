<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - تأجير السيارات في دبي</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <img src="../assets/logos/app_logo.png" alt="شعار التطبيق" class="logo-img">
                <span class="logo-text">لوحة التحكم</span>
            </div>
            
            <div class="header-actions">
                <div class="notifications">
                    <button class="notification-btn" onclick="toggleNotifications()">
                        <i class="fas fa-bell"></i>
                        <span class="notification-count" id="notificationCount">5</span>
                    </button>
                    <div class="notifications-dropdown" id="notificationsDropdown">
                        <div class="notifications-header">
                            <h3>الإشعارات</h3>
                            <button onclick="markAllAsRead()">تم قراءة الكل</button>
                        </div>
                        <div class="notifications-list" id="notificationsList">
                            <!-- سيتم تحميل الإشعارات ديناميكياً -->
                        </div>
                    </div>
                </div>
                
                <div class="user-profile">
                    <button class="profile-btn" onclick="toggleProfileMenu()">
                        <img src="https://via.placeholder.com/40" alt="صورة المستخدم" class="profile-img">
                        <span>المدير العام</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="profile-dropdown" id="profileDropdown">
                        <a href="#" onclick="showProfile()"><i class="fas fa-user"></i> الملف الشخصي</a>
                        <a href="#" onclick="showSettings()"><i class="fas fa-cog"></i> الإعدادات</a>
                        <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active">
                        <a href="#dashboard" onclick="showSection('dashboard')" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#cars" onclick="showSection('cars')" class="nav-link">
                            <i class="fas fa-car"></i>
                            <span>إدارة السيارات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#bookings" onclick="showSection('bookings')" class="nav-link">
                            <i class="fas fa-calendar-alt"></i>
                            <span>الحجوزات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#users" onclick="showSection('users')" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>المستخدمين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#payments" onclick="showSection('payments')" class="nav-link">
                            <i class="fas fa-credit-card"></i>
                            <span>المدفوعات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#reviews" onclick="showSection('reviews')" class="nav-link">
                            <i class="fas fa-star"></i>
                            <span>التقييمات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#offices" onclick="showSection('offices')" class="nav-link">
                            <i class="fas fa-building"></i>
                            <span>المكاتب</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#locations" onclick="showSection('locations')" class="nav-link">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>المواقع</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#coupons" onclick="showSection('coupons')" class="nav-link">
                            <i class="fas fa-percent"></i>
                            <span>الكوبونات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#reports" onclick="showSection('reports')" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" onclick="showSection('settings')" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Loading Indicator -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <p>جاري التحميل...</p>
            </div>

            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="section-header">
                    <h1>لوحة التحكم الرئيسية</h1>
                    <div class="date-range-picker">
                        <input type="date" id="startDate" onchange="updateDashboard()">
                        <span>إلى</span>
                        <input type="date" id="endDate" onchange="updateDashboard()">
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon cars">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalCars">0</h3>
                            <p>إجمالي السيارات</p>
                            <span class="stat-change positive" id="carsChange">+5%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bookings">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalBookings">0</h3>
                            <p>إجمالي الحجوزات</p>
                            <span class="stat-change positive" id="bookingsChange">+12%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon revenue">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalRevenue">0</h3>
                            <p>إجمالي الإيرادات (د.إ)</p>
                            <span class="stat-change positive" id="revenueChange">+8%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon users">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalUsers">0</h3>
                            <p>المستخدمين المسجلين</p>
                            <span class="stat-change positive" id="usersChange">+15%</span>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="charts-row">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>إحصائيات الحجوزات الشهرية</h3>
                            <select id="bookingsChartPeriod" onchange="updateBookingsChart()">
                                <option value="month">الشهر الحالي</option>
                                <option value="quarter">الربع الحالي</option>
                                <option value="year">السنة الحالية</option>
                            </select>
                        </div>
                        <canvas id="bookingsChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>توزيع أنواع السيارات</h3>
                        </div>
                        <canvas id="carsChart"></canvas>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="recent-activities">
                    <h3>الأنشطة الأخيرة</h3>
                    <div class="activities-list" id="recentActivities">
                        <!-- سيتم تحميل الأنشطة ديناميكياً -->
                    </div>
                </div>
            </section>

            <!-- Cars Section -->
            <section id="cars" class="content-section">
                <div class="section-header">
                    <h1>إدارة السيارات</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="showAddCarModal()">
                            <i class="fas fa-plus"></i> إضافة سيارة جديدة
                        </button>
                        <button class="btn btn-secondary" onclick="exportCars()">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </button>
                    </div>
                </div>

                <div class="filters-row">
                    <div class="search-box">
                        <input type="text" id="carsSearch" placeholder="البحث في السيارات..." onkeyup="filterCars()">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="carsCategory" onchange="filterCars()">
                        <option value="">جميع الفئات</option>
                        <option value="economy">اقتصادية</option>
                        <option value="standard">عادية</option>
                        <option value="premium">متميزة</option>
                        <option value="luxury">فاخرة</option>
                        <option value="exotic">خارقة</option>
                    </select>
                    <select id="carsStatus" onchange="filterCars()">
                        <option value="">جميع الحالات</option>
                        <option value="available">متاحة</option>
                        <option value="rented">مؤجرة</option>
                        <option value="maintenance">صيانة</option>
                        <option value="unavailable">غير متاحة</option>
                    </select>
                </div>

                <div class="table-container">
                    <table class="data-table" id="carsTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم السيارة</th>
                                <th>الطراز</th>
                                <th>الفئة</th>
                                <th>السعر اليومي</th>
                                <th>الحالة</th>
                                <th>التقييم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="carsTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="pagination" id="carsPagination">
                    <!-- سيتم إنشاؤها ديناميكياً -->
                </div>
            </section>

            <!-- Bookings Section -->
            <section id="bookings" class="content-section">
                <div class="section-header">
                    <h1>إدارة الحجوزات</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="showAddBookingModal()">
                            <i class="fas fa-plus"></i> حجز جديد
                        </button>
                        <button class="btn btn-secondary" onclick="exportBookings()">
                            <i class="fas fa-download"></i> تصدير الحجوزات
                        </button>
                    </div>
                </div>

                <div class="filters-row">
                    <div class="search-box">
                        <input type="text" id="bookingsSearch" placeholder="البحث برقم الحجز أو اسم العميل..." onkeyup="filterBookings()">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="bookingsStatus" onchange="filterBookings()">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="confirmed">مؤكدة</option>
                        <option value="active">نشطة</option>
                        <option value="completed">مكتملة</option>
                        <option value="cancelled">ملغاة</option>
                    </select>
                    <input type="date" id="bookingsDateFrom" onchange="filterBookings()">
                    <input type="date" id="bookingsDateTo" onchange="filterBookings()">
                </div>

                <div class="table-container">
                    <table class="data-table" id="bookingsTable">
                        <thead>
                            <tr>
                                <th>رقم الحجز</th>
                                <th>العميل</th>
                                <th>السيارة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>المبلغ الإجمالي</th>
                                <th>حالة الدفع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="bookingsTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="bookingsPagination">
                    <!-- سيتم إنشاؤها ديناميكياً -->
                </div>
            </section>

            <!-- Users Section -->
            <section id="users" class="content-section">
                <div class="section-header">
                    <h1>إدارة المستخدمين</h1>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="exportUsers()">
                            <i class="fas fa-download"></i> تصدير المستخدمين
                        </button>
                    </div>
                </div>

                <div class="filters-row">
                    <div class="search-box">
                        <input type="text" id="usersSearch" placeholder="البحث في المستخدمين..." onkeyup="filterUsers()">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="usersStatus" onchange="filterUsers()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="suspended">موقوف</option>
                        <option value="banned">محظور</option>
                    </select>
                    <select id="usersVerification" onchange="filterUsers()">
                        <option value="">جميع حالات التحقق</option>
                        <option value="verified">محقق</option>
                        <option value="pending">في الانتظار</option>
                        <option value="rejected">مرفوض</option>
                    </select>
                </div>

                <div class="table-container">
                    <table class="data-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>تاريخ التسجيل</th>
                                <th>حالة التحقق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="usersPagination">
                    <!-- سيتم إنشاؤها ديناميكياً -->
                </div>
            </section>

            <!-- Payments Section -->
            <section id="payments" class="content-section">
                <div class="section-header">
                    <h1>إدارة المدفوعات</h1>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="exportPayments()">
                            <i class="fas fa-download"></i> تصدير المدفوعات
                        </button>
                    </div>
                </div>

                <div class="filters-row">
                    <div class="search-box">
                        <input type="text" id="paymentsSearch" placeholder="البحث في المدفوعات..." onkeyup="filterPayments()">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="paymentsStatus" onchange="filterPayments()">
                        <option value="">جميع الحالات</option>
                        <option value="succeeded">نجح</option>
                        <option value="pending">في الانتظار</option>
                        <option value="failed">فشل</option>
                        <option value="refunded">مسترد</option>
                    </select>
                    <select id="paymentsType" onchange="filterPayments()">
                        <option value="">جميع الأنواع</option>
                        <option value="rental">إيجار</option>
                        <option value="deposit">ضمان</option>
                        <option value="additional">إضافي</option>
                        <option value="refund">استرداد</option>
                    </select>
                </div>

                <div class="table-container">
                    <table class="data-table" id="paymentsTable">
                        <thead>
                            <tr>
                                <th>معرف الدفع</th>
                                <th>رقم الحجز</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>نوع الدفع</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="paymentsTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="paymentsPagination">
                    <!-- سيتم إنشاؤها ديناميكياً -->
                </div>
            </section>

            <!-- Reviews Section -->
            <section id="reviews" class="content-section">
                <div class="section-header">
                    <h1>إدارة التقييمات</h1>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="exportReviews()">
                            <i class="fas fa-download"></i> تصدير التقييمات
                        </button>
                    </div>
                </div>

                <div class="filters-row">
                    <div class="search-box">
                        <input type="text" id="reviewsSearch" placeholder="البحث في التقييمات..." onkeyup="filterReviews()">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="reviewsRating" onchange="filterReviews()">
                        <option value="">جميع التقييمات</option>
                        <option value="5">5 نجوم</option>
                        <option value="4">4 نجوم</option>
                        <option value="3">3 نجوم</option>
                        <option value="2">2 نجوم</option>
                        <option value="1">1 نجمة</option>
                    </select>
                    <select id="reviewsVerification" onchange="filterReviews()">
                        <option value="">جميع الحالات</option>
                        <option value="verified">محقق</option>
                        <option value="pending">في الانتظار</option>
                    </select>
                </div>

                <div class="reviews-grid" id="reviewsGrid">
                    <!-- سيتم تحميل التقييمات ديناميكياً -->
                </div>

                <div class="pagination" id="reviewsPagination">
                    <!-- سيتم إنشاؤها ديناميكياً -->
                </div>
            </section>

            <!-- Offices Section -->
            <section id="offices" class="content-section">
                <div class="section-header">
                    <h1>إدارة المكاتب</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="showAddOfficeModal()">
                            <i class="fas fa-plus"></i> إضافة مكتب
                        </button>
                        <button class="btn btn-secondary" onclick="exportOffices()">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </button>
                        <button class="btn btn-success" onclick="refreshOffices()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalOffices">0</h3>
                            <p>إجمالي المكاتب</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="activeOffices">0</h3>
                            <p>المكاتب النشطة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalOfficeCars">0</h3>
                            <p>السيارات في المكاتب</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="coverageAreas">0</h3>
                            <p>المناطق المغطاة</p>
                        </div>
                    </div>
                </div>

                <div class="filters-row">
                    <div class="search-box">
                        <input type="text" id="searchOffices" placeholder="البحث في المكاتب..." onkeyup="filterOffices()">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="emirateFilter" onchange="filterOffices()">
                        <option value="">جميع الإمارات</option>
                        <option value="Dubai">دبي</option>
                        <option value="Abu Dhabi">أبوظبي</option>
                        <option value="Sharjah">الشارقة</option>
                        <option value="Ajman">عجمان</option>
                        <option value="Ras Al Khaimah">رأس الخيمة</option>
                        <option value="Fujairah">الفجيرة</option>
                        <option value="Umm Al Quwain">أم القيوين</option>
                    </select>
                    <select id="officeTypeFilter" onchange="filterOffices()">
                        <option value="">جميع الأنواع</option>
                        <option value="main">مكتب رئيسي</option>
                        <option value="branch">فرع</option>
                        <option value="pickup_point">نقطة استلام</option>
                        <option value="service_center">مركز خدمة</option>
                    </select>
                    <select id="statusFilter" onchange="filterOffices()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="maintenance">صيانة</option>
                        <option value="closed">مغلق</option>
                    </select>
                </div>

                <div class="table-container">
                    <table class="data-table" id="officesTable">
                        <thead>
                            <tr>
                                <th>رمز المكتب</th>
                                <th>اسم المكتب</th>
                                <th>الإمارة</th>
                                <th>المنطقة</th>
                                <th>النوع</th>
                                <th>السيارات</th>
                                <th>التقييم</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="officesTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="officesPagination">
                    <!-- سيتم إنشاؤها ديناميكياً -->
                </div>

                <div id="officesCount" class="results-count">
                    <!-- عدد النتائج -->
                </div>
            </section>

            <!-- Locations Section -->
            <section id="locations" class="content-section">
                <div class="section-header">
                    <h1>إدارة المواقع</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="showAddLocationModal()">
                            <i class="fas fa-plus"></i> إضافة موقع جديد
                        </button>
                    </div>
                </div>

                <div class="locations-grid" id="locationsGrid">
                    <!-- سيتم تحميل المواقع ديناميكياً -->
                </div>
            </section>

            <!-- Coupons Section -->
            <section id="coupons" class="content-section">
                <div class="section-header">
                    <h1>إدارة الكوبونات</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="showAddCouponModal()">
                            <i class="fas fa-plus"></i> إضافة كوبون جديد
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table" id="couponsTable">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>العنوان</th>
                                <th>نوع الخصم</th>
                                <th>قيمة الخصم</th>
                                <th>صالح من</th>
                                <th>صالح حتى</th>
                                <th>عدد الاستخدامات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="couponsTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Reports Section -->
            <section id="reports" class="content-section">
                <div class="section-header">
                    <h1>التقارير والإحصائيات</h1>
                    <div class="section-actions">
                        <select id="reportPeriod" onchange="updateReports()">
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                            <option value="quarter">هذا الربع</option>
                            <option value="year">هذا العام</option>
                        </select>
                    </div>
                </div>

                <div class="reports-grid">
                    <div class="report-card">
                        <h3>تقرير الإيرادات</h3>
                        <canvas id="revenueChart"></canvas>
                        <button class="btn btn-secondary" onclick="downloadReport('revenue')">تحميل التقرير</button>
                    </div>
                    
                    <div class="report-card">
                        <h3>تقرير الحجوزات</h3>
                        <canvas id="bookingsReportChart"></canvas>
                        <button class="btn btn-secondary" onclick="downloadReport('bookings')">تحميل التقرير</button>
                    </div>
                    
                    <div class="report-card">
                        <h3>أداء السيارات</h3>
                        <canvas id="carsPerformanceChart"></canvas>
                        <button class="btn btn-secondary" onclick="downloadReport('cars')">تحميل التقرير</button>
                    </div>
                    
                    <div class="report-card">
                        <h3>تحليل العملاء</h3>
                        <canvas id="customersChart"></canvas>
                        <button class="btn btn-secondary" onclick="downloadReport('customers')">تحميل التقرير</button>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="content-section">
                <div class="section-header">
                    <h1>إعدادات التطبيق</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>

                <div class="settings-tabs">
                    <div class="tabs-nav">
                        <button class="tab-btn active" onclick="showSettingsTab('general')">عام</button>
                        <button class="tab-btn" onclick="showSettingsTab('booking')">الحجوزات</button>
                        <button class="tab-btn" onclick="showSettingsTab('payment')">الدفع</button>
                        <button class="tab-btn" onclick="showSettingsTab('notifications')">الإشعارات</button>
                    </div>

                    <div class="tab-content active" id="generalSettings">
                        <div class="form-group">
                            <label for="appName">اسم التطبيق</label>
                            <input type="text" id="appName" value="تأجير السيارات في دبي">
                        </div>
                        <div class="form-group">
                            <label for="supportPhone">رقم الدعم الفني</label>
                            <input type="tel" id="supportPhone" value="+971-4-123-4567">
                        </div>
                        <div class="form-group">
                            <label for="supportEmail">البريد الإلكتروني للدعم</label>
                            <input type="email" id="supportEmail" value="<EMAIL>">
                        </div>
                    </div>

                    <div class="tab-content" id="bookingSettings">
                        <div class="form-group">
                            <label for="minRentalAge">الحد الأدنى لعمر المستأجر</label>
                            <input type="number" id="minRentalAge" value="21">
                        </div>
                        <div class="form-group">
                            <label for="maxRentalDays">أقصى عدد أيام للإيجار</label>
                            <input type="number" id="maxRentalDays" value="30">
                        </div>
                        <div class="form-group">
                            <label for="cancellationHours">ساعات الإلغاء المجاني</label>
                            <input type="number" id="cancellationHours" value="24">
                        </div>
                    </div>

                    <div class="tab-content" id="paymentSettings">
                        <div class="form-group">
                            <label for="defaultCurrency">العملة الافتراضية</label>
                            <select id="defaultCurrency">
                                <option value="AED">درهم إماراتي</option>
                                <option value="USD">دولار أمريكي</option>
                                <option value="EUR">يورو</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taxRate">معدل الضريبة (%)</label>
                            <input type="number" id="taxRate" value="5" step="0.01">
                        </div>
                    </div>

                    <div class="tab-content" id="notificationSettings">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <input type="checkbox" id="emailNotifications" checked>
                                    تفعيل الإشعارات عبر البريد الإلكتروني
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <input type="checkbox" id="smsNotifications" checked>
                                    تفعيل الإشعارات النصية
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <!-- Add Car Modal -->
    <div class="modal" id="addCarModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إضافة سيارة جديدة</h2>
                <button class="close-btn" onclick="closeModal('addCarModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addCarForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="carName">اسم السيارة</label>
                            <input type="text" id="carName" required>
                        </div>
                        <div class="form-group">
                            <label for="carMake">العلامة التجارية</label>
                            <input type="text" id="carMake" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="carModel">الطراز</label>
                            <input type="text" id="carModel" required>
                        </div>
                        <div class="form-group">
                            <label for="carYear">السنة</label>
                            <input type="number" id="carYear" min="2015" max="2025" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="carCategory">الفئة</label>
                            <select id="carCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="economy">اقتصادية</option>
                                <option value="standard">عادية</option>
                                <option value="premium">متميزة</option>
                                <option value="luxury">فاخرة</option>
                                <option value="exotic">خارقة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="carType">النوع</label>
                            <select id="carType" required>
                                <option value="">اختر النوع</option>
                                <option value="sedan">سيدان</option>
                                <option value="suv">دفع رباعي</option>
                                <option value="hatchback">هاتشباك</option>
                                <option value="coupe">كوبيه</option>
                                <option value="convertible">مكشوفة</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="carDailyPrice">السعر اليومي (د.إ)</label>
                            <input type="number" id="carDailyPrice" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="carPlateNumber">رقم اللوحة</label>
                            <input type="text" id="carPlateNumber" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="carImages">صور السيارة</label>
                        <input type="file" id="carImages" multiple accept="image/*">
                    </div>
                    <div class="form-group">
                        <label for="carDescription">الوصف</label>
                        <textarea id="carDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('addCarModal')">إلغاء</button>
                <button type="submit" form="addCarForm" class="btn btn-primary">إضافة السيارة</button>
            </div>
        </div>
    </div>

    <!-- Add Location Modal -->
    <div class="modal" id="addLocationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إضافة موقع جديد</h2>
                <button class="close-btn" onclick="closeModal('addLocationModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addLocationForm">
                    <div class="form-group">
                        <label for="locationName">اسم الموقع</label>
                        <input type="text" id="locationName" required>
                    </div>
                    <div class="form-group">
                        <label for="locationAddress">العنوان</label>
                        <input type="text" id="locationAddress" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="locationLat">خط العرض</label>
                            <input type="number" id="locationLat" step="any" required>
                        </div>
                        <div class="form-group">
                            <label for="locationLng">خط الطول</label>
                            <input type="number" id="locationLng" step="any" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="locationType">نوع الموقع</label>
                        <select id="locationType" required>
                            <option value="">اختر النوع</option>
                            <option value="pickup">استلام</option>
                            <option value="dropoff">إرجاع</option>
                            <option value="both">استلام وإرجاع</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('addLocationModal')">إلغاء</button>
                <button type="submit" form="addLocationForm" class="btn btn-primary">إضافة الموقع</button>
            </div>
        </div>
    </div>

    <!-- Add Coupon Modal -->
    <div class="modal" id="addCouponModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إضافة كوبون جديد</h2>
                <button class="close-btn" onclick="closeModal('addCouponModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addCouponForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="couponCode">كود الكوبون</label>
                            <input type="text" id="couponCode" required>
                        </div>
                        <div class="form-group">
                            <label for="couponType">نوع الخصم</label>
                            <select id="couponType" required>
                                <option value="">اختر النوع</option>
                                <option value="percentage">نسبة مئوية</option>
                                <option value="fixed">مبلغ ثابت</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="couponValue">قيمة الخصم</label>
                            <input type="number" id="couponValue" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="couponMinOrder">الحد الأدنى للطلب</label>
                            <input type="number" id="couponMinOrder" step="0.01">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="couponValidFrom">صالح من</label>
                            <input type="datetime-local" id="couponValidFrom" required>
                        </div>
                        <div class="form-group">
                            <label for="couponValidTo">صالح حتى</label>
                            <input type="datetime-local" id="couponValidTo" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('addCouponModal')">إلغاء</button>
                <button type="submit" form="addCouponForm" class="btn btn-primary">إضافة الكوبون</button>
            </div>
        </div>
    </div>

    <!-- Office Modals -->
    <!-- Add Office Modal -->
    <div class="modal" id="addOfficeModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2>إضافة مكتب جديد</h2>
                <button class="close-btn" onclick="closeModal('addOfficeModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addOfficeForm" onsubmit="submitOffice(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="officeCode">رمز المكتب *</label>
                            <input type="text" id="officeCode" name="office_code" required maxlength="10" placeholder="DXB001">
                        </div>
                        <div class="form-group">
                            <label for="officeType">نوع المكتب *</label>
                            <select id="officeType" name="office_type" required>
                                <option value="">اختر النوع</option>
                                <option value="main">مكتب رئيسي</option>
                                <option value="branch">فرع</option>
                                <option value="pickup_point">نقطة استلام</option>
                                <option value="service_center">مركز خدمة</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="officeName">اسم المكتب *</label>
                            <input type="text" id="officeName" name="name" required placeholder="اسم المكتب">
                        </div>
                        <div class="form-group">
                            <label for="officeNameAr">الاسم بالعربية *</label>
                            <input type="text" id="officeNameAr" name="name_ar" required placeholder="الاسم بالعربية">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="officeNameEn">الاسم بالإنجليزية *</label>
                            <input type="text" id="officeNameEn" name="name_en" required placeholder="Name in English">
                        </div>
                        <div class="form-group">
                            <label for="officePhone">رقم الهاتف *</label>
                            <input type="tel" id="officePhone" name="phone" required placeholder="+971-4-555-0000">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="officeEmail">البريد الإلكتروني *</label>
                            <input type="email" id="officeEmail" name="email" required placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="officeWhatsapp">واتساب</label>
                            <input type="tel" id="officeWhatsapp" name="whatsapp" placeholder="+971-50-123-4567">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="officeEmirate">الإمارة *</label>
                            <select id="officeEmirate" name="emirate" required>
                                <option value="">اختر الإمارة</option>
                                <option value="Dubai">دبي</option>
                                <option value="Abu Dhabi">أبوظبي</option>
                                <option value="Sharjah">الشارقة</option>
                                <option value="Ajman">عجمان</option>
                                <option value="Ras Al Khaimah">رأس الخيمة</option>
                                <option value="Fujairah">الفجيرة</option>
                                <option value="Umm Al Quwain">أم القيوين</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="officeCity">المدينة *</label>
                            <input type="text" id="officeCity" name="city" required placeholder="اسم المدينة">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="officeArea">المنطقة</label>
                            <input type="text" id="officeArea" name="area" placeholder="اسم المنطقة">
                        </div>
                        <div class="form-group">
                            <label for="officeCapacity">سعة المكتب</label>
                            <input type="number" id="officeCapacity" name="capacity" min="0" placeholder="عدد السيارات">
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="officeAddress">العنوان *</label>
                        <textarea id="officeAddress" name="address" required rows="2" placeholder="العنوان التفصيلي"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="officeLatitude">خط العرض *</label>
                            <input type="number" id="officeLatitude" name="latitude" step="0.000001" required placeholder="25.1972">
                        </div>
                        <div class="form-group">
                            <label for="officeLongitude">خط الطول *</label>
                            <input type="number" id="officeLongitude" name="longitude" step="0.000001" required placeholder="55.2744">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="isPickupLocation" name="is_pickup_location" checked>
                                    <span>نقطة استلام</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="isReturnLocation" name="is_return_location" checked>
                                    <span>نقطة إرجاع</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="is24Hours" name="is_24_hours">
                                    <span>يعمل 24 ساعة</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="officeDescription">وصف المكتب</label>
                        <textarea id="officeDescription" name="description" rows="3" placeholder="وصف مختصر للمكتب وخدماته"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addOfficeModal')">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ المكتب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Office Details Modal -->
    <div class="modal" id="officeDetailsModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2>تفاصيل المكتب</h2>
                <button class="close-btn" onclick="closeModal('officeDetailsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="officeDetailsContent">
                    <!-- سيتم ملؤه ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Office Modal -->
    <div class="modal" id="editOfficeModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2>تعديل بيانات المكتب</h2>
                <button class="close-btn" onclick="closeModal('editOfficeModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editOfficeForm" onsubmit="updateOffice(event)">
                    <input type="hidden" id="editOfficeId" name="office_id">
                    <!-- نفس الحقول كما في نموذج الإضافة -->
                    <div id="editOfficeFormContent">
                        <!-- سيتم ملؤه ديناميكياً -->
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/app.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/cars.js"></script>
    <script src="js/bookings.js"></script>
    <script src="js/users.js"></script>
    <script src="js/payments.js"></script>
    <script src="js/reviews.js"></script>
    <script src="js/offices.js"></script>
    <script src="js/api.js"></script>
</body>
</html>
