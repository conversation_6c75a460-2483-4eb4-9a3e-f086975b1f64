-- إنشاء جدول profiles لحفظ بيانات المستخدمين
-- يجب تشغيل هذا السكريبت في Supabase SQL Editor

-- أولاً، تحقق من وجود الجدول
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') THEN
        -- إنشاء جدول profiles
        CREATE TABLE profiles (
            id UUID REFERENCES auth.users ON DELETE CASCADE,
            full_name TEXT NOT NULL,
            first_name TEXT,
            last_name TEXT,
            phone TEXT,
            email TEXT,
            date_of_birth DATE,
            nationality TEXT,
            
            -- الوثائق
            emirates_id_front_url TEXT,
            emirates_id_back_url TEXT,
            driving_license_url TEXT,
            passport_url TEXT,
            profile_image_url TEXT,
            license_image_url TEXT,
            
            -- العنوان
            address TEXT,
            address_line1 TEXT,
            address_line2 TEXT,
            city TEXT DEFAULT 'Dubai',
            country TEXT DEFAULT 'UAE',
            emirate TEXT DEFAULT 'Dubai',
            postal_code TEXT,
            
            -- معلومات الطوارئ
            emergency_contact_name TEXT,
            emergency_contact_phone TEXT,
            
            -- حالة التحقق
            is_verified BOOLEAN DEFAULT false,
            is_documents_verified BOOLEAN DEFAULT false,
            is_email_verified BOOLEAN DEFAULT false,
            is_phone_verified BOOLEAN DEFAULT false,
            is_license_verified BOOLEAN DEFAULT false,
            is_profile_complete BOOLEAN DEFAULT false,
            verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected')),
            verification_notes TEXT,
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'banned')),
            
            -- الإعدادات
            preferred_language TEXT DEFAULT 'ar' CHECK (preferred_language IN ('ar', 'en')),
            notification_enabled BOOLEAN DEFAULT true,
            email_notifications BOOLEAN DEFAULT true,
            sms_notifications BOOLEAN DEFAULT true,
            dark_mode BOOLEAN DEFAULT false,
            
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
            PRIMARY KEY (id)
        );

        -- إنشاء فهرس على البريد الإلكتروني
        CREATE INDEX idx_profiles_email ON profiles(email);
        
        -- إنشاء فهرس على حالة المستخدم
        CREATE INDEX idx_profiles_status ON profiles(status);
        
        -- إنشاء trigger لتحديث updated_at تلقائياً
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = timezone('utc'::text, now());
            RETURN NEW;
        END;
        $$ language 'plpgsql';

        CREATE TRIGGER update_profiles_updated_at 
            BEFORE UPDATE ON profiles 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();

        -- إعداد Row Level Security (RLS)
        ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

        -- سماسة للمستخدمين برؤية وتعديل ملفهم الشخصي فقط
        CREATE POLICY "Users can view own profile" 
            ON profiles FOR SELECT 
            USING (auth.uid() = id);

        CREATE POLICY "Users can update own profile" 
            ON profiles FOR UPDATE 
            USING (auth.uid() = id);

        CREATE POLICY "Users can insert own profile" 
            ON profiles FOR INSERT 
            WITH CHECK (auth.uid() = id);

        RAISE NOTICE 'Profiles table created successfully!';
    ELSE
        RAISE NOTICE 'Profiles table already exists.';
    END IF;
END $$;

-- التحقق من إنشاء الجدول
SELECT 'Profiles table setup completed!' as message;
