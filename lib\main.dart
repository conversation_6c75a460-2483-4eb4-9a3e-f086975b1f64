import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:provider/provider.dart';

import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'providers/theme_provider.dart';
import 'screens/splash_screen.dart';
import 'l10n/app_localizations.dart';
import 'constants/app_constants.dart';
import 'services/stripe_service.dart';
import 'routes/app_routes.dart' as routes;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تعيين اتجاه الشاشة - فقط للمنصات المحمولة
  if (!kIsWeb) {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  // تهيئة Supabase
  try {
    print('🔗 Supabase URL: ${AppConstants.supabaseUrl}');
    print(
        '🔑 Supabase Key: ${AppConstants.supabaseAnonKey.substring(0, 20)}...');

    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
    );
    print('✓ تم تهيئة Supabase بنجاح');
  } catch (e) {
    print('⚠️ تحذير: فشل في تهيئة Supabase: $e');
  }

  // تهيئة Stripe - آمنة للويب والمنصات الأخرى
  try {
    await StripeService.init();
    print('✓ تم تهيئة Stripe بنجاح');
  } catch (e) {
    print('⚠️ تحذير: فشل في تهيئة Stripe: $e');
  }

  print('🚀 تشغيل التطبيق...');
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer3<AuthProvider, LanguageProvider, ThemeProvider>(
        builder: (context, authProvider, languageProvider, themeProvider, _) {
          return MaterialApp(
            title: 'Dubai Car Rental',
            debugShowCheckedModeBanner: false,

            // Localization
            locale: languageProvider.locale,
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: AppLocalizations.localizationsDelegates,

            // Theme
            theme: ThemeProvider.lightTheme,
            darkTheme: ThemeProvider.darkTheme,
            themeMode: themeProvider.themeMode,

            // Home
            home: SplashScreen(),

            // Route generator
            onGenerateRoute: routes.AppRoutes.generateRoute,
          );
        },
      ),
    );
  }
}
