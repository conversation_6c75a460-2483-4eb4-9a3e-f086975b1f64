import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final String id;
  final String? fullName;
  final String? firstName;
  final String? lastName;
  final String? phone;
  final String? email;
  final DateTime? dateOfBirth;
  final String? nationality;

  // الوثائق
  final String? emiratesIdFrontUrl;
  final String? emiratesIdBackUrl;
  final String? drivingLicenseUrl;
  final String? passportUrl;
  final String? profileImageUrl;
  final String? licenseImageUrl;

  // العنوان
  final String? address;
  final String? addressLine1;
  final String? addressLine2;
  final String? city;
  final String? country;
  final String? emirate;
  final String? postalCode;

  // معلومات الطوارئ
  final String? emergencyContactName;
  final String? emergencyContactPhone;

  // حالة التحقق
  final bool isVerified;
  final bool isDocumentsVerified;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final bool isLicenseVerified;
  final bool isProfileComplete;
  final String verificationStatus;
  final String? verificationNotes;
  final UserStatus status;

  // الإعدادات
  final String? preferredLanguage;
  final bool notificationEnabled;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool darkMode;

  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    this.fullName,
    this.firstName,
    this.lastName,
    this.phone,
    this.email,
    this.dateOfBirth,
    this.nationality,
    this.emiratesIdFrontUrl,
    this.emiratesIdBackUrl,
    this.drivingLicenseUrl,
    this.passportUrl,
    this.profileImageUrl,
    this.licenseImageUrl,
    this.address,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.country,
    this.emirate,
    this.postalCode,
    this.emergencyContactName,
    this.emergencyContactPhone,
    required this.isVerified,
    required this.isDocumentsVerified,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.isLicenseVerified,
    required this.isProfileComplete,
    required this.verificationStatus,
    this.verificationNotes,
    required this.status,
    this.preferredLanguage,
    required this.notificationEnabled,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.darkMode,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory constructor to create from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      fullName: json['full_name'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      nationality: json['nationality'] as String?,
      emiratesIdFrontUrl: json['emirates_id_front_url'] as String?,
      emiratesIdBackUrl: json['emirates_id_back_url'] as String?,
      drivingLicenseUrl: json['driving_license_url'] as String?,
      passportUrl: json['passport_url'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      licenseImageUrl: json['license_image_url'] as String?,
      address: json['address'] as String?,
      addressLine1: json['address_line1'] as String?,
      addressLine2: json['address_line2'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
      emirate: json['emirate'] as String?,
      postalCode: json['postal_code'] as String?,
      emergencyContactName: json['emergency_contact_name'] as String?,
      emergencyContactPhone: json['emergency_contact_phone'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
      isDocumentsVerified: json['is_documents_verified'] as bool? ?? false,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      isPhoneVerified: json['is_phone_verified'] as bool? ?? false,
      isLicenseVerified: json['is_license_verified'] as bool? ?? false,
      isProfileComplete: json['is_profile_complete'] as bool? ?? false,
      verificationStatus: json['verification_status'] as String? ?? 'pending',
      verificationNotes: json['verification_notes'] as String?,
      status: UserStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String? ?? 'active'),
        orElse: () => UserStatus.active,
      ),
      preferredLanguage: json['preferred_language'] as String?,
      notificationEnabled: json['notification_enabled'] as bool? ?? true,
      emailNotifications: json['email_notifications'] as bool? ?? true,
      smsNotifications: json['sms_notifications'] as bool? ?? true,
      darkMode: json['dark_mode'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'email': email,
      'date_of_birth': dateOfBirth?.toIso8601String().split('T')[0],
      'nationality': nationality,
      'emirates_id_front_url': emiratesIdFrontUrl,
      'emirates_id_back_url': emiratesIdBackUrl,
      'driving_license_url': drivingLicenseUrl,
      'passport_url': passportUrl,
      'profile_image_url': profileImageUrl,
      'license_image_url': licenseImageUrl,
      'address': address,
      'address_line1': addressLine1,
      'address_line2': addressLine2,
      'city': city,
      'country': country,
      'emirate': emirate,
      'postal_code': postalCode,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'is_verified': isVerified,
      'is_documents_verified': isDocumentsVerified,
      'is_email_verified': isEmailVerified,
      'is_phone_verified': isPhoneVerified,
      'is_license_verified': isLicenseVerified,
      'is_profile_complete': isProfileComplete,
      'verification_status': verificationStatus,
      'verification_notes': verificationNotes,
      'status': status.name,
      'preferred_language': preferredLanguage,
      'notification_enabled': notificationEnabled,
      'email_notifications': emailNotifications,
      'sms_notifications': smsNotifications,
      'dark_mode': darkMode,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Factory constructor for creating a new user
  factory UserModel.create({
    required String id,
    required String email,
    required String fullName,
    String? profileImageUrl,
  }) {
    final now = DateTime.now().toUtc();
    return UserModel(
      id: id,
      email: email,
      fullName: fullName,
      profileImageUrl: profileImageUrl,
      city: 'Dubai',
      country: 'UAE',
      emirate: 'Dubai',
      isVerified: false,
      isDocumentsVerified: false,
      isEmailVerified: false,
      isPhoneVerified: false,
      isLicenseVerified: false,
      isProfileComplete: false,
      verificationStatus: 'pending',
      status: UserStatus.active,
      preferredLanguage: 'ar',
      notificationEnabled: true,
      emailNotifications: true,
      smsNotifications: true,
      darkMode: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Copy with method for updates
  UserModel copyWith({
    String? fullName,
    String? firstName,
    String? lastName,
    String? phone,
    String? email,
    DateTime? dateOfBirth,
    String? nationality,
    String? emiratesIdFrontUrl,
    String? emiratesIdBackUrl,
    String? drivingLicenseUrl,
    String? passportUrl,
    String? profileImageUrl,
    String? licenseImageUrl,
    String? address,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? country,
    String? emirate,
    String? postalCode,
    String? emergencyContactName,
    String? emergencyContactPhone,
    bool? isVerified,
    bool? isDocumentsVerified,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isLicenseVerified,
    bool? isProfileComplete,
    String? verificationStatus,
    String? verificationNotes,
    UserStatus? status,
    String? preferredLanguage,
    bool? notificationEnabled,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? darkMode,
  }) {
    return UserModel(
      id: id,
      fullName: fullName ?? this.fullName,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      nationality: nationality ?? this.nationality,
      emiratesIdFrontUrl: emiratesIdFrontUrl ?? this.emiratesIdFrontUrl,
      emiratesIdBackUrl: emiratesIdBackUrl ?? this.emiratesIdBackUrl,
      drivingLicenseUrl: drivingLicenseUrl ?? this.drivingLicenseUrl,
      passportUrl: passportUrl ?? this.passportUrl,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      licenseImageUrl: licenseImageUrl ?? this.licenseImageUrl,
      address: address ?? this.address,
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      city: city ?? this.city,
      country: country ?? this.country,
      emirate: emirate ?? this.emirate,
      postalCode: postalCode ?? this.postalCode,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      isVerified: isVerified ?? this.isVerified,
      isDocumentsVerified: isDocumentsVerified ?? this.isDocumentsVerified,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isLicenseVerified: isLicenseVerified ?? this.isLicenseVerified,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      verificationNotes: verificationNotes ?? this.verificationNotes,
      status: status ?? this.status,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      notificationEnabled: notificationEnabled ?? this.notificationEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      darkMode: darkMode ?? this.darkMode,
      createdAt: createdAt,
      updatedAt: DateTime.now().toUtc(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        fullName,
        firstName,
        lastName,
        phone,
        email,
        dateOfBirth,
        nationality,
        emiratesIdFrontUrl,
        emiratesIdBackUrl,
        drivingLicenseUrl,
        passportUrl,
        profileImageUrl,
        licenseImageUrl,
        address,
        addressLine1,
        addressLine2,
        city,
        country,
        emirate,
        postalCode,
        emergencyContactName,
        emergencyContactPhone,
        isVerified,
        isDocumentsVerified,
        isEmailVerified,
        isPhoneVerified,
        isLicenseVerified,
        isProfileComplete,
        verificationStatus,
        verificationNotes,
        status,
        preferredLanguage,
        notificationEnabled,
        emailNotifications,
        smsNotifications,
        darkMode,
        createdAt,
        updatedAt,
      ];
}

enum UserStatus {
  active,
  inactive,
  suspended,
  banned,
}
